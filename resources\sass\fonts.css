@font-face {
  font-family: "Graphik";
  src: url(../css/fonts/Graphik-Light.eot);
  src: url(../css/fonts/Graphik-Light.woff) format("woff");
  src: url(../css/fonts/Graphik-Light.ttf) format("truetype");
  font-weight: 300;
}
@font-face {
  font-family: "Graphik";
  src: url(../css/fonts/Graphik-Regular.eot);
  src: url(../css/fonts/Graphik-Regular.woff) format("woff");
  src: url(../css/fonts/Graphik-Regular.ttf) format("truetype");
  font-weight: 400;
}
@font-face {
  font-family: "Graphik";
  src: url(../css/fonts/Graphik-Medium.eot);
  src: url(../css/fonts/Graphik-Medium.woff) format("woff");
  src: url(../css/fonts/Graphik-Medium.ttf) format("truetype");
  font-weight: 500;
}
@font-face {
  font-family: "Graphik";
  src: url(../css/fonts/Graphik-Semibold.eot);
  src: url(../css/fonts/Graphik-Semibold.woff) format("woff");
  src: url(../css/fonts/Graphik-Semibold.ttf) format("truetype");
  font-weight: 600;
}
@font-face {
  font-family: "Graphik";
  src: url(../css/fonts/Graphik-Bold.eot);
  src: url(../css/fonts/Graphik-Bold.woff) format("woff");
  src: url(../css/fonts/Graphik-Bold.ttf) format("truetype");
  font-weight: 700;
}/*# sourceMappingURL=fonts.css.map */