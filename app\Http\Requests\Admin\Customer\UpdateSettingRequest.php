<?php

namespace App\Http\Requests\Admin\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateSettingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'currency_id'             => ['nullable', 'number'],
            'timezone'                => ['nullable', 'string'],
            'date_format'             => ['nullable', 'string'],
            'week_start'              => ['nullable', 'string'],
            'seo_title'               => ['nullable', 'string', 'max:255'],
            'seo_description'         => ['nullable', 'string', 'max:255'],
            'smtp_from_name'          => ['nullable', 'string', 'max:255'],
            'smtp_from_email'         => ['nullable', 'string', 'max:255'],
            'logo'                    => ['nullable', 'string', 'max:255'],
        ];
        return $rules;
    }

    protected function failedValidation(Validator $validator)
    {
        // dd($validator->errors());
        throw new HttpResponseException(response()->json($validator->errors(), 422));
    }
}
