<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->decimal('supplier_price')->after('custom_product')->default(0);
            $table->unsignedBigInteger('supplier_id')->after('supplier_price')->nullable();
            $table->foreign('supplier_id', 'FK_products_suppliers_foreign_key')->references('id')->on('suppliers');            
            $table->unsignedBigInteger('wp_id')->after('supplier_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['supplier_id']);
            $table->bigInteger('supplier_id')->unsigned()->nullable(true)->change();
        });
    }
};
