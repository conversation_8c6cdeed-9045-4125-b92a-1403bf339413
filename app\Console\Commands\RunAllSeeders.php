<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class RunAllSeeders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:seed {--fresh} {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed all tables with dummy data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        if ($this->option('fresh')) {
            $this->call('migrate:fresh');
            $this->call('db:seed', [
                '--class' => 'DatabaseSeeder'
            ]);
            $this->call('db:seed', [
                '--class' => 'AllDataSeeder'
            ]);
        }
        if ($this->option('all')) {
            $this->call('db:seed', [
                '--class' => 'AllDataSeeder'
            ]);
        }
    }
}
