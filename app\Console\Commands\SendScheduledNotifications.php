<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Studio;
use App\Models\User;
use App\Models\AdminNotification as AdminNotificationModel;
use App\Notifications\AdminNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;

class SendScheduledNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-scheduled-notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $notificationsToBeSent = AdminNotificationModel::where('published_at', Carbon::today())->get();
        foreach ($notificationsToBeSent as $notification) {
            if ($notification->customer_id) {
                $customer = Customer::find($notification->customer_id)->owner;
                $customer->notify(new AdminNotification($notification));
            } else {
                $customers = User::where('is_active', true)->whereHas('roles', function ($query) {
                    $query->where('name', 'customer');
                })->get();
                Notification::send($customers, new AdminNotification($notification));
            }
        }
    }
}
