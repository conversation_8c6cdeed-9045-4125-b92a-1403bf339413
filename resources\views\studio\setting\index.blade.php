@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('Settings') }}</h3>
            <a href="{{ url()->previous() }}" class="back-link">← Back</a>
        </div>
    </div>
    <form method="POST" action="{{route('studio.setting.update', $customer)}}" enctype="multipart/form-data">
        @csrf
        @method('put')
        <div class="main-subtitle no-border-btm">
            <h5>{{ __('general settings') }}</h5>
        </div>
        @include('partials.forms.admin-settings.select', [
            'field' => 'name',
            'field_name' => 'timezone',
            'field_label' => 'TIME ZONE *',
            'field_value' => $settings->timezone ?? 'timezone-1',
            'values' => array(
                (object) array(
                    'id' => 'US and Canada',
                    'name' => 'US and Canada',
                    'group' => array(
                        (object) array(
                            'id' => 'America/New_York',
                            'name' => 'Eastern Standard Time (EST) / Eastern Daylight Time (EDT)',
                        ),
                        (object) array(
                            'id' => 'America/Chicago',
                            'name' => 'Central Standard Time (CST) / Central Daylight Time (CDT)',
                        ),
                        (object) array(
                            'id' => 'America/Denver',
                            'name' => 'Mountain Standard Time (MST) / Mountain Daylight Time (MDT)',
                        ),
                        (object) array(
                            'id' => 'America/Los_Angeles',
                            'name' => 'Pacific Standard Time (PST) / Pacific Daylight Time (PDT)',
                        ),
                        (object) array(
                            'id' => 'America/Anchorage',
                            'name' => 'Alaska Standard Time (AKST) / Alaska Daylight Time (AKDT)',
                        ),
                        (object) array(
                            'id' => 'Pacific/Honolulu',
                            'name' => 'Hawaii-Aleutian Standard Time (HST)',
                        ),
                        (object) array(
                            'id' => 'America/Halifax',
                            'name' => 'Atlantic Standard Time (AST) / Atlantic Daylight Time (ADT)',
                        ),
                        (object) array(
                            'id' => 'America/St_Johns',
                            'name' => 'Newfoundland Standard Time (NST) / Newfoundland Daylight Time (NDT)',
                        )
                    )
                ),
                (object) array(
                    'id' => 'Europe',
                    'name' => 'Europe',
                    'group' => array(
                        (object) array(
                            'id' => 'Europe/London',
                            'name' => 'Greenwich Mean Time (GMT) / British Summer Time (BST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Berlin',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Paris',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Madrid',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Rome',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Amsterdam',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Zurich',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Stockholm',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Warsaw',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Athens',
                            'name' => 'Eastern European Time (EET) / Eastern European Summer Time (EEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Istanbul',
                            'name' => 'Turkey Time (TRT)',
                        ),
                        (object) array(
                            'id' => 'Europe/Helsinki',
                            'name' => 'Eastern European Time (EET) / Eastern European Summer Time (EEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Moscow',
                            'name' => 'Moscow Standard Time (MSK)',
                        ),
                        (object) array(
                            'id' => 'Europe/Dublin',
                            'name' => 'Greenwich Mean Time (GMT) / Irish Standard Time (IST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Bucharest',
                            'name' => 'Eastern European Time (EET) / Eastern European Summer Time (EEST)',
                        ),
                        (object) array(
                            'id' => 'Europe/Belgrade',
                            'name' => 'Central European Time (CET) / Central European Summer Time (CEST)',
                        )
                    )
                ),
                (object) array(
                    'id' => 'Australia',
                    'name' => 'Australia',
                    'group' => array(
                        (object) array(
                            'id' => 'Australia/Sydney',
                            'name' => 'Australian Eastern Standard Time (AEST) / Australian Eastern Daylight Time (AEDT)',
                        ),
                        (object) array(
                            'id' => 'Australia/Melbourne',
                            'name' => 'Australian Eastern Standard Time (AEST) / Australian Eastern Daylight Time (AEDT)',
                        ),
                        (object) array(
                            'id' => 'Australia/Brisbane',
                            'name' => 'Australian Eastern Standard Time (AEST) (No DST)',
                        ),
                        (object) array(
                            'id' => 'Australia/Adelaide',
                            'name' => 'Australian Central Standard Time (ACST) / Australian Central Daylight Time (ACDT)',
                        ),
                        (object) array(
                            'id' => 'Australia/Darwin',
                            'name' => 'Australian Central Standard Time (ACST) (No DST)',
                        ),
                        (object) array(
                            'id' => 'Australia/Perth',
                            'name' => 'Australian Western Standard Time (AWST) (No DST)',
                        ),
                        (object) array(
                            'id' => 'Australia/Hobart',
                            'name' => 'Australian Eastern Standard Time (AEST) / Australian Eastern Daylight Time (AEDT)',
                        ),
                        (object) array(
                            'id' => 'Australia/Canberra',
                            'name' => 'Australian Eastern Standard Time (AEST) / Australian Eastern Daylight Time (AEDT)',
                        ),
                        (object) array(
                            'id' => 'Australia/Brisbane',
                            'name' => 'Australian Eastern Standard Time (AEST) (No DST)',
                        )
                    )
                ),
            ),
            'option_key' => 'id',
            'option_label' => 'name',
        ])

        @include('partials.forms.admin-settings.select', [
            'field' => 'name',
            'field_name' => 'date_format',
            'field_label' => 'DATE FORMAT *',
            'field_value' => $settings->date_format ?? 'F j, Y',
            'values' => array(
                (object) array(
                    'id' => 'F j, Y',
                    'name' => 'July 7, 2023',
                ),
                (object) array(
                    'id' => 'j F Y',
                    'name' => '7 July 2023',
                ),
                (object) array(
                    'id' => 'Y F j',
                    'name' => '2023 July 7',
                ),
                (object) array(
                    'id' => 'd.m.Y.',
                    'name' => '22.12.2023.',
                ),
                (object) array(
                    'id' => 'm.d.Y.',
                    'name' => '12.22.2023.',
                ),
            ),
            'option_key' => 'id',
            'option_label' => 'name',
        ])

        @include('partials.forms.admin-settings.select', [
            'field' => 'name',
            'field_name' => 'time_format',
            'field_label' => 'TIME FORMAT *',
            'field_value' => $settings->time_format ?? 'g:i A',
            'values' => array(
                (object) array(
                    'id' => 'g:i A',
                    'name' => 'h:m (am/pm)',
                ),
                (object) array(
                    'id' => 'H:i',
                    'name' => 'hh:mm',
                ),
            ),
            'option_key' => 'id',
            'option_label' => 'name',
        ])

        @include('partials.forms.admin-settings.select', [
            'field' => 'name',
            'field_name' => 'week_start',
            'field_label' => 'WEEK STARTS ON *',
            'field_value' => $settings->week_start ?? 'sunday',
            'values' => array(
                (object) array(
                    'id' => 'sunday',
                    'name' => 'Sunday',
                ),
                (object) array(
                    'id' => 'monday',
                    'name' => 'Monday',
                ),
            ),
            'option_key' => 'id',
            'option_label' => 'name',
        ])

        @include('partials.forms.admin-settings.select', [
            'field' => 'name',
            'field_name' => 'currency',
            'field_label' => 'CURRENCY *',
            'field_value' => $settings->currency_id,
            'values' => array(
                (object) array(
                    'id' => '1',
                    'name' => 'USD',
                ),
                (object) array(
                    'id' => '2',
                    'name' => 'EUR',
                ),
            ),
            'option_key' => 'id',
            'option_label' => 'name',
        ])

        <div class="buttons-wrapper">
            <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
            <a type="button" href="{{route('studio.setting')}}"
                class="btn cancel-btn">{{ __('CANCEL') }}</a>
        </div>
    </form>
@endsection

@section('js-links')
    <script type="module">
        $('input.check-clear-field').click(function(){
            let targetField = $('#' + $(this).attr('data-clear-field'));

            if( targetField.length ) {
                if ( ! $(this).prop('checked') ) {
                    targetField.val('');
                    targetField.removeAttr('value');
                    targetField.removeAttr('required');
                } else {
                    targetField.prop('required', true);
                }
            }
        });
    </script>
@endsection
