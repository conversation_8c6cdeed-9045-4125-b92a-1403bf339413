@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('edit lease') }}</h3>
            <a href="{{ route('admin.customers.dashboard', $customer) }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.leases.update', ['customer' => $customer, 'lease' => $lease]) }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('company selection') }}</h5>

            @include('partials.forms.select', [
              'field_name' => 'company_id',
              'field_label' => null,
              'values' => $companies,
              'field' => 'state',
              'option_key' => 'id',
              'option_label' => 'name',
              'field_value' => old('company_id', $lease->company_id),
              'include_empty' => true,
          ])

            <h5 class="form-section-title">{{ __('studio selection') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'studio_exists',
                'field_label' => 'Radio group location',
                'values' => ['addNewStudio' => ['text' => 'Add a new studio', 'value' => '0'], 'assignExclusivity' => ['text' => 'Assign exclusivity to an existing studio', 'value' => '1']],
                'field' => 'status',
                'required' => 'required',
                'checked' => old('studio_exists', $lease->is_active)
            ])

            <br>

            @include('partials.forms.select', [
               'field_name' => 'studio_id',
               'field_label' => null,
               'values' => $studios,
               'field' => 'state',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('studio_id', $lease->studio_id),
               'include_empty' => true,
               'field_id' => 'license_select'
           ])

            <h5 class="form-section-title">{{ __('studio info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'studio[name]',
                'field_label' => 'NAME *',
                'field_type' => 'text',
                'field_value' => old('studio[name]', $lease->studio->name),
            ])

            @include('partials.forms.input', [
               'field_name' => 'studio[owner_first_name]',
               'field_label' => 'OWNER - FIRST NAME *',
               'field_type' => 'text',
               'field_value' => old('studio[owner_first_name]', $lease->studio->owner_first_name),
           ])

            @include('partials.forms.input', [
               'field_name' => 'studio[owner_last_name]',
               'field_label' => 'OWNER - LAST NAME *',
               'field_type' => 'text',
               'field_value' => old('studio[owner_last_name]', $lease->studio->owner_last_name),
           ])

            @include('partials.forms.input', [
                'field_name' => 'studio[email]',
                'field_label' => 'EMAIL ADDRESS *',
                'field_type' => 'email',
                'field_value' => old('studio[email]', $lease->studio->email),
            ])

            @include('partials.forms.input', [
               'field_name' => 'studio[phone]',
               'field_label' => 'PHONE # *',
               'field_type' => 'text',
               'field_value' => old('studio[phone]', $lease->studio->phone),
           ])

           <h5 class="form-section-title">address info</h5>

            @include('partials.forms.input', [
               'field_name' => 'studio[address]',
               'field_label' => 'ADDRESS',
               'field_type' => 'text',
               'field_value' => old('studio[address]', $lease->studio->address),
           ])

            @include('partials.forms.input', [
               'field_name' => 'studio[city]',
               'field_label' => 'CITY',
               'field_type' => 'text',
               'field_value' => old('studio[city]', $lease->studio->city),
           ])

           @include('partials.forms.input', [
               'field_name' => 'studio[zip]',
               'field_label' => 'ZIP CODE',
               'field_type' => 'text',
               'field_value' => old('studio[zip]', $lease->studio->zip),
           ])

            @include('partials.forms.select', [
               'field_name' => 'studio[state_id]',
               'field_label' => 'STATE',
               'values' => $states,
               'field' => 'state',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('state_id', $lease->studio->state_id),
               'include_empty' => true
           ])

            <h5 class="form-section-title">{{ __('LEASE info') }}</h5>

            @include('partials.forms.select', [
               'field_name' => 'machine_id',
               'field_label' => 'MACHINE *',
               'values' => $machines,
               'field' => 'machine',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('machine_id', $lease->machine_id),
               'include_empty' => true,
            ])

            @include('partials.forms.input', [
                   'field_name' => 'machine_price',
                   'field_label' => 'PRICE (PER MACHINE) *',
                   'field_type' => 'text',
                   'field_value' => old('machine_price', round($lease->converted_price, 2)),
                   'currency' => true,
                   'input_field_class' => 'decimal-field'
            ])

            @include('partials.forms.input', [
                   'field_name' => 'machine_quantity',
                   'field_label' => 'QTY *',
                   'field_type' => 'text',
                   'field_value' => old('machine_quantity', $lease->machine_quantity),
                   'input_field_class' => 'integer-field'
            ])

            @include('partials.forms.input', [
                   'field_name' => 'duration',
                   'field_label' => 'DURATION *',
                   'field_type' => 'text',
                   'field_value' => old('duration', $lease->duration),
                   'input_field_class' => 'integer-field',

            ])

            @include('partials.forms.input',  [
                'field_name' => 'starting_date',
                'field_label' => 'STARTING DATE *',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'field_value' => old('starting_date', $lease->starting_date),
                'input_field_class' => 'date-field'
            ])

            <h5 class="form-section-title" id="deposit_label">{{ __('deposit (optional)') }}</h5>

            @include('partials.forms.input', [
               'field_name' => 'deposit_amount',
               'field_label' => 'AMOUNT',
               'field_type' => 'text',
               'field_value' => old('deposit_amount', round($lease->converted_deposit, 2)),
               'currency' => true,
               'input_field_class' => 'decimal-field'
           ])

            @include('partials.forms.input',  [
                'field_name' => 'deposit_date',
                'field_label' => 'DATE',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'field_value' => old('deposit_date', $lease->deposit_date),
                'input_field_class' => 'date-field'
            ])

            <div class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button" href="{{route('admin.leases.edit', ['customer' => $customer, 'lease' => $lease])}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'Lease'}}{{$lease->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>

        </form>
    </div>
@endsection

@include('partials.modals.delete', [
        'type' => 'Lease',
        'id' => $lease->id,
        'route' => route('admin.leases.destroy', ['customer' => $customer, 'lease' => $lease]),
        'title' => $lease->name,
    ])

<script type="module">
    document.addEventListener('DOMContentLoaded', function () {

        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        let clearInputsOnSwitch = true; // Flag to control when to clear inputs

        $('input[name="studio_exists"]').change(function(){
            clearInputsOnSwitch = true;
            if ($('#assignExclusivity').is(':checked')) {
                $('#license_select').show();
                disableStudioInputs()
            } else {
                $('#studio_id').val(null).trigger('change');
                $('#license_select').hide();
                clearStudioInputs();
                enableStudioInputs();
                clearInputsOnSwitch = false;
            }
        });

        // Trigger change event on page load to handle pre-selected option
        $('input[name="studio_exists"]:checked').trigger('change');

        $('#studio_id').change(function() {
            let studioId = $(this).val();

            if (studioId) {
                let url = '{{ route('admin.studio.get-info', ':studioId') }}';
                url = url.replace(':studioId', studioId);
                $.ajax({
                    url: url,
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            $('input[name="studio[name]"]').val(response.data.name);
                            $('input[name="studio[owner_first_name]"]').val(response.data.owner_first_name);
                            $('input[name="studio[owner_last_name]"]').val(response.data.owner_last_name);
                            $('input[name="studio[email]"]').val(response.data.email);
                            $('input[name="studio[phone]"]').val(response.data.phone);
                            $('input[name="studio[address]"]').val(response.data.address);
                            $('input[name="studio[city]"]').val(response.data.city);
                            $('select[name="studio[state_id]"]').val(response.data.state_id).trigger('change');
                            $('input[name="studio[zip]"]').val(response.data.zip);
                            disableStudioInputs();
                        }
                    },
                    error: function() {
                        flasherJS.error('', 'Error occurred while loading data.');
                    }
                });
            } else {
                clearStudioInputs();
                enableStudioInputs();
            }
        });

        function clearStudioInputs() {
            if (clearInputsOnSwitch && !$('input[name="studio[name]"]').val()) {
                $('input[name="studio[name]"]').val('');
                $('input[name="studio[owner_first_name]"]').val('');
                $('input[name="studio[owner_last_name]"]').val('');
                $('input[name="studio[email]"]').val('');
                $('input[name="studio[phone]"]').val('');
                $('input[name="studio[address]"]').val('');
                $('input[name="studio[city]"]').val('');
                $('select[name="studio[state_id]"]').val('').trigger('change');
                $('input[name="studio[zip]"]').val('');
            }
        }

        function disableStudioInputs() {
            $('input[name="studio[name]"]').prop('disabled', true);
            $('input[name="studio[owner_first_name]"]').prop('disabled', true);
            $('input[name="studio[owner_last_name]"]').prop('disabled', true);
            $('input[name="studio[email]"]').prop('disabled', true);
            $('input[name="studio[phone]"]').prop('disabled', true);
            $('input[name="studio[address]"]').prop('disabled', true);
            $('input[name="studio[city]"]').prop('disabled', true);
            $('select[name="studio[state_id]"]').prop('disabled', true);
            $('input[name="studio[zip]"]').prop('disabled', true);
        }

        function enableStudioInputs() {
            $('input[name="studio[name]"]').prop('disabled', false);
            $('input[name="studio[owner_first_name]"]').prop('disabled', false);
            $('input[name="studio[owner_last_name]"]').prop('disabled', false);
            $('input[name="studio[email]"]').prop('disabled', false);
            $('input[name="studio[phone]"]').prop('disabled', false);
            $('input[name="studio[address]"]').prop('disabled', false);
            $('input[name="studio[city]"]').prop('disabled', false);
            $('select[name="studio[state_id]"]').prop('disabled', false);
            $('input[name="studio[zip]"]').prop('disabled', false);
        }

        function toggleLeaseFields() {
            let depositAmount = $('#deposit_amount');
            let depositDate = $('#deposit_date');
            let duration = $('#duration');

            if ($('input[name="type"]:checked').val() === 'exclusivity') {
                depositAmount.closest('.form-group').hide();
                depositAmount.val('');
                depositDate.closest('.form-group').hide();
                depositDate.val('');
                duration.closest('.form-group').hide();
                duration.val('');
                $('#deposit_label').hide();
            } else {
                depositAmount.closest('.form-group').show();
                depositDate.closest('.form-group').show();
                duration.closest('.form-group').show();
                $('#deposit_label').show();
            }
        }

        $('input[name="type"]').change(function() {
            toggleLeaseFields();
        });

        toggleLeaseFields();
    });
</script>
