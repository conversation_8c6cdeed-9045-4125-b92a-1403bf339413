<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('license_files', function (Blueprint $table) {
            $table->string('original_name')->after('file_path');
        });
    }

    public function down()
    {
        Schema::table('license_files', function (Blueprint $table) {
            $table->dropColumn('original_name');
        });
    }
};
