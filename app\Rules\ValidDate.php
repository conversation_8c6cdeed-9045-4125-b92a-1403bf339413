<?php

namespace App\Rules;

use App\Helpers\Constants;
use App\Models\License;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidDate implements ValidationRule
{

    private $license;

    public function __construct($license)
    {
        $this->license = $license;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (Carbon::parse($value) < Carbon::now() && $value != $this->license->starting_date) {
            $fail('The :attribute must be bigger than the current date.');
        }

        if ($value > $this->license->payments->sortBy('payment_date')->first()->payment_date
            && $this->license->starting_date !== $value
        ) {
            $fail('The :attribute cant be changed, payment has already started.');
        }
    }
}
