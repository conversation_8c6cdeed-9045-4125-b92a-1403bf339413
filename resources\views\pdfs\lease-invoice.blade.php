<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>

    <style>
        body {
            font-family: Graphik, sans-serif;
            font-size: 15px;
            margin-top: 20px;
            margin-left: 30px;
            margin-right: 30px;
        }
        .header-table {
            width: 100%;
            border-collapse: collapse;
        }
        .header-table td, .items-table th, .items-table td, .note-table td {
            padding: 8px;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .invoice-table td {
            padding: 10px;
            vertical-align: middle;
        }
        .logo-cell {
            width: 20%;
            text-align: center;
        }
        .logo-cell img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-family: Arial, sans-serif;
        }

        .data-table th, .data-table td {
            font-weight: normal;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #F0F0F0;
            border-top: 1px solid #F0F0F0;
        }

        .data-table th {
            font-size: 12px;
            color: #000000;
            padding: 20px;
        }

        .data-table td {
            font-size: 14px;
            color: #000000;
            padding: 20px;
        }

        .data-table tbody tr td[colspan="6"] {
            font-size: 16px;
            /*padding: 20px;*/
            background-color: #f9f9f9;
        }

        /*.data-table th:nth-child(1), .data-table td:nth-child(1) {*/
        /*    width: 250px;*/
        /*}*/

        /*.data-table th:nth-child(2), .data-table td:nth-child(2) {*/
        /*    width: auto;*/
        /*}*/

        /*.data-table th:nth-child(3), .data-table td:nth-child(3) {*/
        /*    width: auto;*/
        /*}*/

        /*.data-table th:nth-child(4), .data-table td:nth-child(4) {*/
        /*    width: auto;*/
        /*}*/

        .text-right {
            text-align: right;
        }
        .terms {
            font-size: 12px;
            margin-top: 20px;
        }
        .light-line {
            width: 100%;
            border-bottom: 1px solid #F0F0F0;
        }
        .company-table {
            margin-bottom: 40px;
        }
    </style>
</head>
<body>

<table class="header-table">
    <tr>
        <td class="logo-cell">
            <img src="{{$company->image?->path ? URL::asset('/storage/' . $company->image->path) : URL::asset('/img_placeholder.svg') }}" alt="Company Logo">
{{--            <img src="https://app.lagreebooking.com/booking-logo.png" alt="Company Logo">--}}
        </td>
        <td class="info-cell">
            @if($company)
            <strong>{{ $company->name }}</strong><br>
            <span>{{ $company->address }}</span><br>
            <span>{{ $company->phone }}</span>
            @endif
        </td>
        <td style="text-align:right;">
            <strong style="font-size: 25px">Invoice #: {{$invoice->formatted_number}}</strong><br>
            Issued Date: {{\Carbon\Carbon::now()->format('m/d/Y')}}<br>
            Due Date: {{\Carbon\Carbon::parse($invoice->payment_date)->format('m/d/Y')}}
        </td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 30px">
    <tr>
        <td></td>
    </tr>
</table>

<table class="company-table">
    <tr>
        <td>
            <strong style="font-size: 20px">{{ $studio->name }}</strong><br>
            <span>{{ $studio->address }}</span><br>
            <span>{{ $studio->phone }}</span>
        </td>
        <td></td>
    </tr>
</table>

<table class="data-table">
    <thead>
    <tr>
        <th>Machine Name</th>
        <th>Type</th>
        <th>Duration Left</th>
        <th>Quantity</th>
        <th>Monthly Price</th>
        <th>Total</th>
    </tr>
    </thead>
    <tbody>

    <tr>
        <td>{{$payment->lease->machine->name}}</td>
        <td>Lease</td>
        <td>{{($duration != 0) ? $duration : 'Last'}} {{($duration > 1) ? 'months' : 'month'}}</td>
        <td>{{$payment->lease->machine_quantity}}</td>
        <td>{{$currencySymbol}}{{number_format($leasePrice, 2)}}</td>
        <td>{{$currencySymbol}}{{number_format($leaseMonthlyPrice, 2)}}</td>
    </tr>
    </tbody>
</table>

<table class="invoice-table">
    <tr>
        <td colspan="4"></td>
        <td class="text-right">Net Total:</td>
        <td class="text-right">{{$currencySymbol}}{{number_format($leaseMonthlyPrice, 2)}}</td>
    </tr>
    <tr>
        <td colspan="4"></td>
        <td class="text-right"><strong>Grand Total:</strong></td>
        <td class="text-right"><strong>{{$currencySymbol}}{{number_format($leaseMonthlyPrice, 2)}}</strong></td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 30px">
    <tr>
        <td></td>
    </tr>
</table>

<table class="invoice-table">
    <tr>
        <td class="header">Description</td>
    </tr>
    <tr>
        <td>Payment on Machine Lease: {{$payment->lease->machine->name}}</td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 30px">
    <tr>
        <td></td>
    </tr>
</table>

<table class="invoice-table">
    <tr>
        <td class="header">Terms & Conditions</td>
    </tr>
    <tr>
        <td class="terms">
            Unless otherwise agreed in writing by the supplier, all invoices are payable immediately upon receipt. This is a payment due on the License purchase. This payment will be credited toward the license. Please refer to the license purchase agreement.
        </td>
    </tr>
</table>

</body>
</html>
