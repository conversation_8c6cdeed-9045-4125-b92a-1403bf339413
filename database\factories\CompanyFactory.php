<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Company>
 */
class CompanyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'address_id' => fake()->randomElement(Address::pluck('id')),
            'name' => fake()->name,
            'tax_number' => fake()->numberBetween(),
            'logo' => fake()->name,
            'phone' => fake()->phoneNumber,
            'email' => fake()->email,
            'website' => fake()->word,
        ];
    }
}
