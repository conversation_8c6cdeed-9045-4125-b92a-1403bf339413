<?php

namespace App\Console\Commands;

use App\Models\Currency;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class FetchCurrencyRates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetch-currency-rates';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $response = Http::get("https://api.currencyfreaks.com/v2.0/rates/latest", [
            'apikey' => config('currency-freaks.api_key'),
            'symbols' => 'EUR'
        ]);

        if ($response->successful()) {
           $data = $response->json();
           Currency::where('code', 'EUR')->update(['rate' => $data['rates']['EUR']]);
        }
    }
}
