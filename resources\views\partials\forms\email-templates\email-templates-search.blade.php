@if(count($emailTemplates) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-medium">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="number" class="sortable-list-header" data-sort="number">{{ __('Invoice') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="payment.payment_amount" class="sortable-list-header" data-sort="payment.payment_amount">{{ __('Amount') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="payment.payment_date" class="sortable-list-header" data-sort="payment.payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    {{--    <form id="searchForm" method="post">--}}
    {{--        @csrf--}}

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">

        @foreach($emailTemplates as $template)

            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <p class="my-0">{{$template->name}}</p>

{{--                <div class="round-button-dropdown">--}}
{{--                    <button class="dropbtn">--}}
{{--                        <i class="fa fa-ellipsis-h"></i>--}}
{{--                    </button>--}}
{{--                    <div class="dropdown-content">--}}
{{--                        <a href="{{route('admin.download-invoice', ['customer' => $customer, 'invoice' => $invoice])}}">Download Invoice</a>--}}
{{--                        <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$invoice->id}}">{{ __('Email Invoice') }}</a>--}}
{{--                    </div>--}}
{{--                </div>--}}
            </div>

        @endforeach
        <div id="paginate">
            @if($emailTemplates)
                <div class="">
                    {!! $emailTemplates->links() !!}
                </div>
            @endif
        </div>
    </div>
    {{--    </form>--}}
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">

    const templatesCount = @json($emailTemplates).total;
    const descriptiveLabel = templatesCount === 1 ? ' Item' : ' Items';
    $('#templatesCount').text(templatesCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
