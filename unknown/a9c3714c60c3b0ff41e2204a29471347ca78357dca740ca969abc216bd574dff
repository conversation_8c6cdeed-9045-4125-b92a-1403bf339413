<?php

namespace App\Mail;

use App\Models\AdminSettings;
use App\Models\InvoiceProduct;
use Barryvdh\DomPDF\PDF;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Attachment;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendInvoiceProduct extends Mailable
{
    use Queueable, SerializesModels;

    private PDF $pdf;
    private InvoiceProduct $invoice;

    /**
     * Create a new message instance.
     */
    public function __construct(InvoiceProduct $invoice, PDF $pdf)
    {
        $this->invoice = $invoice;
        $this->pdf = $pdf;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $settings = AdminSettings::first();
        return new Envelope(
            from: new Address($settings->smtp_from_email, $settings->smtp_from_name),
            subject: 'Invoice ' . ($this->invoice->reminder_date_tmp != NULL ? 'reminder ' : '') . '#' . $this->invoice->formatted_number . ' from Lagree Fitness',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.send-invoice-product',
            with: ['invoice' => $this->invoice],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [
            Attachment::fromData(fn() => $this->pdf->output(), 'invoice-' . $this->invoice->formatted_number . '.pdf')
                ->withMime('application/pdf'),
        ];
    }
}
