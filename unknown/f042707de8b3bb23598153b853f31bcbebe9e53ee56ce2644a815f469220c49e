<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'ERP') }}</title>

    <!-- Scripts -->
    @vite(['resources/sass/app.sass', 'resources/js/app.js', 'resources/css/custom.css'])
    <style>
        .text-logo {
            height: 100px;
            width: 100px;
            border: 2px solid black;
            .text-right {
                text-align: right;
            }
        }
        .input-placeholder input {
            padding-left: 0 !important;
        }
        .input-placeholder {
            position: relative;
        }
        .input-placeholder > input + .placeholder-shown {
            opacity: 0;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 49px;
            display: flex;
            padding: 16px;
            align-items: center;
            padding-left: 0 !important;
            pointer-events: none;
            transition: all 0.5s cubic-bezier(.13,.56,.38,.89) 0s;
            font-size: 14px;
            color: #969696;
        }
        .input-placeholder > input:placeholder-shown + .placeholder-shown {
            transform: translateY(0px);
        }
        .input-placeholder > input:not(:placeholder-shown) + .placeholder-shown {
            transform: translateY(-20px);
            opacity: 1;
            font-size: 11px;
        }
        @media(max-width: 576px){
            .login-modal-content input {
                padding-left: 15px !important;
                padding-right: 15px !important;
            }
            .input-placeholder input {
                padding-left: 15px !important;
            }

        }
    </style>
</head>

<body class="font-sans antialiased auth-page">
    <div id="app">
        <main>
            <div class="container px-0">
                <div class="auth-page-content-wrap d-flex flex-column align-items-center justify-content-center py-0 py-md-5">
                    @yield('content')
                </div>
            </div>
        </main>
    </div>

    @include('partials.js-links')
</body>
</html>
