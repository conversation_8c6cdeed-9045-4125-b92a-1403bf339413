<div class="form-group {{ $field_class ?? '' }} @if($errors->has($field_name)) has-danger @endif">
    @foreach($values as $key => $value)
        <label for="{{ $key }}" class="radio-container">
            <input id="{{ $key }}" name="{{ $field_name }}" placeholder="{{ $field_label }}"
                   value="{{ $value['value'] }}"
                   class="{{ $field_class ?? '' }}"
                   {{ old($field_name, $checked) == $value['value'] ? 'checked' : '' }}
                   type="radio"
                @readonly(isset($readonly) && $readonly == 'readonly')
                @disabled(isset($disabled) && $disabled == 'disabled')
                @required(isset($required) && $required == 'required')
            >
            <span class="checkmark"></span>
            <span class="radio-text">{!! $value['text'] !!}</span>
        </label>
    @endforeach

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</div>
