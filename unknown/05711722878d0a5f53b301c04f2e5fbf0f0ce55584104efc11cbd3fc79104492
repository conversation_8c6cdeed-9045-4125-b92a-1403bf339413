<div class="border-bottom border-light mt-6 pb-4">
    <div class="row">
        <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
            <div class="customer-dashboard-card position-relative text-md-start">
                <div class="customer-dashboard-card-body ps-5">
                    <h3 class="customer-dashboard-card-title">
                        {{ $admin_currency_symbol }}{{ number_format($remainingSubscriptionsSum, 2) }}</h3>
                    <p class="customer-dashboard-card-text text-secondary">In remaining subscriptions</p>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
            <div class="customer-dashboard-card position-relative text-md-start">
                <div class="customer-dashboard-card-body ps-5">
                    <h3 class="customer-dashboard-card-title">{{ $activeLicenses }}</h3>
                    <p class="customer-dashboard-card-text text-secondary">Active licenses</p>
                    <p class="customer-dashboard-card-text text-warning">({{ $activeExclusivities }} exclusivity)</p>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
            <div class="customer-dashboard-card position-relative text-md-start">
                <div class="customer-dashboard-card-body ps-5">
                    <h3 class="customer-dashboard-card-title">{{ $activeLeases }}</h3>
                    <p class="customer-dashboard-card-text text-secondary">Active leases</p>
                    <p class="customer-dashboard-card-text text-secondary">({{ $machines }} machines)</p>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
            <div class="customer-dashboard-card position-relative text-md-start">
                <div class="customer-dashboard-card-body ps-5">
                    <h3 class="customer-dashboard-card-title">{{ $purchasedMachines }}</h3>
                    <p class="customer-dashboard-card-text text-secondary">Purchased machines</p>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="main-subtitle no-border-btm">
    <h5>last 10 payments</h5>
</div>

    <div id="last-10-payments-table" class="entity-table mb-9">
        @if(count($payments) !== 0)
            <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
                <div class="hide-transp">{{ __('Id') }}</div>
                <div>{{ __('Name') }}</div>
                <div>{{ __('Type') }}</div>
                <div>{{ __('Payment') }}</div>
                <div>{{ __('Amount') }}</div>
                <div>{{ __('Invoice') }}</div>
                <div>{{ __('Date') }}</div>
                <div>{{ __('Status') }}</div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    @foreach($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            @endif
            @php
            // echo '<pre>';
            // print_r($payments->toArray());
            // die();
            
            @endphp
            <div class="list-group pb-0" id="results">
                @foreach($payments as $payment)
                    <div class="list-group-item list-group-item-action d-grid align-items-center">
                        <p class="my-0 hide-transp">{{$payment->sequential_id}}</p>
                        <p class="my-0" data-column="customer.name"><a href="{{ route('admin.customers.dashboard', ['customer' => ($payment->customer ?? 0)]) }}" class="fw-medium" style="text-decoration: none;">{{$payment->customer->name ?? '-'}}</a></p>
                        @if($payment->type === \App\Helpers\Constants::LICENSE_TYPES[0])
                            <p class="my-0 text-secondary" data-column="payment_amount">License <span class="d-block">({{ \App\Helpers\Constants::LICENSE_PACKAGES_KEYS[$payment->license->package] }})</span></p>
                        @elseif($payment->type === \App\Helpers\Constants::LICENSE_TYPES[1])
                            <p class="my-0 text-secondary">Exclusivity <span class="d-block">({{ \App\Helpers\Constants::LICENSE_PACKAGES_KEYS[$payment->license->package] }})</span></p>
                        @elseif($payment->type === 'purchase')
                            <p class="my-0 text-secondary">Purchase <span class="d-block">({{$payment->name}})</span></p>
                        @else
                            <p class="my-0 text-secondary">Lease <span class="d-block">({{$payment->name}})</span></p>
                        @endif

                        @if($payment->type === 'license')
                            <p class="my-0" data-column="payment_amount">{{ __('Yearly') }}</p>
                        @elseif($payment->type === 'purchase')
                            <p class="my-0" data-column="payment_number">{{ __('One-time') }}</p>
                        @elseif($payment->type === 'lease')
                            <p class="my-0" data-column="payment_number">{{ $payment->payment_number }} of {{ $payment->lease->duration }}</p>
                        @endif
                        {{-- <p class="my-0">{{$payment->payment_number}} of {{($payment->license_id !== null) ? $payment->license->duration : $payment->lease->duration}}</p> --}}
                        <p class="my-0 text-success">+ {{$admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                        <p class="my-0 text-secondary" data-column="payment_amount">
                            {{ $payment->invoice ? 'Invoice #' . $payment->invoice?->formatted_number : 'No invoice found' }}
                        </p>
                        <p class="my-0">{{\Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y')}}</p>
                        <div class="my-0" data-column="status">
                            @if($payment->status == \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                            <div class="d-inline-block rounded-pill bg-success text-success medium">
                                {{ __('Paid') }}
                            </div>
                            @endif
                        </div>

                        @if ($payment->invoice)
                        <div class="round-button-dropdown">
                            <button class="dropbtn">
                                <i class="fa fa-ellipsis-h"></i>
                            </button>
                            <div class="dropdown-content" id="last-10-payments-dropdown-content">
                                <a href="{{route('admin.download-invoice', ['customer' => $payment->customer, 'invoice' => $payment->invoice])}}">Download Invoice</a>
                                <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$payment->invoice->id}}">{{ __('Email Invoice') }}</a>
                            </div>
                        </div>
                        @endif
                    </div>
                    @if ($payment->invoice)
                    @include('partials.modals.email-invoice', [
                        'id' => $payment->invoice->id,
                        'route' => route('admin.email-invoice', ['customer' => $payment->customer, 'invoice' => $payment->invoice]),
                        'invoice' => $payment->invoice
                    ])
                    @endif
                @endforeach
            </div>
        @else
        <p class="no-results-txt">There are no results.</p>        
        @endif
    </div>

<script type="module">
    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $(document).on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
