@extends('layouts.app')
@section('content')
    <div class="page-title ttl-dropdown border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ $license->studio->name }}</h3>
            </div>
            <a href="{{ route('studio.location', ['customer' => $customer]) }}" class="back-link">← Back</a>
        </div>
    </div>
    <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
        <h5 class="main-subtitle no-border-btm text-uppercase">{{ __('basic info') }}</h5>
        <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Location Name:</div>
                    <div class="">
                        {{ $license->studio->name }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Licensee:</div>
                    <div class="">
                        {{ $license->studio->owner_first_name }} {{ $license->studio->owner_last_name }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Phone #:</div>
                    <div class="">
                        {{ $license->studio->phone }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{ $license->studio->email }}
                    </div>
                </div>
            </div>
        </div>

        <div class="main-subtitle no-border-btm">
            <h5>ADDRESS INFO</h5>
        </div>
        <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Address:</div>
                    <div class="">
                        {{ $license?->studio?->address }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">City:</div>
                    <div class="">
                        {{ $license?->studio?->city }}
                    </div>
                </div>
                @if ($license->location === \App\Helpers\Constants::LOCATION_TYPE[0])
                    <div class="instructor-basic-info">
                        <div class="text-secondary">State:</div>
                        <div class="">
                            {{ $license?->studio?->state?->name }}
                        </div>
                    </div>
                @else
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Country:</div>
                        <div class="">
                            {{ $license?->studio?->country?->name }}
                        </div>
                    </div>
                @endif
                <div class="instructor-basic-info">
                    <div class="text-secondary">ZIP Code:</div>
                    <div class="">
                        {{ $license?->studio?->zip }}
                    </div>
                </div>
            </div>
        </div>

        @if ($license->type === \App\Helpers\Constants::LICENSE_TYPES[0])
            <h5 class="my-6 fs-14px form-section-title custom-fw-500 text-uppercase pb-05">{{ __('Location info') }}
            </h5>
            <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Type:</div>
                        <div class="">
                            {{ ucfirst($license->type) }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Price:</div>
                        <div class="">
                            <span
                                class="text-success">{{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseTotal, 2) }}</span>
                            &nbsp; ({{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseMonthly, 2) }}/year)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Deposit:</div>
                        <div class="">
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseDeposit, 2) ?? 'No Deposit' }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Remaining:</div>
                        <div class="">
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseRemaining['paymentSum'], 2) }}
                            ({{ $licenseRemaining['count'] }}/{{ number_format($license->duration / 100 / $conversionRate, 2) }}
                            months)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Start date:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($license->starting_date)->format('m/d/Y') }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Ends on:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($license->payments->sortByDesc('starting_date')->take(1)->first()->starting_date)->addYear(1)->format('m/d/Y') }}
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                <a type="button"
                    href="{{ route('studio.location.edit', ['customer' => $customer, 'license' => $license]) }}">
                    <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
                </a>
            </div>
        @else
            <h5 class="mt-45 mb-45 fs-14px form-section-title text-uppercase">{{ __('Exclusivity Info') }}</h5>
            <div>
                <div class="profile-info-div">
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Type:</div>
                        <div class="">
                            {{ ucfirst($license->type) }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Price:</div>
                        <div class="">
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseTotal, 2) }}
                            ({{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseMonthly / 100 / $conversionRate, 2) }}/month)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Remaining:</div>
                        <div class="">
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($licenseRemaining['paymentSum'], 2) }}
                            ({{ $licenseRemaining['count'] }}/{{ number_format($license->duration / 100 / $conversionRate, 2) }}
                            months)
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Start date:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($license->starting_date)->format('m/d/Y') }}
                        </div>
                    </div>
                    <div class="instructor-basic-info">
                        <div class="text-secondary">Ends on:</div>
                        <div class="">
                            {{ \Carbon\Carbon::parse($license->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y') }}
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
                <a type="button"
                    href="{{ route('studio.location.edit', ['customer' => $customer, 'license' => $license]) }}">
                    <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
                </a>
            </div>
        @endif
    </div>
@endsection
