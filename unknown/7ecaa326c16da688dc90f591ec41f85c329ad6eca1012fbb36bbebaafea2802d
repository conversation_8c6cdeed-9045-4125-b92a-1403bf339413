<?php

namespace App\Observers;

use App\Helpers\Constants;
use App\Models\Purchase;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class PurchaseObserver
{
    public function created(Purchase $purchase)
    {
        Log::info("Purchase created: {$purchase->id}");
        $startDate = Carbon::parse($purchase->starting_date);

        if(isset($purchase->deposit_amount) AND $purchase->deposit_amount != '' AND $purchase->deposit_amount != NULL) {
            $deposit_payment = Payment::create([
                'purchase_id' => $purchase->id,
                'machine_id' => $purchase->machine_id,
                'customer_id' => $purchase->customer_id,
                'payment_number' => 1,
                'payment_amount' => $purchase->deposit_amount * $purchase->machine_quantity,
                'payment_date' => $startDate,
                'description' => 'Deposit',
            ]);
            Log::info("Deposit Payment created: {$deposit_payment->id}");

            $payment = Payment::create([
                'purchase_id' => $purchase->id,
                'machine_id' => $purchase->machine_id,
                'customer_id' => $purchase->customer_id,
                'payment_number' => 2,
                'payment_amount' => $purchase->machine_price * $purchase->machine_quantity - $purchase->deposit_amount * $purchase->machine_quantity,
                'payment_date' => $startDate,
                'description' => 'Rest of the payment',
            ]);
            Log::info("Purchase Rest of Payment created: {$payment->id}");

            $deposit_invoice = $deposit_payment->invoice()->create(['customer_id' => $purchase->customer_id]);
            Log::info("Deposit Invoice created: {$deposit_invoice->id}");

            $invoice = $payment->invoice()->create(['customer_id' => $purchase->customer_id]);
            Log::info("Invoice created: {$invoice->id}");
        }else{
            $payment = Payment::create([
                'purchase_id' => $purchase->id,
                'machine_id' => $purchase->machine_id,
                'customer_id' => $purchase->customer_id,
                'payment_number' => 1,
                'payment_amount' => $purchase->machine_price * $purchase->machine_quantity,
                'payment_date' => $startDate,
                'description' => 'Full purchase payment',
            ]);
            Log::info("Purchase Full amount purchase payment created: {$payment->id}");

            $invoice = $payment->invoice()->create(['customer_id' => $purchase->customer_id]);
            Log::info("Invoice created: {$invoice->id}");
        }
    }

    public function updated(Purchase $purchase)
    {
        // if ($purchase->wasChanged('monthly_installment')) {
        //     $purchase->payments()
        //         ->where('payment_date', '>', Carbon::now()->endOfMonth())
        //         ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
        //         ->update(['payment_amount' => $purchase->monthly_installment * $purchase->machine_quantity]);
        // }

        // if ($purchase->wasChanged('duration')) {
        //     $oldDuration = $purchase->getOriginal('duration');
        //     $newDuration = $purchase->duration;

        //     if ($oldDuration > $newDuration) {
        //         $paymentsToDelete = $purchase->payments()
        //             ->where('payment_number', '>', $newDuration)
        //             ->where('payment_date', '>', Carbon::now())
        //             ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
        //             ->get();

        //         foreach ($paymentsToDelete as $payment) {
        //             $payment->invoice->delete();
        //             $payment->forceDelete();
        //         }

        //     } elseif ($oldDuration < $newDuration) {
        //         $numberOfPaymentsToBeAdded = $newDuration - $oldDuration;
        //         $dateOfCurrentLastPayment = $purchase->payments->where('payment_number', $oldDuration)->first()->payment_date;
        //         for ($i = 1; $i <= $numberOfPaymentsToBeAdded; $i++) {
        //             $payment = $purchase->payments()->create([
        //                 'machine_id' => $purchase->machine_id,
        //                 'customer_id'    => $purchase->customer_id,
        //                 'payment_number' => $oldDuration + $i,
        //                 'payment_amount' => $purchase->monthly_installment * $purchase->machine_quantity,
        //                 'payment_date'   => Carbon::parse($dateOfCurrentLastPayment)->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
        //             ]);

        //             $payment->invoice()->create(['customer_id' => $purchase->customer_id]);
        //         }
        //     }
        // }

        if ($purchase->wasChanged('starting_date')) {
            $paymentsToDelete = $purchase->payments;
            foreach ($paymentsToDelete as $payment) {
                $payment->invoice->delete();
                $payment->forceDelete();
            }
            // for ($i = 1; $i <= $purchase->duration; $i++) {
                $payment = $purchase->payments()->create([
                    'machine_id' => $purchase->machine_id,
                    'customer_id' => $purchase->customer_id,
                    'payment_number' => 1,
                    'payment_amount' => $purchase->machine_price * $purchase->machine_quantity,
                    'payment_date' => Carbon::parse($purchase->starting_date),
                ]);

                $payment->invoice()->create(['customer_id' => $purchase->customer_id]);
            // }
        }

        if ($purchase->wasChanged('machine_quantity')) {
            $purchase->payments()
                ->where('payment_date', '>', Carbon::now()->endOfMonth())
                ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                ->update(['payment_amount' => $purchase->machine_price * $purchase->machine_quantity]);
        }
    }
}