/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 100427 (10.4.27-MariaDB)
 Source Host           : localhost:3306
 Source Schema         : lagree_erp

 Target Server Type    : MySQL
 Target Server Version : 100427 (10.4.27-MariaDB)
 File Encoding         : 65001

 Date: 07/03/2025 09:12:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin_notifications
-- ----------------------------
DROP TABLE IF EXISTS `admin_notifications`;
CREATE TABLE `admin_notifications`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint UNSIGNED NULL DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `link` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_notifications_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `admin_notifications_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin_notifications
-- ----------------------------
INSERT INTO `admin_notifications` VALUES (1, 1, 'First text', 'This is very first notification.', 'http://somelink.com', '2024-12-26 01:00:00', NULL, '2024-12-26 07:12:51', '2024-12-26 07:12:51');

-- ----------------------------
-- Table structure for admin_settings
-- ----------------------------
DROP TABLE IF EXISTS `admin_settings`;
CREATE TABLE `admin_settings`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `admin_id` bigint UNSIGNED NULL DEFAULT NULL,
  `currency_id` bigint UNSIGNED NULL DEFAULT NULL,
  `timezone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `date_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `time_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `week_start` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `seo_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `seo_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `smtp_from_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `smtp_from_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `admin_settings_admin_id_foreign`(`admin_id` ASC) USING BTREE,
  INDEX `admin_settings_currency_id_foreign`(`currency_id` ASC) USING BTREE,
  CONSTRAINT `admin_settings_admin_id_foreign` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `admin_settings_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin_settings
-- ----------------------------
INSERT INTO `admin_settings` VALUES (1, 1, 1, 'America/Los_Angeles', 'F j, Y', 'g:i A', 'sunday', NULL, NULL, 'Lagree Fitness (IMS)', '<EMAIL>', 'instructors/1-03CEqE6uvnl4wxBau44yB2b2NlBxJmfE7qDR9WT8.jpg', NULL, '2024-12-12 12:46:22', '2025-01-16 03:19:16');
INSERT INTO `admin_settings` VALUES (2, 21, 1, 'America/Los_Angeles', 'F j, Y', 'g:i A', 'sunday', NULL, NULL, 'Lagree Fitness (IMS)', '<EMAIL>', 'instructors/1-i0DNWVXcRGjB9wFoabCScESMpWJxWVE0Wv0nD67o.jpg', NULL, '2024-12-12 12:46:22', '2025-01-08 06:52:51');

-- ----------------------------
-- Table structure for agreements
-- ----------------------------
DROP TABLE IF EXISTS `agreements`;
CREATE TABLE `agreements`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint UNSIGNED NOT NULL,
  `adobe_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `adobe_agreement_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `send_date` date NULL DEFAULT NULL,
  `singing_date` date NULL DEFAULT NULL,
  `completed_date` date NULL DEFAULT NULL,
  `filepath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `agreements_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `agreements_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of agreements
-- ----------------------------

-- ----------------------------
-- Table structure for bundle
-- ----------------------------
DROP TABLE IF EXISTS `bundle`;
CREATE TABLE `bundle`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bundle
-- ----------------------------
INSERT INTO `bundle` VALUES (1, 'Paxton Boyle', NULL, '2025-02-04 14:02:25', '2025-02-04 14:02:25');
INSERT INTO `bundle` VALUES (2, 'Montana Howell', NULL, '2025-02-04 14:02:25', '2025-02-04 14:02:25');
INSERT INTO `bundle` VALUES (3, 'Aracely Gottlieb', NULL, '2025-02-04 14:02:25', '2025-02-04 14:02:25');
INSERT INTO `bundle` VALUES (4, 'Kiel Lockman', NULL, '2025-02-04 14:02:25', '2025-02-04 14:02:25');
INSERT INTO `bundle` VALUES (5, 'Pearlie West', NULL, '2025-02-04 14:02:25', '2025-02-04 14:02:25');
INSERT INTO `bundle` VALUES (6, 'Sim Ruecker', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (7, 'Gardner Satterfield', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (8, 'Fae Gleichner V', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (9, 'Marge Veum', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (10, 'Margie Wyman Sr.', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (11, 'Jolie Schneider', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (12, 'Lawson Renner II', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (13, 'Prof. Adeline Dietrich Sr.', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (14, 'Dr. Lauryn Blick', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (15, 'Margaret Jenkins V', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (16, 'Trinity Wuckert', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (17, 'Sean Schumm', NULL, '2025-02-04 14:02:26', '2025-02-04 14:02:26');
INSERT INTO `bundle` VALUES (18, 'Dr. Gerard Lakin MD', NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle` VALUES (19, 'Ms. Giovanna Zemlak', NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle` VALUES (20, 'Dr. Malcolm Hintz III', NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle` VALUES (21, 'Mr. Glen Kunze', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (22, 'Ms. Eleonore Reinger', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (23, 'Mrs. Herta Satterfield PhD', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (24, 'Prof. Melyssa Wintheiser', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (25, 'Mrs. Marcelina Larson', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (26, 'Natalia Upton', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (27, 'Mavis Nader II', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (28, 'Ally Armstrong', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (29, 'Mose Hand', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (30, 'Prof. Santa Lemke', NULL, '2025-02-10 15:17:40', '2025-02-10 15:17:40');
INSERT INTO `bundle` VALUES (31, 'Dr. Justice Schmitt I', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (32, 'Neva Kuhlman PhD', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (33, 'Mr. Hudson Rempel II', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (34, 'Gregoria Torphy PhD', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (35, 'Name Kuphal', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (36, 'Moriah McKenzie', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (37, 'Elizabeth Franecki', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (38, 'Mr. Bell Schmidt', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (39, 'Rosalia Koelpin MD', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle` VALUES (40, 'Peyton Murphy', NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');

-- ----------------------------
-- Table structure for bundle_item
-- ----------------------------
DROP TABLE IF EXISTS `bundle_item`;
CREATE TABLE `bundle_item`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `bundle_id` bigint UNSIGNED NOT NULL,
  `product_id` bigint UNSIGNED NOT NULL,
  `quantity` int NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `bundle_item_bundle_id_foreign`(`bundle_id` ASC) USING BTREE,
  INDEX `bundle_item_product_id_foreign`(`product_id` ASC) USING BTREE,
  CONSTRAINT `bundle_item_bundle_id_foreign` FOREIGN KEY (`bundle_id`) REFERENCES `bundle` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `bundle_item_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bundle_item
-- ----------------------------
INSERT INTO `bundle_item` VALUES (1, 20, 134, 2, NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle_item` VALUES (2, 13, 93, 10, NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle_item` VALUES (3, 5, 42, 6, NULL, '2025-02-04 14:02:27', '2025-02-04 14:02:27');
INSERT INTO `bundle_item` VALUES (4, 11, 236, 9, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (5, 17, 33, 3, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (6, 8, 126, 5, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (7, 7, 28, 1, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (8, 15, 24, 1, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (9, 20, 200, 5, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (10, 20, 110, 6, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (11, 7, 110, 10, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (12, 16, 219, 3, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (13, 3, 170, 10, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (14, 16, 164, 8, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (15, 20, 195, 9, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (16, 8, 28, 3, NULL, '2025-02-04 14:02:28', '2025-02-04 14:02:28');
INSERT INTO `bundle_item` VALUES (17, 8, 218, 4, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (18, 15, 226, 3, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (19, 1, 163, 7, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (20, 1, 131, 3, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (21, 10, 53, 10, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (22, 16, 40, 7, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (23, 5, 35, 5, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (24, 3, 206, 6, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (25, 7, 156, 3, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (26, 12, 177, 7, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (27, 3, 179, 9, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (28, 14, 107, 4, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (29, 10, 205, 5, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (30, 6, 232, 4, NULL, '2025-02-04 14:02:29', '2025-02-04 14:02:29');
INSERT INTO `bundle_item` VALUES (31, 2, 136, 6, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (32, 17, 160, 9, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (33, 15, 199, 8, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (34, 4, 206, 6, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (35, 7, 158, 9, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (36, 5, 223, 9, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (37, 14, 135, 7, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (38, 19, 43, 6, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (39, 4, 68, 10, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (40, 16, 159, 3, NULL, '2025-02-04 14:02:30', '2025-02-04 14:02:30');
INSERT INTO `bundle_item` VALUES (41, 36, 232, 9, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (42, 16, 192, 2, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (43, 22, 127, 9, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (44, 13, 199, 7, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (45, 25, 129, 2, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (46, 28, 87, 5, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (47, 26, 36, 6, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (48, 6, 233, 7, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (49, 32, 151, 7, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (50, 18, 148, 8, NULL, '2025-02-10 15:17:41', '2025-02-10 15:17:41');
INSERT INTO `bundle_item` VALUES (51, 34, 41, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (52, 17, 250, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (53, 39, 116, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (54, 33, 115, 2, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (55, 2, 132, 3, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (56, 31, 136, 6, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (57, 3, 174, 6, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (58, 37, 188, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (59, 3, 21, 5, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (60, 38, 177, 2, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (61, 19, 149, 9, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (62, 24, 54, 1, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (63, 32, 143, 4, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (64, 5, 130, 1, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (65, 10, 162, 7, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (66, 6, 39, 5, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (67, 19, 138, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (68, 18, 116, 8, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (69, 10, 165, 2, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (70, 14, 241, 6, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (71, 22, 162, 7, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (72, 15, 242, 7, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (73, 11, 52, 9, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (74, 31, 38, 3, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (75, 1, 172, 5, NULL, '2025-02-10 15:17:42', '2025-02-10 15:17:42');
INSERT INTO `bundle_item` VALUES (76, 10, 151, 7, NULL, '2025-02-10 15:17:43', '2025-02-10 15:17:43');
INSERT INTO `bundle_item` VALUES (77, 1, 242, 8, NULL, '2025-02-10 15:17:43', '2025-02-10 15:17:43');
INSERT INTO `bundle_item` VALUES (78, 38, 180, 7, NULL, '2025-02-10 15:17:43', '2025-02-10 15:17:43');
INSERT INTO `bundle_item` VALUES (79, 13, 117, 7, NULL, '2025-02-10 15:17:43', '2025-02-10 15:17:43');
INSERT INTO `bundle_item` VALUES (80, 5, 161, 1, NULL, '2025-02-10 15:17:43', '2025-02-10 15:17:43');

-- ----------------------------
-- Table structure for companies
-- ----------------------------
DROP TABLE IF EXISTS `companies`;
CREATE TABLE `companies`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `state_id` bigint UNSIGNED NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `owner_last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `companies_state_id_foreign`(`state_id` ASC) USING BTREE,
  CONSTRAINT `companies_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of companies
-- ----------------------------
INSERT INTO `companies` VALUES (1, 5, 'Maximum Fitness', NULL, NULL, '<EMAIL>', '(310) 990-0370', '9340 Eton Ave, Chatsworth, CA 91311', 'Chatsworth', '91311', 1, '2024-12-12 12:46:22', '2024-12-30 00:58:19', NULL);
INSERT INTO `companies` VALUES (2, 5, 'Lagree Fitness', NULL, NULL, NULL, '103-545-5862', '9340 Eton Ave, Chatsworth, CA 91311', NULL, NULL, 1, '2024-12-12 12:46:22', '2025-01-26 05:49:30', NULL);
INSERT INTO `companies` VALUES (3, 5, 'SPX Fitness', NULL, NULL, NULL, NULL, '9340 Eton Ave, Chatsworth, CA 91311', NULL, NULL, 1, '2024-12-12 12:46:22', '2024-12-12 12:46:22', NULL);
INSERT INTO `companies` VALUES (4, 14, 'Svercer Trade', NULL, NULL, '<EMAIL>', '+1 (697) 392-3993', NULL, NULL, NULL, 1, '2025-01-03 01:49:21', '2025-01-09 06:51:21', NULL);
INSERT INTO `companies` VALUES (5, 3, 'A.Corp', NULL, NULL, '<EMAIL>', '123456789', 'Yukio address', 'Yukio city', '37400', 1, '2025-03-05 08:19:04', '2025-03-05 08:19:04', NULL);

-- ----------------------------
-- Table structure for contact_people
-- ----------------------------
DROP TABLE IF EXISTS `contact_people`;
CREATE TABLE `contact_people`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `contactable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `contactable_id` bigint NOT NULL,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of contact_people
-- ----------------------------
INSERT INTO `contact_people` VALUES (1, 'App\\Models\\Supplier', 1, 'Vo Cong', 'Hai', 'CEO', '+84 38 4545 777', '<EMAIL>', NULL, '2024-12-23 05:55:30', '2024-12-23 05:55:59');
INSERT INTO `contact_people` VALUES (2, 'App\\Models\\Supplier', 2, 'Ivy', 'Barrett', 'Animi dignissimos i', '+****************', '<EMAIL>', NULL, '2025-01-03 01:50:19', '2025-01-26 08:54:07');
INSERT INTO `contact_people` VALUES (3, 'App\\Models\\Supplier', 3, 'Quinton', 'Pouros', 'Quis hic fugit exercitationem ipsa fugiat.', '398-264-4205', '<EMAIL>', NULL, '2025-01-08 12:28:47', '2025-01-08 12:28:47');
INSERT INTO `contact_people` VALUES (4, 'App\\Models\\Supplier', 4, 'asdasd', 'asdasd', 'asdasd', '123123', '<EMAIL>', NULL, '2025-01-09 06:47:32', '2025-01-09 06:47:32');

-- ----------------------------
-- Table structure for conversion_rates
-- ----------------------------
DROP TABLE IF EXISTS `conversion_rates`;
CREATE TABLE `conversion_rates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `rateable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rateable_id` bigint NOT NULL,
  `currency_id` bigint UNSIGNED NOT NULL,
  `rate` decimal(8, 4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `conversion_rates_currency_id_foreign`(`currency_id` ASC) USING BTREE,
  CONSTRAINT `conversion_rates_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 63 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of conversion_rates
-- ----------------------------
INSERT INTO `conversion_rates` VALUES (3, 'App\\Models\\License', 1, 1, 1.0000, '2024-12-16 11:04:33', '2024-12-16 11:04:33');
INSERT INTO `conversion_rates` VALUES (4, 'App\\Models\\License', 1, 2, 1.1765, '2024-12-16 11:04:33', '2024-12-16 11:04:33');
INSERT INTO `conversion_rates` VALUES (5, 'App\\Models\\License', 2, 1, 1.0000, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `conversion_rates` VALUES (6, 'App\\Models\\License', 2, 2, 1.1765, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `conversion_rates` VALUES (7, 'App\\Models\\Lease', 1, 1, 1.0000, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `conversion_rates` VALUES (8, 'App\\Models\\Lease', 1, 2, 1.1765, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `conversion_rates` VALUES (11, 'App\\Models\\License', 4, 1, 1.0000, '2024-12-30 08:19:03', '2024-12-30 08:19:03');
INSERT INTO `conversion_rates` VALUES (12, 'App\\Models\\License', 4, 2, 1.1765, '2024-12-30 08:19:03', '2024-12-30 08:19:03');
INSERT INTO `conversion_rates` VALUES (13, 'App\\Models\\License', 5, 1, 1.0000, '2024-12-30 09:30:06', '2024-12-30 09:30:06');
INSERT INTO `conversion_rates` VALUES (14, 'App\\Models\\License', 5, 2, 1.1765, '2024-12-30 09:30:06', '2024-12-30 09:30:06');
INSERT INTO `conversion_rates` VALUES (15, 'App\\Models\\License', 3, 1, 1.0000, '2024-12-30 09:30:43', '2024-12-30 09:30:43');
INSERT INTO `conversion_rates` VALUES (16, 'App\\Models\\License', 3, 2, 1.1765, '2024-12-30 09:30:43', '2024-12-30 09:30:43');
INSERT INTO `conversion_rates` VALUES (51, 'App\\Models\\License', 21, 1, 1.0000, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `conversion_rates` VALUES (52, 'App\\Models\\License', 21, 2, 1.1765, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `conversion_rates` VALUES (53, 'App\\Models\\License', 22, 1, 1.0000, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `conversion_rates` VALUES (54, 'App\\Models\\License', 22, 2, 1.1765, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `conversion_rates` VALUES (55, 'App\\Models\\License', 23, 1, 1.0000, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `conversion_rates` VALUES (56, 'App\\Models\\License', 23, 2, 1.1765, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `conversion_rates` VALUES (57, 'App\\Models\\License', 24, 1, 1.0000, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `conversion_rates` VALUES (58, 'App\\Models\\License', 24, 2, 1.1765, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `conversion_rates` VALUES (59, 'App\\Models\\License', 31, 1, 1.0000, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `conversion_rates` VALUES (60, 'App\\Models\\License', 31, 2, 1.1765, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `conversion_rates` VALUES (61, 'App\\Models\\License', 49, 1, 1.0000, '2025-03-06 22:43:34', '2025-03-06 22:43:34');
INSERT INTO `conversion_rates` VALUES (62, 'App\\Models\\License', 49, 2, 1.1765, '2025-03-06 22:43:34', '2025-03-06 22:43:34');

-- ----------------------------
-- Table structure for countries
-- ----------------------------
DROP TABLE IF EXISTS `countries`;
CREATE TABLE `countries`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 196 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of countries
-- ----------------------------
INSERT INTO `countries` VALUES (1, 'Afghanistan', NULL, NULL);
INSERT INTO `countries` VALUES (2, 'Albania', NULL, NULL);
INSERT INTO `countries` VALUES (3, 'Algeria', NULL, NULL);
INSERT INTO `countries` VALUES (4, 'Andorra', NULL, NULL);
INSERT INTO `countries` VALUES (5, 'Angola', NULL, NULL);
INSERT INTO `countries` VALUES (6, 'Antigua and Barbuda', NULL, NULL);
INSERT INTO `countries` VALUES (7, 'Argentina', NULL, NULL);
INSERT INTO `countries` VALUES (8, 'Armenia', NULL, NULL);
INSERT INTO `countries` VALUES (9, 'Australia', NULL, NULL);
INSERT INTO `countries` VALUES (10, 'Austria', NULL, NULL);
INSERT INTO `countries` VALUES (11, 'Azerbaijan', NULL, NULL);
INSERT INTO `countries` VALUES (12, 'Bahamas', NULL, NULL);
INSERT INTO `countries` VALUES (13, 'Bahrain', NULL, NULL);
INSERT INTO `countries` VALUES (14, 'Bangladesh', NULL, NULL);
INSERT INTO `countries` VALUES (15, 'Barbados', NULL, NULL);
INSERT INTO `countries` VALUES (16, 'Belarus', NULL, NULL);
INSERT INTO `countries` VALUES (17, 'Belgium', NULL, NULL);
INSERT INTO `countries` VALUES (18, 'Belize', NULL, NULL);
INSERT INTO `countries` VALUES (19, 'Benin', NULL, NULL);
INSERT INTO `countries` VALUES (20, 'Bhutan', NULL, NULL);
INSERT INTO `countries` VALUES (21, 'Bolivia', NULL, NULL);
INSERT INTO `countries` VALUES (22, 'Bosnia and Herzegovina', NULL, NULL);
INSERT INTO `countries` VALUES (23, 'Botswana', NULL, NULL);
INSERT INTO `countries` VALUES (24, 'Brazil', NULL, NULL);
INSERT INTO `countries` VALUES (25, 'Brunei', NULL, NULL);
INSERT INTO `countries` VALUES (26, 'Bulgaria', NULL, NULL);
INSERT INTO `countries` VALUES (27, 'Burkina Faso', NULL, NULL);
INSERT INTO `countries` VALUES (28, 'Burundi', NULL, NULL);
INSERT INTO `countries` VALUES (29, 'Cambodia', NULL, NULL);
INSERT INTO `countries` VALUES (30, 'Cameroon', NULL, NULL);
INSERT INTO `countries` VALUES (31, 'Canada', NULL, NULL);
INSERT INTO `countries` VALUES (32, 'Central African Republic', NULL, NULL);
INSERT INTO `countries` VALUES (33, 'Chad', NULL, NULL);
INSERT INTO `countries` VALUES (34, 'Chile', NULL, NULL);
INSERT INTO `countries` VALUES (35, 'China', NULL, NULL);
INSERT INTO `countries` VALUES (36, 'Colombia', NULL, NULL);
INSERT INTO `countries` VALUES (37, 'Comoros', NULL, NULL);
INSERT INTO `countries` VALUES (38, 'Congo (Brazzaville)', NULL, NULL);
INSERT INTO `countries` VALUES (39, 'Congo (Kinshasa)', NULL, NULL);
INSERT INTO `countries` VALUES (40, 'Costa Rica', NULL, NULL);
INSERT INTO `countries` VALUES (41, 'Côte d\'Ivoire', NULL, NULL);
INSERT INTO `countries` VALUES (42, 'Croatia', NULL, NULL);
INSERT INTO `countries` VALUES (43, 'Cuba', NULL, NULL);
INSERT INTO `countries` VALUES (44, 'Cyprus', NULL, NULL);
INSERT INTO `countries` VALUES (45, 'Czech Republic', NULL, NULL);
INSERT INTO `countries` VALUES (46, 'Denmark', NULL, NULL);
INSERT INTO `countries` VALUES (47, 'Djibouti', NULL, NULL);
INSERT INTO `countries` VALUES (48, 'Dominica', NULL, NULL);
INSERT INTO `countries` VALUES (49, 'Dominican Republic', NULL, NULL);
INSERT INTO `countries` VALUES (50, 'Ecuador', NULL, NULL);
INSERT INTO `countries` VALUES (51, 'Egypt', NULL, NULL);
INSERT INTO `countries` VALUES (52, 'El Salvador', NULL, NULL);
INSERT INTO `countries` VALUES (53, 'Equatorial Guinea', NULL, NULL);
INSERT INTO `countries` VALUES (54, 'Eritrea', NULL, NULL);
INSERT INTO `countries` VALUES (55, 'Estonia', NULL, NULL);
INSERT INTO `countries` VALUES (56, 'Ethiopia', NULL, NULL);
INSERT INTO `countries` VALUES (57, 'Fiji', NULL, NULL);
INSERT INTO `countries` VALUES (58, 'Finland', NULL, NULL);
INSERT INTO `countries` VALUES (59, 'France', NULL, NULL);
INSERT INTO `countries` VALUES (60, 'Gabon', NULL, NULL);
INSERT INTO `countries` VALUES (61, 'Gambia', NULL, NULL);
INSERT INTO `countries` VALUES (62, 'Georgia', NULL, NULL);
INSERT INTO `countries` VALUES (63, 'Germany', NULL, NULL);
INSERT INTO `countries` VALUES (64, 'Ghana', NULL, NULL);
INSERT INTO `countries` VALUES (65, 'Greece', NULL, NULL);
INSERT INTO `countries` VALUES (66, 'Grenada', NULL, NULL);
INSERT INTO `countries` VALUES (67, 'Guatemala', NULL, NULL);
INSERT INTO `countries` VALUES (68, 'Guinea', NULL, NULL);
INSERT INTO `countries` VALUES (69, 'Guinea-Bissau', NULL, NULL);
INSERT INTO `countries` VALUES (70, 'Guyana', NULL, NULL);
INSERT INTO `countries` VALUES (71, 'Haiti', NULL, NULL);
INSERT INTO `countries` VALUES (72, 'Honduras', NULL, NULL);
INSERT INTO `countries` VALUES (73, 'Hungary', NULL, NULL);
INSERT INTO `countries` VALUES (74, 'Iceland', NULL, NULL);
INSERT INTO `countries` VALUES (75, 'India', NULL, NULL);
INSERT INTO `countries` VALUES (76, 'Indonesia', NULL, NULL);
INSERT INTO `countries` VALUES (77, 'Iran', NULL, NULL);
INSERT INTO `countries` VALUES (78, 'Iraq', NULL, NULL);
INSERT INTO `countries` VALUES (79, 'Ireland', NULL, NULL);
INSERT INTO `countries` VALUES (80, 'Israel', NULL, NULL);
INSERT INTO `countries` VALUES (81, 'Italy', NULL, NULL);
INSERT INTO `countries` VALUES (82, 'Jamaica', NULL, NULL);
INSERT INTO `countries` VALUES (83, 'Japan', NULL, NULL);
INSERT INTO `countries` VALUES (84, 'Jordan', NULL, NULL);
INSERT INTO `countries` VALUES (85, 'Kazakhstan', NULL, NULL);
INSERT INTO `countries` VALUES (86, 'Kenya', NULL, NULL);
INSERT INTO `countries` VALUES (87, 'Kiribati', NULL, NULL);
INSERT INTO `countries` VALUES (88, 'North Korea', NULL, NULL);
INSERT INTO `countries` VALUES (89, 'South Korea', NULL, NULL);
INSERT INTO `countries` VALUES (90, 'Kosovo', NULL, NULL);
INSERT INTO `countries` VALUES (91, 'Kuwait', NULL, NULL);
INSERT INTO `countries` VALUES (92, 'Kyrgyzstan', NULL, NULL);
INSERT INTO `countries` VALUES (93, 'Laos', NULL, NULL);
INSERT INTO `countries` VALUES (94, 'Latvia', NULL, NULL);
INSERT INTO `countries` VALUES (95, 'Lebanon', NULL, NULL);
INSERT INTO `countries` VALUES (96, 'Lesotho', NULL, NULL);
INSERT INTO `countries` VALUES (97, 'Liberia', NULL, NULL);
INSERT INTO `countries` VALUES (98, 'Libya', NULL, NULL);
INSERT INTO `countries` VALUES (99, 'Liechtenstein', NULL, NULL);
INSERT INTO `countries` VALUES (100, 'Lithuania', NULL, NULL);
INSERT INTO `countries` VALUES (101, 'Luxembourg', NULL, NULL);
INSERT INTO `countries` VALUES (102, 'Macedonia', NULL, NULL);
INSERT INTO `countries` VALUES (103, 'Madagascar', NULL, NULL);
INSERT INTO `countries` VALUES (104, 'Malawi', NULL, NULL);
INSERT INTO `countries` VALUES (105, 'Malaysia', NULL, NULL);
INSERT INTO `countries` VALUES (106, 'Maldives', NULL, NULL);
INSERT INTO `countries` VALUES (107, 'Mali', NULL, NULL);
INSERT INTO `countries` VALUES (108, 'Malta', NULL, NULL);
INSERT INTO `countries` VALUES (109, 'Marshall Islands', NULL, NULL);
INSERT INTO `countries` VALUES (110, 'Mauritania', NULL, NULL);
INSERT INTO `countries` VALUES (111, 'Mauritius', NULL, NULL);
INSERT INTO `countries` VALUES (112, 'Mexico', NULL, NULL);
INSERT INTO `countries` VALUES (113, 'Micronesia', NULL, NULL);
INSERT INTO `countries` VALUES (114, 'Moldova', NULL, NULL);
INSERT INTO `countries` VALUES (115, 'Monaco', NULL, NULL);
INSERT INTO `countries` VALUES (116, 'Mongolia', NULL, NULL);
INSERT INTO `countries` VALUES (117, 'Montenegro', NULL, NULL);
INSERT INTO `countries` VALUES (118, 'Morocco', NULL, NULL);
INSERT INTO `countries` VALUES (119, 'Mozambique', NULL, NULL);
INSERT INTO `countries` VALUES (120, 'Myanmar (Burma)', NULL, NULL);
INSERT INTO `countries` VALUES (121, 'Namibia', NULL, NULL);
INSERT INTO `countries` VALUES (122, 'Nauru', NULL, NULL);
INSERT INTO `countries` VALUES (123, 'Nepal', NULL, NULL);
INSERT INTO `countries` VALUES (124, 'Netherlands', NULL, NULL);
INSERT INTO `countries` VALUES (125, 'New Zealand', NULL, NULL);
INSERT INTO `countries` VALUES (126, 'Nicaragua', NULL, NULL);
INSERT INTO `countries` VALUES (127, 'Niger', NULL, NULL);
INSERT INTO `countries` VALUES (128, 'Nigeria', NULL, NULL);
INSERT INTO `countries` VALUES (129, 'Norway', NULL, NULL);
INSERT INTO `countries` VALUES (130, 'Oman', NULL, NULL);
INSERT INTO `countries` VALUES (131, 'Pakistan', NULL, NULL);
INSERT INTO `countries` VALUES (132, 'Palau', NULL, NULL);
INSERT INTO `countries` VALUES (133, 'Panama', NULL, NULL);
INSERT INTO `countries` VALUES (134, 'Papua New Guinea', NULL, NULL);
INSERT INTO `countries` VALUES (135, 'Paraguay', NULL, NULL);
INSERT INTO `countries` VALUES (136, 'Peru', NULL, NULL);
INSERT INTO `countries` VALUES (137, 'Philippines', NULL, NULL);
INSERT INTO `countries` VALUES (138, 'Poland', NULL, NULL);
INSERT INTO `countries` VALUES (139, 'Portugal', NULL, NULL);
INSERT INTO `countries` VALUES (140, 'Qatar', NULL, NULL);
INSERT INTO `countries` VALUES (141, 'Romania', NULL, NULL);
INSERT INTO `countries` VALUES (142, 'Russia', NULL, NULL);
INSERT INTO `countries` VALUES (143, 'Rwanda', NULL, NULL);
INSERT INTO `countries` VALUES (144, 'Saint Kitts and Nevis', NULL, NULL);
INSERT INTO `countries` VALUES (145, 'Saint Lucia', NULL, NULL);
INSERT INTO `countries` VALUES (146, 'Saint Vincent and the Grenadines', NULL, NULL);
INSERT INTO `countries` VALUES (147, 'Samoa', NULL, NULL);
INSERT INTO `countries` VALUES (148, 'San Marino', NULL, NULL);
INSERT INTO `countries` VALUES (149, 'Sao Tome and Principe', NULL, NULL);
INSERT INTO `countries` VALUES (150, 'Saudi Arabia', NULL, NULL);
INSERT INTO `countries` VALUES (151, 'Senegal', NULL, NULL);
INSERT INTO `countries` VALUES (152, 'Serbia', NULL, NULL);
INSERT INTO `countries` VALUES (153, 'Seychelles', NULL, NULL);
INSERT INTO `countries` VALUES (154, 'Sierra Leone', NULL, NULL);
INSERT INTO `countries` VALUES (155, 'Singapore', NULL, NULL);
INSERT INTO `countries` VALUES (156, 'Sint Maarten', NULL, NULL);
INSERT INTO `countries` VALUES (157, 'Slovakia', NULL, NULL);
INSERT INTO `countries` VALUES (158, 'Slovenia', NULL, NULL);
INSERT INTO `countries` VALUES (159, 'Solomon Islands', NULL, NULL);
INSERT INTO `countries` VALUES (160, 'Somalia', NULL, NULL);
INSERT INTO `countries` VALUES (161, 'South Africa', NULL, NULL);
INSERT INTO `countries` VALUES (162, 'South Sudan', NULL, NULL);
INSERT INTO `countries` VALUES (163, 'Spain', NULL, NULL);
INSERT INTO `countries` VALUES (164, 'Sri Lanka', NULL, NULL);
INSERT INTO `countries` VALUES (165, 'Sudan', NULL, NULL);
INSERT INTO `countries` VALUES (166, 'Suriname', NULL, NULL);
INSERT INTO `countries` VALUES (167, 'Swaziland', NULL, NULL);
INSERT INTO `countries` VALUES (168, 'Sweden', NULL, NULL);
INSERT INTO `countries` VALUES (169, 'Switzerland', NULL, NULL);
INSERT INTO `countries` VALUES (170, 'Syria', NULL, NULL);
INSERT INTO `countries` VALUES (171, 'Tajikistan', NULL, NULL);
INSERT INTO `countries` VALUES (172, 'Tanzania', NULL, NULL);
INSERT INTO `countries` VALUES (173, 'Thailand', NULL, NULL);
INSERT INTO `countries` VALUES (174, 'Timor-Leste', NULL, NULL);
INSERT INTO `countries` VALUES (175, 'Togo', NULL, NULL);
INSERT INTO `countries` VALUES (176, 'Tonga', NULL, NULL);
INSERT INTO `countries` VALUES (177, 'Trinidad and Tobago', NULL, NULL);
INSERT INTO `countries` VALUES (178, 'Tunisia', NULL, NULL);
INSERT INTO `countries` VALUES (179, 'Turkey', NULL, NULL);
INSERT INTO `countries` VALUES (180, 'Turkmenistan', NULL, NULL);
INSERT INTO `countries` VALUES (181, 'Tuvalu', NULL, NULL);
INSERT INTO `countries` VALUES (182, 'Uganda', NULL, NULL);
INSERT INTO `countries` VALUES (183, 'Ukraine', NULL, NULL);
INSERT INTO `countries` VALUES (184, 'United Arab Emirates', NULL, NULL);
INSERT INTO `countries` VALUES (185, 'United Kingdom', NULL, NULL);
INSERT INTO `countries` VALUES (186, 'United States', NULL, NULL);
INSERT INTO `countries` VALUES (187, 'Uruguay', NULL, NULL);
INSERT INTO `countries` VALUES (188, 'Uzbekistan', NULL, NULL);
INSERT INTO `countries` VALUES (189, 'Vanuatu', NULL, NULL);
INSERT INTO `countries` VALUES (190, 'Vatican City', NULL, NULL);
INSERT INTO `countries` VALUES (191, 'Venezuela', NULL, NULL);
INSERT INTO `countries` VALUES (192, 'Vietnam', NULL, NULL);
INSERT INTO `countries` VALUES (193, 'Yemen', NULL, NULL);
INSERT INTO `countries` VALUES (194, 'Zambia', NULL, NULL);
INSERT INTO `countries` VALUES (195, 'Zimbabwe', NULL, NULL);

-- ----------------------------
-- Table structure for currencies
-- ----------------------------
DROP TABLE IF EXISTS `currencies`;
CREATE TABLE `currencies`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `rate` decimal(8, 4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of currencies
-- ----------------------------
INSERT INTO `currencies` VALUES (1, 'USD', 'US Dollar', '$', 1.0000, '2024-12-12 12:46:22', '2024-12-12 12:46:22');
INSERT INTO `currencies` VALUES (2, 'EUR', 'Euro', '€', 0.8500, '2024-12-12 12:46:22', '2024-12-12 12:46:22');

-- ----------------------------
-- Table structure for customer_agreements
-- ----------------------------
DROP TABLE IF EXISTS `customer_agreements`;
CREATE TABLE `customer_agreements`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint UNSIGNED NOT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `file_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `customer_agreements_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `customer_agreements_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 52 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of customer_agreements
-- ----------------------------
INSERT INTO `customer_agreements` VALUES (9, 1, 'lease', 'wesdaf', 'uploads/Docebo-SF - Copy-lvWeiyRD.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-02-27 02:40:02', '2025-02-27 02:40:02', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (10, 1, 'lease', 'wesdaf', 'uploads/Docebo-SF-0B7pspNO.docx', 'Docebo-SF.docx', 'Accepted', '2025-02-27 02:40:03', '2025-02-27 02:40:03', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (11, 1, 'lease', 'test', 'uploads/Docebo-SF-QTXUpMLa.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:05:59', '2025-03-03 12:05:59', '2');
INSERT INTO `customer_agreements` VALUES (12, 1, 'exclusivity', 'Hello', 'uploads/Docebo-SF-rfIfhxrO.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:09:20', '2025-03-03 12:09:20', 'Chatsworth');
INSERT INTO `customer_agreements` VALUES (13, 1, 'license', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-q4PC6r2A.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:12:01', '2025-03-03 12:12:01', 'Los Angeles');
INSERT INTO `customer_agreements` VALUES (14, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-jL3j0DQ3.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:12:46', '2025-03-03 12:12:46', 'West Jordan');
INSERT INTO `customer_agreements` VALUES (15, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-W0fxGMqC.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:14:56', '2025-03-03 12:14:56', 'Doha');
INSERT INTO `customer_agreements` VALUES (16, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-mEmd33Qr.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:14:56', '2025-03-03 12:14:56', 'Doha');
INSERT INTO `customer_agreements` VALUES (17, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-ErajfCn3.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:15:08', '2025-03-03 12:15:08', 'Doha');
INSERT INTO `customer_agreements` VALUES (18, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-W2UFIGXl.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:15:08', '2025-03-03 12:15:08', 'Doha');
INSERT INTO `customer_agreements` VALUES (19, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-xxuyQLKd.rar', 'Docebo Report - Copy.rar', 'Accepted', '2025-03-03 12:15:26', '2025-03-03 12:15:26', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (20, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-2SToy9b5.rar', 'Docebo Report - Copy.rar', 'Accepted', '2025-03-03 12:15:41', '2025-03-03 12:15:41', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (21, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-pHW94Tb4.rar', 'Docebo Report - Copy.rar', 'Accepted', '2025-03-03 12:15:47', '2025-03-03 12:15:47', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (22, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-fZv6ZPuv.rar', 'Docebo Report - Copy.rar', 'Accepted', '2025-03-03 12:16:14', '2025-03-03 12:16:14', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (23, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-iiQx9aUN.rar', 'Docebo Report - Copy.rar', 'Accepted', '2025-03-03 12:16:16', '2025-03-03 12:16:16', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (24, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-B7ClrbZX.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:19:35', '2025-03-03 12:19:35', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (25, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-FhZe164E.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:19:35', '2025-03-03 12:19:35', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (26, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-QjBIsrFu.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:20:09', '2025-03-03 12:20:09', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (27, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-RVtmvqq5.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:20:09', '2025-03-03 12:20:09', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (28, 1, 'lease', 'Docebo Report.docx', 'uploads/Docebo Report-kxTHScYh.docx', 'Docebo Report.docx', 'Accepted', '2025-03-03 12:22:34', '2025-03-03 12:22:34', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (29, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-G3RszUcq.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:22:34', '2025-03-03 12:22:34', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (30, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-hVzRutKv.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:22:34', '2025-03-03 12:22:34', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (31, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-Nv6qESSa.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 12:49:39', '2025-03-03 12:49:39', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (32, 1, 'lease', 'Docebo-SF.docx', 'uploads/Docebo-SF-CXoKhDiv.docx', 'Docebo-SF.docx', 'Accepted', '2025-03-03 12:49:39', '2025-03-03 12:49:39', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (38, 1, 'lease', 'Test', 'uploads/Docebo Report - Copy-wIaIAM0F.rar', 'Docebo Report - Copy.rar', 'Active', '2025-03-03 13:13:55', '2025-03-04 11:24:51', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (39, 1, 'lease', 'Test', 'uploads/Docebo Report-Lc04hO0g.docx', 'Docebo Report.docx', 'Accepted', '2025-03-03 13:13:55', '2025-03-03 13:13:55', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (40, 1, 'lease', 'Test', 'uploads/Docebo-SF - Copy-rEbcFwHC.docx', 'Docebo-SF - Copy.docx', 'Accepted', '2025-03-03 13:13:55', '2025-03-03 13:13:55', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (43, 1, 'lease', 'testse', 'uploads/Docebo-SF-2bGoYWrt.docx', 'Docebo-SF.docx', 'Active', '2025-03-03 13:16:00', '2025-03-04 11:24:42', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (45, 1, 'lease', 'Docebo-SF - Copy.docx', 'uploads/Docebo-SF - Copy-aZm05xQ2.docx', 'Docebo-SF - Copy.docx', 'Active', '2025-03-03 13:19:11', '2025-03-04 11:24:36', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (46, 1, 'exclusivity', 'Docebo Report - Copy.rar', 'uploads/Docebo Report - Copy-do0F5rMU.rar', 'Docebo Report - Copy.rar', 'Active', '2025-03-03 13:19:35', '2025-03-04 01:19:55', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (50, 1, 'exclusivity', 'asdfasdf', 'uploads/Docebo Report-Q7xrtELa.docx', 'Docebo Report.docx', 'Inactive', '2025-03-03 22:12:34', '2025-03-03 22:12:34', 'BOULOGNE BILLANCOURT');
INSERT INTO `customer_agreements` VALUES (51, 1, 'exclusivity', 'asdfasdf', 'uploads/Docebo-SF - Copy-nlCHQ4a4.docx', 'Docebo-SF - Copy.docx', 'Inactive', '2025-03-03 22:12:35', '2025-03-04 00:22:31', 'BOULOGNE BILLANCOURT');

-- ----------------------------
-- Table structure for customers
-- ----------------------------
DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `owner_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `state_id` bigint UNSIGNED NULL DEFAULT NULL,
  `country_id` bigint UNSIGNED NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `shipping_state_id` bigint UNSIGNED NULL DEFAULT NULL,
  `shipping_country_id` bigint UNSIGNED NULL DEFAULT NULL,
  `shipping_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `shipping_city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `shipping_zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `shipping_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `use_billing_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `customers_owner_id_foreign`(`owner_id` ASC) USING BTREE,
  INDEX `customers_state_id_foreign`(`state_id` ASC) USING BTREE,
  INDEX `customers_country_id_foreign`(`country_id` ASC) USING BTREE,
  INDEX `customers_shipping_state_id_foreign`(`shipping_state_id` ASC) USING BTREE,
  INDEX `customers_shipping_country_id_foreign`(`shipping_country_id` ASC) USING BTREE,
  CONSTRAINT `customers_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `customers_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `customers_shipping_country_id_foreign` FOREIGN KEY (`shipping_country_id`) REFERENCES `countries` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `customers_shipping_state_id_foreign` FOREIGN KEY (`shipping_state_id`) REFERENCES `states` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `customers_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of customers
-- ----------------------------
INSERT INTO `customers` VALUES (1, 2, 'Lagree Studio', 'USA', NULL, '2024-12-12 04:51:42', '2024-12-30 07:37:44', 5, 91, '9340 Eaton Ave', 'Chatsworth', '91311', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (2, 3, 'Megaformer', 'USA', '2024-12-26 07:39:33', '2024-12-18 08:21:01', '2024-12-26 07:39:33', NULL, NULL, '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (3, 4, 'Company?', 'International', '2024-12-26 07:39:33', '2024-12-21 14:38:09', '2024-12-26 07:39:33', NULL, NULL, '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (4, 5, 'Test Customer', 'USA', '2024-12-26 07:39:33', '2024-12-23 07:06:32', '2024-12-26 07:39:33', NULL, NULL, '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (5, 6, 'JD DEVELOPMENT 5', 'International', NULL, '2024-12-26 08:35:41', '2025-01-03 18:25:31', NULL, 59, 'Siren  ***********, 5, rue Claude Monet', 'BOULOGNE BILLANCOURT', '92100', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (6, 7, 'Honour Wholeness', 'International', NULL, '2024-12-26 12:10:13', '2024-12-31 04:20:55', 5, 140, 'Zone 6 Old Al-Ghanim, street 810 Al-Fardan Center 3rd Floor', 'Doha', 'na', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (7, 8, 'Caitlyn XX', 'International', NULL, '2024-12-27 11:12:58', '2024-12-30 06:03:51', 5, 152, 'Neka ulica', 'Novi Sad', '91311', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (8, 9, 'Luka Anđelković', 'USA', NULL, '2024-12-30 06:40:40', '2024-12-30 09:33:41', 4, 152, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (9, 10, 'Luka Test', 'USA', '2024-12-30 07:07:54', '2024-12-30 07:07:36', '2024-12-30 07:07:54', 6, 152, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (10, 11, 'test', 'International', '2024-12-30 09:33:46', '2024-12-30 07:08:27', '2024-12-30 09:33:46', 6, 155, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (11, 12, 'Luka Anđelković', 'International', '2025-01-08 05:24:31', '2024-12-31 06:22:53', '2025-01-08 05:24:31', NULL, 152, 'qweqweqwe', 'Niš', '18000', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (12, 16, 'Tes', 'USA', '2025-01-08 12:36:44', '2024-12-31 08:40:56', '2025-01-08 12:36:44', 4, NULL, 'tst', 'test', 'test', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (13, 17, 'TEst', 'International', '2025-01-08 12:36:44', '2024-12-31 08:41:59', '2025-01-08 12:36:44', NULL, 5, 'tea', 'test', 'test', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (14, 18, 'Test 123', 'USA', NULL, '2024-12-31 08:42:21', '2024-12-31 08:42:21', 5, NULL, 'Neka ulica', 'Los Angeles', '91311', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (15, 21, 'test kekacbre', 'International', NULL, '2024-12-31 09:42:20', '2025-01-03 11:30:40', 49, 152, '543 Toby Greens', 'West Jordan', '86063', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (16, 22, 'My Licencee', 'USA', NULL, '2024-12-31 10:24:33', '2024-12-31 10:24:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (17, 23, 'Top Licensee', 'USA', NULL, '2024-12-31 10:26:55', '2025-01-04 02:44:07', 4, NULL, 'test', 'test', '112233', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (18, 24, 'CORE', 'USA', NULL, '2025-01-03 14:57:41', '2025-01-03 14:57:41', 30, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (19, 25, 'Lagree Room', 'International', '2025-01-08 10:20:44', '2025-01-06 11:49:29', '2025-01-08 10:20:44', NULL, 31, '19979 76th Ave. Unit 115', 'Langley, BC', 'V2Y 3J3', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (20, 26, 'Shania Spinka', 'USA', NULL, '2025-01-08 12:37:10', '2025-01-08 12:37:10', 13, NULL, '365 Shields View', 'North Port', '61128', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (21, 27, 'Kelly Zulauf', 'International', NULL, '2025-01-08 12:37:18', '2025-01-09 06:42:35', 42, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (22, 32, 'asdasdasd', 'USA', NULL, '2025-01-09 06:43:05', '2025-01-09 06:43:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `customers` VALUES (23, 33, 'Yukio_licensee', 'International', NULL, '2025-03-05 08:21:16', '2025-03-05 08:21:16', NULL, 138, 'Voivodeship, 20', 'Nisko', '37400', NULL, NULL, NULL, NULL, NULL, 'International', '1');

-- ----------------------------
-- Table structure for email_templates
-- ----------------------------
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `keywords` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `unique_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email_templates_unique_name_unique`(`unique_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of email_templates
-- ----------------------------

-- ----------------------------
-- Table structure for failed_jobs
-- ----------------------------
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `failed_jobs_uuid_unique`(`uuid` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of failed_jobs
-- ----------------------------
INSERT INTO `failed_jobs` VALUES (1, '232092ff-06bd-456c-a732-f790c1ba9ec1', 'database', 'default', '{\"uuid\":\"232092ff-06bd-456c-a732-f790c1ba9ec1\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-16 19:02:36');
INSERT INTO `failed_jobs` VALUES (2, '73864200-1ac1-4ba9-b2c5-2473d6c77ab2', 'database', 'default', '{\"uuid\":\"73864200-1ac1-4ba9-b2c5-2473d6c77ab2\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"payments\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-16 19:04:35');
INSERT INTO `failed_jobs` VALUES (3, '8f8b0d6f-15e6-4db0-a8f2-d8d1214f6663', 'database', 'default', '{\"uuid\":\"8f8b0d6f-15e6-4db0-a8f2-d8d1214f6663\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-24 09:17:00');
INSERT INTO `failed_jobs` VALUES (4, '94964a66-8b84-4b19-a0d1-3d01ea6280fa', 'database', 'default', '{\"uuid\":\"94964a66-8b84-4b19-a0d1-3d01ea6280fa\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:14:\\\"lease template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:16:\\\"App\\\\Models\\\\Lease\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-24 09:22:17');
INSERT INTO `failed_jobs` VALUES (5, '44a60269-677f-4e48-ac8e-6c3b41371eea', 'database', 'default', '{\"uuid\":\"44a60269-677f-4e48-ac8e-6c3b41371eea\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 14:49:32');
INSERT INTO `failed_jobs` VALUES (6, '782c51f3-aa47-43dd-9f45-8efa0301e3d2', 'database', 'default', '{\"uuid\":\"782c51f3-aa47-43dd-9f45-8efa0301e3d2\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:4;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 16:19:04');
INSERT INTO `failed_jobs` VALUES (7, '486a7649-30fd-4ad7-b862-f9cf76d16cec', 'database', 'default', '{\"uuid\":\"486a7649-30fd-4ad7-b862-f9cf76d16cec\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:5;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 17:30:09');
INSERT INTO `failed_jobs` VALUES (8, '8211c0f7-2620-437d-a346-c75916abc754', 'database', 'default', '{\"uuid\":\"8211c0f7-2620-437d-a346-c75916abc754\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"payments\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 17:30:44');

-- ----------------------------
-- Table structure for fees
-- ----------------------------
DROP TABLE IF EXISTS `fees`;
CREATE TABLE `fees`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of fees
-- ----------------------------

-- ----------------------------
-- Table structure for files
-- ----------------------------
DROP TABLE IF EXISTS `files`;
CREATE TABLE `files`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `fileable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `fileable_id` bigint NOT NULL,
  `hash_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `extension` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of files
-- ----------------------------
INSERT INTO `files` VALUES (1, 'App\\Models\\Company', 1, '6762c4ece8d03_3I4rxeodJW.jpg', 'Group 15956.jpg', 'jpg', 'admin/companies/1/6762c4ece8d03_3I4rxeodJW.jpg', 'image', '2024-12-16 05:56:12', '2024-12-18 05:49:48');
INSERT INTO `files` VALUES (2, 'App\\Models\\Company', 1, '67602384e0109_rHzUmUeuhr.png', 'test2.png', 'png', 'admin/companies/1/67602384e0109_rHzUmUeuhr.png', 'image', '2024-12-16 05:56:36', '2024-12-16 05:56:36');
INSERT INTO `files` VALUES (3, 'App\\Models\\Company', 1, '676026deb3a3d_diBgXudZWj.png', 'test2.png', 'png', 'admin/companies/1/676026deb3a3d_diBgXudZWj.png', 'image', '2024-12-16 06:10:54', '2024-12-16 06:10:54');
INSERT INTO `files` VALUES (4, 'App\\Models\\Company', 1, '676026f4bf797_5lDD2e6j4F.png', 'test2.png', 'png', 'admin/companies/1/676026f4bf797_5lDD2e6j4F.png', 'image', '2024-12-16 06:11:16', '2024-12-16 06:11:16');
INSERT INTO `files` VALUES (6, 'App\\Models\\Company', 3, '6762c51cb6980_pNAZ8miaqJ.jpg', 'lagree-logo.jpg', 'jpg', 'admin/companies/3/6762c51cb6980_pNAZ8miaqJ.jpg', 'image', '2024-12-18 05:50:36', '2024-12-18 05:50:36');
INSERT INTO `files` VALUES (7, 'App\\Models\\Company', 2, '6796a6edc0497_bLbiv41Ult.jpg', 'logo-ims-invoice.jpg', 'jpg', 'admin/companies/2/6796a6edc0497_bLbiv41Ult.jpg', 'image', '2025-01-26 13:19:41', '2025-01-26 13:19:41');
INSERT INTO `files` VALUES (8, 'App\\Models\\Company', 5, '67c87979457f5_NL6FiDZeBG.png', 'Screenshot_47.png', 'png', 'admin/companies/5/67c87979457f5_NL6FiDZeBG.png', 'image', '2025-03-05 08:19:05', '2025-03-05 08:19:05');

-- ----------------------------
-- Table structure for invoice_product_items
-- ----------------------------
DROP TABLE IF EXISTS `invoice_product_items`;
CREATE TABLE `invoice_product_items`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `invoice_product_id` bigint UNSIGNED NOT NULL,
  `product_id` bigint UNSIGNED NOT NULL,
  `price` decimal(8, 2) NOT NULL,
  `quantity` int NOT NULL,
  `discount` decimal(8, 2) NOT NULL,
  `discount_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `invoice_product_items_invoice_product_id_foreign`(`invoice_product_id` ASC) USING BTREE,
  INDEX `invoice_product_items_product_id_foreign`(`product_id` ASC) USING BTREE,
  CONSTRAINT `invoice_product_items_invoice_product_id_foreign` FOREIGN KEY (`invoice_product_id`) REFERENCES `invoice_products` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `invoice_product_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 122 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invoice_product_items
-- ----------------------------
INSERT INTO `invoice_product_items` VALUES (84, 27, 112, 260.00, 1, 0.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (85, 27, 113, 90.00, 1, 0.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (86, 27, 114, 80.00, 2, 0.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (87, 27, 115, 100.00, 2, 0.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (88, 27, 116, 90.00, 1, 0.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (89, 27, 213, 222.00, 1, 20.00, '$', '2025-01-26 04:23:16', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (90, 27, 215, 22.00, 1, 0.00, '$', '2025-01-26 05:36:30', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (91, 27, 216, 21.00, 1, 0.00, '$', '2025-01-26 05:36:49', '2025-01-26 05:36:49');
INSERT INTO `invoice_product_items` VALUES (92, 28, 112, 260.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (93, 28, 113, 90.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (94, 28, 117, 250.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (95, 28, 118, 60.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (96, 28, 119, 150.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (97, 28, 114, 80.00, 2, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (98, 28, 115, 100.00, 2, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (99, 28, 120, 90.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (100, 28, 116, 90.00, 1, 0.00, '$', '2025-01-27 05:49:36', '2025-01-27 05:49:36');
INSERT INTO `invoice_product_items` VALUES (101, 29, 126, 60.00, 1, 0.00, '$', '2025-02-12 10:55:55', '2025-02-12 10:55:55');
INSERT INTO `invoice_product_items` VALUES (102, 29, 136, 60.00, 6, 0.00, '$', '2025-02-12 10:55:55', '2025-02-12 10:55:55');
INSERT INTO `invoice_product_items` VALUES (103, 29, 132, 80.00, 3, 0.00, '$', '2025-02-12 10:55:55', '2025-02-12 10:55:55');
INSERT INTO `invoice_product_items` VALUES (104, 30, 163, 20.00, 7, 0.00, '$', '2025-02-12 12:57:17', '2025-02-12 12:57:17');
INSERT INTO `invoice_product_items` VALUES (105, 30, 131, 70.00, 3, 0.00, '$', '2025-02-12 12:57:17', '2025-02-12 12:57:17');
INSERT INTO `invoice_product_items` VALUES (106, 30, 172, 60.00, 5, 0.00, '$', '2025-02-12 12:57:17', '2025-02-12 12:57:17');
INSERT INTO `invoice_product_items` VALUES (107, 30, 242, 222.00, 8, 0.00, '$', '2025-02-12 12:57:17', '2025-02-12 12:57:17');
INSERT INTO `invoice_product_items` VALUES (108, 31, 163, 20.00, 7, 0.00, '$', '2025-02-12 13:16:07', '2025-02-12 13:16:07');
INSERT INTO `invoice_product_items` VALUES (109, 31, 131, 70.00, 3, 0.00, '$', '2025-02-12 13:16:08', '2025-02-12 13:16:08');
INSERT INTO `invoice_product_items` VALUES (110, 31, 172, 60.00, 5, 0.00, '$', '2025-02-12 13:16:08', '2025-02-12 13:16:08');
INSERT INTO `invoice_product_items` VALUES (111, 31, 242, 222.00, 8, 0.00, '$', '2025-02-12 13:16:08', '2025-02-12 13:16:08');
INSERT INTO `invoice_product_items` VALUES (112, 32, 136, 60.00, 6, 0.00, '$', '2025-02-12 23:08:49', '2025-02-12 23:08:49');
INSERT INTO `invoice_product_items` VALUES (113, 32, 132, 80.00, 3, 0.00, '$', '2025-02-12 23:08:49', '2025-02-12 23:08:49');
INSERT INTO `invoice_product_items` VALUES (114, 32, 172, 60.00, 5, 0.00, '$', '2025-02-12 23:08:49', '2025-02-12 23:08:49');
INSERT INTO `invoice_product_items` VALUES (115, 32, 242, 222.00, 8, 0.00, '$', '2025-02-12 23:08:49', '2025-02-12 23:08:49');
INSERT INTO `invoice_product_items` VALUES (116, 32, 163, 20.00, 7, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');
INSERT INTO `invoice_product_items` VALUES (117, 32, 131, 70.00, 3, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');
INSERT INTO `invoice_product_items` VALUES (118, 32, 172, 60.00, 5, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');
INSERT INTO `invoice_product_items` VALUES (119, 32, 242, 222.00, 8, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');
INSERT INTO `invoice_product_items` VALUES (120, 32, 136, 60.00, 6, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');
INSERT INTO `invoice_product_items` VALUES (121, 32, 132, 80.00, 31, 0.00, '$', '2025-02-12 23:08:50', '2025-02-12 23:08:50');

-- ----------------------------
-- Table structure for invoice_products
-- ----------------------------
DROP TABLE IF EXISTS `invoice_products`;
CREATE TABLE `invoice_products`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `number` int UNSIGNED NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'awaiting',
  `company_id` bigint UNSIGNED NOT NULL,
  `customer_id` bigint UNSIGNED NOT NULL,
  `send_date` date NOT NULL,
  `sent_date` date NULL DEFAULT NULL,
  `shipping_fee` decimal(8, 2) NULL DEFAULT NULL,
  `handling_fee` decimal(8, 2) NULL DEFAULT NULL,
  `show_tax` int NOT NULL,
  `paid` int NOT NULL DEFAULT 0,
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `reminder_date` date NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `invoice_products_number_unique`(`number` ASC) USING BTREE,
  INDEX `invoice_products_company_id_foreign`(`company_id` ASC) USING BTREE,
  INDEX `invoice_products_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `invoice_products_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `invoice_products_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invoice_products
-- ----------------------------
INSERT INTO `invoice_products` VALUES (11, 1, 'sent', 1, 5, '2024-12-26', '2024-12-26', NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 08:39:10', '2024-12-26 08:41:44', NULL);
INSERT INTO `invoice_products` VALUES (12, 12, 'sent', 1, 1, '2024-12-26', '2024-12-26', NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 09:09:54', '2024-12-26 09:10:02', NULL);
INSERT INTO `invoice_products` VALUES (13, 13, 'awaiting', 1, 6, '2024-12-26', NULL, NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 12:14:42', '2024-12-26 12:14:42', NULL);
INSERT INTO `invoice_products` VALUES (14, 14, 'awaiting', 1, 1, '2024-12-30', NULL, NULL, NULL, 1, 0, NULL, NULL, '2024-12-30 08:20:22', '2024-12-30 08:22:34', NULL);
INSERT INTO `invoice_products` VALUES (15, 15, 'awaiting', 2, 11, '2024-12-31', NULL, NULL, NULL, 0, 0, NULL, NULL, '2024-12-31 06:23:33', '2024-12-31 06:23:33', NULL);
INSERT INTO `invoice_products` VALUES (16, 16, 'sent', 4, 15, '2025-01-03', '2025-01-22', 50.00, 112.00, 1, 0, 'test note', NULL, '2025-01-03 11:31:40', '2025-01-22 04:02:37', '2025-01-22');
INSERT INTO `invoice_products` VALUES (17, 17, 'sent', 1, 18, '2025-01-03', '2025-01-06', NULL, NULL, 0, 0, 'LEASE DEPOSIT', NULL, '2025-01-03 15:00:50', '2025-01-06 10:54:47', NULL);
INSERT INTO `invoice_products` VALUES (18, 18, 'awaiting', 1, 6, '2025-01-06', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-06 09:16:18', '2025-01-06 09:16:18', NULL);
INSERT INTO `invoice_products` VALUES (19, 19, 'sent', 1, 19, '2025-01-06', '2025-01-06', NULL, NULL, 0, 0, NULL, '2025-01-08 10:20:44', '2025-01-06 11:52:12', '2025-01-08 10:20:44', NULL);
INSERT INTO `invoice_products` VALUES (20, 20, 'sent', 3, 19, '2025-01-06', '2025-01-06', NULL, NULL, 0, 0, 'LICENSE POSTAL CODE: V2Y', '2025-01-08 10:20:44', '2025-01-06 12:04:35', '2025-01-08 10:20:44', NULL);
INSERT INTO `invoice_products` VALUES (21, 21, 'awaiting', 1, 16, '2025-01-07', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-07 09:28:09', '2025-01-07 09:28:09', NULL);
INSERT INTO `invoice_products` VALUES (22, 22, 'awaiting', 1, 16, '2025-01-07', NULL, 330.00, 22.00, 0, 0, NULL, NULL, '2025-01-07 09:33:42', '2025-01-13 07:42:26', NULL);
INSERT INTO `invoice_products` VALUES (23, 23, 'awaiting', 4, 8, '2025-01-15', NULL, 22.00, 33.00, 0, 0, 'test', NULL, '2025-01-15 15:52:18', '2025-01-15 15:52:18', NULL);
INSERT INTO `invoice_products` VALUES (24, 24, 'sent', 4, 15, '2025-01-17', '2025-01-17', 20.00, 10.00, 1, 1, NULL, NULL, '2025-01-17 04:00:02', '2025-01-17 15:26:50', '2025-01-17');
INSERT INTO `invoice_products` VALUES (25, 25, 'awaiting', 1, 22, '2025-01-22', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-22 01:53:34', '2025-01-22 10:18:26', NULL);
INSERT INTO `invoice_products` VALUES (26, 26, 'awaiting', 1, 7, '2025-01-22', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-22 02:47:05', '2025-01-22 02:47:05', NULL);
INSERT INTO `invoice_products` VALUES (27, 27, 'sent', 4, 15, '2025-01-22', '2025-01-22', 22.00, 33.00, 0, 1, NULL, NULL, '2025-01-22 02:54:26', '2025-01-22 04:13:24', NULL);
INSERT INTO `invoice_products` VALUES (28, 28, 'awaiting', 3, 22, '2025-01-27', NULL, 120.00, 5.00, 0, 0, NULL, NULL, '2025-01-27 05:49:36', '2025-01-27 05:49:36', NULL);
INSERT INTO `invoice_products` VALUES (29, 29, 'awaiting', 2, 7, '2025-02-12', NULL, 2.00, 3.00, 1, 0, 'asdfasd asdfa sdf', NULL, '2025-02-12 10:55:55', '2025-02-12 10:55:55', NULL);
INSERT INTO `invoice_products` VALUES (30, 30, 'awaiting', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, 'aaa', NULL, '2025-02-12 12:57:17', '2025-02-12 12:57:17', NULL);
INSERT INTO `invoice_products` VALUES (31, 31, 'awaiting', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, 'aaaa', NULL, '2025-02-12 13:16:07', '2025-02-12 13:16:07', NULL);
INSERT INTO `invoice_products` VALUES (32, 32, 'awaiting', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, 'Created from template', NULL, '2025-02-12 23:08:49', '2025-02-12 23:08:49', NULL);

-- ----------------------------
-- Table structure for invoice_template_products
-- ----------------------------
DROP TABLE IF EXISTS `invoice_template_products`;
CREATE TABLE `invoice_template_products`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `invoice_template_id` bigint UNSIGNED NOT NULL,
  `product_id` bigint UNSIGNED NOT NULL,
  `price` decimal(8, 2) NOT NULL,
  `quantity` int NOT NULL,
  `discount` decimal(8, 2) NOT NULL,
  `discount_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `invoice_template_products_invoice_product_id_foreign`(`invoice_template_id` ASC) USING BTREE,
  INDEX `invoice_template_products_product_id_foreign`(`product_id` ASC) USING BTREE,
  CONSTRAINT `invoice_template_products_invoice_product_id_foreign` FOREIGN KEY (`invoice_template_id`) REFERENCES `invoice_templates` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `invoice_template_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invoice_template_products
-- ----------------------------
INSERT INTO `invoice_template_products` VALUES (3, 3, 136, 60.00, 6, 0.00, '$', '2025-02-12 11:15:33', '2025-02-12 11:15:33');
INSERT INTO `invoice_template_products` VALUES (4, 3, 132, 80.00, 3, 0.00, '$', '2025-02-12 11:15:33', '2025-02-12 11:15:33');
INSERT INTO `invoice_template_products` VALUES (5, 3, 68, 35.00, 10, 0.00, '$', '2025-02-12 11:15:34', '2025-02-12 11:15:34');
INSERT INTO `invoice_template_products` VALUES (6, 3, 163, 20.00, 7, 0.00, '$', '2025-02-12 11:15:34', '2025-02-12 11:15:34');
INSERT INTO `invoice_template_products` VALUES (7, 3, 131, 70.00, 3, 0.00, '$', '2025-02-12 11:15:34', '2025-02-12 11:15:34');
INSERT INTO `invoice_template_products` VALUES (8, 3, 172, 60.00, 5, 0.00, '$', '2025-02-12 11:15:34', '2025-02-12 11:15:34');
INSERT INTO `invoice_template_products` VALUES (9, 3, 242, 222.00, 8, 0.00, '$', '2025-02-12 11:15:34', '2025-02-12 11:15:34');
INSERT INTO `invoice_template_products` VALUES (10, 4, 163, 20.00, 7, 0.00, '$', '2025-02-12 11:34:04', '2025-02-12 11:34:04');
INSERT INTO `invoice_template_products` VALUES (11, 4, 131, 70.00, 3, 0.00, '$', '2025-02-12 11:34:04', '2025-02-12 11:34:04');
INSERT INTO `invoice_template_products` VALUES (12, 4, 172, 60.00, 5, 0.00, '$', '2025-02-12 11:34:04', '2025-02-12 11:34:04');
INSERT INTO `invoice_template_products` VALUES (13, 4, 242, 222.00, 8, 0.00, '$', '2025-02-12 11:34:04', '2025-02-12 11:34:04');
INSERT INTO `invoice_template_products` VALUES (14, 5, 136, 60.00, 6, 0.00, '$', '2025-02-12 11:35:50', '2025-02-12 11:35:50');
INSERT INTO `invoice_template_products` VALUES (15, 5, 132, 80.00, 3, 0.00, '$', '2025-02-12 11:35:50', '2025-02-12 11:35:50');
INSERT INTO `invoice_template_products` VALUES (16, 6, 129, 230.00, 2, 0.00, '$', '2025-02-12 12:00:23', '2025-02-12 12:52:57');
INSERT INTO `invoice_template_products` VALUES (17, 6, 126, 60.00, 5, 0.00, '$', '2025-02-12 12:00:23', '2025-02-12 12:52:57');
INSERT INTO `invoice_template_products` VALUES (24, 6, 254, 123.00, 1, 0.00, '$', '2025-02-12 12:42:26', '2025-02-12 12:52:57');
INSERT INTO `invoice_template_products` VALUES (27, 7, 172, 60.00, 5, 0.00, '$', '2025-02-12 13:16:41', '2025-02-12 13:17:13');
INSERT INTO `invoice_template_products` VALUES (28, 7, 242, 222.00, 8, 0.00, '$', '2025-02-12 13:16:41', '2025-02-12 13:17:13');
INSERT INTO `invoice_template_products` VALUES (29, 8, 136, 60.00, 6, 0.00, '$', '2025-02-12 13:33:43', '2025-02-12 13:35:44');
INSERT INTO `invoice_template_products` VALUES (30, 8, 132, 80.00, 31, 0.00, '$', '2025-02-12 13:33:44', '2025-02-12 13:35:44');
INSERT INTO `invoice_template_products` VALUES (35, 9, 163, 10.00, 7, 0.00, '$', '2025-02-12 13:38:27', '2025-02-12 13:38:44');
INSERT INTO `invoice_template_products` VALUES (36, 9, 131, 170.00, 3, 0.00, '$', '2025-02-12 13:38:27', '2025-02-12 13:38:44');
INSERT INTO `invoice_template_products` VALUES (37, 9, 172, 160.00, 5, 0.00, '$', '2025-02-12 13:38:27', '2025-02-12 13:38:44');
INSERT INTO `invoice_template_products` VALUES (38, 9, 242, 1222.00, 8, 0.00, '$', '2025-02-12 13:38:27', '2025-02-12 13:38:44');
INSERT INTO `invoice_template_products` VALUES (39, 10, 136, 60.00, 6, 0.00, '$', '2025-02-12 23:09:24', '2025-02-12 23:09:24');
INSERT INTO `invoice_template_products` VALUES (40, 10, 132, 80.00, 3, 0.00, '$', '2025-02-12 23:09:24', '2025-02-12 23:09:24');
INSERT INTO `invoice_template_products` VALUES (41, 10, 163, 20.00, 7, 0.00, '$', '2025-02-12 23:09:24', '2025-02-12 23:09:24');
INSERT INTO `invoice_template_products` VALUES (42, 10, 131, 70.00, 3, 0.00, '$', '2025-02-12 23:09:24', '2025-02-12 23:09:24');
INSERT INTO `invoice_template_products` VALUES (43, 10, 172, 60.00, 5, 0.00, '$', '2025-02-12 23:09:25', '2025-02-12 23:09:25');
INSERT INTO `invoice_template_products` VALUES (44, 10, 242, 222.00, 8, 0.00, '$', '2025-02-12 23:09:25', '2025-02-12 23:09:25');
INSERT INTO `invoice_template_products` VALUES (45, 11, 172, 60.00, 5, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');
INSERT INTO `invoice_template_products` VALUES (46, 11, 242, 222.00, 8, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');
INSERT INTO `invoice_template_products` VALUES (47, 11, 163, 20.00, 7, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');
INSERT INTO `invoice_template_products` VALUES (48, 11, 131, 70.00, 3, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');
INSERT INTO `invoice_template_products` VALUES (49, 11, 172, 60.00, 5, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');
INSERT INTO `invoice_template_products` VALUES (50, 11, 242, 222.00, 8, 0.00, '$', '2025-02-19 06:37:34', '2025-02-19 06:37:34');

-- ----------------------------
-- Table structure for invoice_templates
-- ----------------------------
DROP TABLE IF EXISTS `invoice_templates`;
CREATE TABLE `invoice_templates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `number` int UNSIGNED NOT NULL,
  `status` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'awaiting',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'Invoice Template',
  `company_id` bigint UNSIGNED NULL DEFAULT NULL,
  `customer_id` bigint UNSIGNED NULL DEFAULT NULL,
  `send_date` date NULL DEFAULT NULL,
  `sent_date` date NULL DEFAULT NULL,
  `shipping_fee` decimal(8, 2) NULL DEFAULT NULL,
  `handling_fee` decimal(8, 2) NULL DEFAULT NULL,
  `show_tax` int NULL DEFAULT NULL,
  `paid` int NULL DEFAULT NULL,
  `note` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `invoice_templates_number_unique`(`number` ASC) USING BTREE,
  INDEX `invoice_templates_company_id_foreign`(`company_id` ASC) USING BTREE,
  INDEX `invoice_templates_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `invoice_templates_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `invoice_templates_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invoice_templates
-- ----------------------------
INSERT INTO `invoice_templates` VALUES (1, 1, 'awaiting', 'Invoice Template', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, 'asdfasd', '2025-02-12 11:58:50', '2025-02-12 11:08:41', '2025-02-12 11:58:50');
INSERT INTO `invoice_templates` VALUES (3, 2, 'awaiting', 'Invoice Template', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, NULL, '2025-02-12 11:59:06', '2025-02-12 11:15:33', '2025-02-12 11:59:06');
INSERT INTO `invoice_templates` VALUES (4, 4, 'awaiting', 'Invoice Template', 2, 22, '2025-02-12', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-02-12 11:34:04', '2025-02-12 11:34:04');
INSERT INTO `invoice_templates` VALUES (5, 5, 'awaiting', 'ghost', 1, 7, '2025-02-12', NULL, NULL, NULL, 0, 0, 'asdf aasdf asdf adsf asdf', NULL, '2025-02-12 11:35:50', '2025-02-12 11:35:50');
INSERT INTO `invoice_templates` VALUES (6, 6, 'awaiting', 'Test template123123', 4, 18, '2025-02-12', NULL, NULL, NULL, 0, 0, 'gost 123123', '2025-02-12 12:53:09', '2025-02-12 12:00:23', '2025-02-12 12:53:09');
INSERT INTO `invoice_templates` VALUES (7, 7, 'awaiting', 'invoice@@template', 1, 22, '2025-02-12', NULL, NULL, NULL, 0, 0, 'asdfasdf adsf adsf', NULL, '2025-02-12 13:16:41', '2025-02-12 13:17:13');
INSERT INTO `invoice_templates` VALUES (8, 8, 'awaiting', 'Test123', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ggasdts', NULL, '2025-02-12 13:33:43', '2025-02-12 13:35:44');
INSERT INTO `invoice_templates` VALUES (9, 9, 'awaiting', 'Correct', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'ghost', '2025-02-12 13:39:00', '2025-02-12 13:38:27', '2025-02-12 13:39:00');
INSERT INTO `invoice_templates` VALUES (10, 10, 'awaiting', 'Created From Old templates', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-12 23:09:24', '2025-02-12 23:09:24');
INSERT INTO `invoice_templates` VALUES (11, 11, 'awaiting', 'ttqss1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'asdfasd', NULL, '2025-02-19 06:37:34', '2025-02-19 06:37:34');

-- ----------------------------
-- Table structure for invoices
-- ----------------------------
DROP TABLE IF EXISTS `invoices`;
CREATE TABLE `invoices`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint UNSIGNED NULL DEFAULT NULL,
  `payment_id` bigint UNSIGNED NOT NULL,
  `number` int UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `invoices_number_unique`(`number` ASC) USING BTREE,
  INDEX `invoices_payment_id_foreign`(`payment_id` ASC) USING BTREE,
  INDEX `invoices_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `invoices_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `invoices_payment_id_foreign` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 355 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of invoices
-- ----------------------------
INSERT INTO `invoices` VALUES (1, 8, 127, 1, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (2, 8, 128, 2, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (3, 8, 129, 3, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (4, 8, 130, 4, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (5, 8, 131, 5, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (6, 8, 132, 6, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (7, 8, 133, 7, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (8, 8, 134, 8, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (9, 8, 135, 9, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (10, 8, 136, 10, '2024-12-30 06:49:31', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (11, 8, 137, 11, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (12, 8, 138, 12, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (13, 8, 139, 13, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (14, 8, 140, 14, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (15, 8, 141, 15, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (16, 8, 142, 16, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (17, 8, 143, 17, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (18, 8, 144, 18, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (19, 8, 145, 19, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (20, 8, 146, 20, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (21, 8, 147, 21, '2024-12-30 09:30:06', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (22, 8, 148, 22, '2024-12-30 09:30:06', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (23, 8, 149, 23, '2024-12-30 09:30:06', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (24, 8, 150, 24, '2024-12-30 09:30:06', '2024-12-30 09:33:41', NULL);
INSERT INTO `invoices` VALUES (158, 1, 284, 25, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (159, 1, 285, 26, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (160, 1, 286, 27, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (161, 1, 287, 28, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (162, 1, 288, 29, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (163, 1, 289, 30, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (164, 1, 290, 31, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (165, 1, 291, 32, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (166, 1, 292, 33, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (167, 1, 293, 34, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (168, 1, 294, 35, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (169, 1, 295, 36, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `invoices` VALUES (170, 1, 296, 37, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (171, 1, 297, 38, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (172, 1, 298, 39, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (173, 1, 299, 40, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (174, 1, 300, 41, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (175, 1, 301, 42, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (176, 1, 302, 43, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (177, 1, 303, 44, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (178, 1, 304, 45, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (179, 1, 305, 46, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (180, 1, 306, 47, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (181, 1, 307, 48, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `invoices` VALUES (182, 23, 308, 49, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (183, 23, 309, 50, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (184, 23, 310, 51, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (185, 23, 311, 52, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (186, 23, 312, 53, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (187, 23, 313, 54, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (188, 23, 314, 55, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (189, 23, 315, 56, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (190, 23, 316, 57, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (191, 23, 317, 58, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (192, 23, 318, 59, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (193, 23, 319, 60, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `invoices` VALUES (194, 23, 320, 61, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `invoices` VALUES (195, 23, 321, 62, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `invoices` VALUES (196, 23, 322, 63, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `invoices` VALUES (197, 23, 323, 64, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `invoices` VALUES (198, 23, 324, 65, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `invoices` VALUES (199, 23, 325, 66, '2025-03-05 23:23:10', '2025-03-05 23:23:10', NULL);
INSERT INTO `invoices` VALUES (200, 23, 326, 67, '2025-03-05 23:23:10', '2025-03-05 23:23:10', NULL);
INSERT INTO `invoices` VALUES (201, 23, 327, 68, '2025-03-05 23:23:10', '2025-03-05 23:23:10', NULL);
INSERT INTO `invoices` VALUES (202, 23, 328, 69, '2025-03-05 23:23:10', '2025-03-05 23:23:10', NULL);
INSERT INTO `invoices` VALUES (203, 23, 329, 70, '2025-03-05 23:23:10', '2025-03-05 23:23:10', NULL);
INSERT INTO `invoices` VALUES (298, 23, 424, 71, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (299, 23, 425, 72, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (300, 23, 426, 73, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (301, 23, 427, 74, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (302, 23, 428, 75, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (303, 23, 429, 76, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (304, 23, 430, 77, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (305, 23, 431, 78, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (306, 23, 432, 79, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (307, 23, 433, 80, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (308, 23, 434, 81, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (309, 23, 435, 82, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `invoices` VALUES (345, 23, 471, 83, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (346, 23, 472, 84, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (347, 23, 473, 85, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (348, 23, 474, 86, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (349, 23, 475, 87, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (350, 23, 476, 88, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (351, 23, 477, 89, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (352, 23, 478, 90, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (353, 23, 479, 91, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);
INSERT INTO `invoices` VALUES (354, 23, 480, 92, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);

-- ----------------------------
-- Table structure for jobs
-- ----------------------------
DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED NULL DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `jobs_queue_index`(`queue` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of jobs
-- ----------------------------

-- ----------------------------
-- Table structure for lease_files
-- ----------------------------
DROP TABLE IF EXISTS `lease_files`;
CREATE TABLE `lease_files`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `lease_id` bigint UNSIGNED NOT NULL,
  `file_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lease_files_lease_id_foreign`(`lease_id` ASC) USING BTREE,
  CONSTRAINT `lease_files_lease_id_foreign` FOREIGN KEY (`lease_id`) REFERENCES `leases` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lease_files
-- ----------------------------
INSERT INTO `lease_files` VALUES (4, 1, 'uploads/Docebo Report-ayeYQQmY.docx', 'Docebo Report.docx', '2025-02-18 23:11:20', '2025-02-18 23:11:20');
INSERT INTO `lease_files` VALUES (5, 1, 'uploads/Docebo-SF - Copy-WJ7G9GEw.docx', 'Docebo-SF - Copy.docx', '2025-02-18 23:11:20', '2025-02-18 23:11:20');
INSERT INTO `lease_files` VALUES (6, 1, 'uploads/Docebo-SF-5NbFyYys.docx', 'Docebo-SF.docx', '2025-02-18 23:11:20', '2025-02-18 23:11:20');

-- ----------------------------
-- Table structure for lease_notes
-- ----------------------------
DROP TABLE IF EXISTS `lease_notes`;
CREATE TABLE `lease_notes`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `lease_id` bigint UNSIGNED NOT NULL,
  `body` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lease_notes_license_id_foreign`(`lease_id` ASC) USING BTREE,
  CONSTRAINT `lease_notes_license_id_foreign` FOREIGN KEY (`lease_id`) REFERENCES `licenses` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lease_notes
-- ----------------------------
INSERT INTO `lease_notes` VALUES (1, 1, 'asdfasdf', NULL, '2025-02-18 21:59:54', '2025-02-18 21:59:54');
INSERT INTO `lease_notes` VALUES (2, 1, 'asd asd asf a', '2025-02-18 22:03:44', '2025-02-18 22:03:34', '2025-02-18 22:03:44');
INSERT INTO `lease_notes` VALUES (3, 1, 'lease not 2', '2025-02-18 22:35:17', '2025-02-18 22:35:12', '2025-02-18 22:35:17');

-- ----------------------------
-- Table structure for leases
-- ----------------------------
DROP TABLE IF EXISTS `leases`;
CREATE TABLE `leases`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` bigint UNSIGNED NOT NULL,
  `studio_id` bigint UNSIGNED NOT NULL,
  `initial_currency_id` bigint UNSIGNED NULL DEFAULT NULL,
  `customer_id` bigint UNSIGNED NOT NULL,
  `machine_id` bigint UNSIGNED NOT NULL,
  `machine_price` int NOT NULL,
  `machine_quantity` int NOT NULL,
  `duration` int NOT NULL,
  `starting_date` date NOT NULL,
  `deposit_amount` int NULL DEFAULT NULL,
  `deposit_date` date NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `leases_company_id_foreign`(`company_id` ASC) USING BTREE,
  INDEX `leases_studio_id_foreign`(`studio_id` ASC) USING BTREE,
  INDEX `leases_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  INDEX `leases_machine_id_foreign`(`machine_id` ASC) USING BTREE,
  INDEX `leases_initial_currency_id_foreign`(`initial_currency_id` ASC) USING BTREE,
  CONSTRAINT `leases_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `leases_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `leases_initial_currency_id_foreign` FOREIGN KEY (`initial_currency_id`) REFERENCES `currencies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `leases_machine_id_foreign` FOREIGN KEY (`machine_id`) REFERENCES `machines` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `leases_studio_id_foreign` FOREIGN KEY (`studio_id`) REFERENCES `studios` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of leases
-- ----------------------------
INSERT INTO `leases` VALUES (1, 1, 1, 1, 1, 1, 150000, 10, 48, '2024-12-24', 1000000, '2024-12-24', 1, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');

-- ----------------------------
-- Table structure for license_files
-- ----------------------------
DROP TABLE IF EXISTS `license_files`;
CREATE TABLE `license_files`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `license_id` bigint UNSIGNED NOT NULL,
  `file_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `original_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `license_files_license_id_foreign`(`license_id` ASC) USING BTREE,
  CONSTRAINT `license_files_license_id_foreign` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of license_files
-- ----------------------------
INSERT INTO `license_files` VALUES (32, 2, 'uploads/Docebo Report - Copy-0iaCRJDq.docx', 'Docebo Report - Copy.docx', '2025-02-17 23:46:31', '2025-02-17 23:46:31');
INSERT INTO `license_files` VALUES (33, 2, 'uploads/Docebo Report - Copy-IJNM8DHU.rar', 'Docebo Report - Copy.rar', '2025-02-17 23:46:31', '2025-02-17 23:46:31');
INSERT INTO `license_files` VALUES (34, 2, 'uploads/Docebo Report-R8fuexKr.docx', 'Docebo Report.docx', '2025-02-17 23:46:31', '2025-02-17 23:46:31');
INSERT INTO `license_files` VALUES (35, 2, 'uploads/Docebo-SF - Copy-xhCYVqNv.docx', 'Docebo-SF - Copy.docx', '2025-02-17 23:46:31', '2025-02-17 23:46:31');
INSERT INTO `license_files` VALUES (36, 2, 'uploads/Docebo-SF-rUlyU9vX.docx', 'Docebo-SF.docx', '2025-02-17 23:46:31', '2025-02-17 23:46:31');

-- ----------------------------
-- Table structure for license_notes
-- ----------------------------
DROP TABLE IF EXISTS `license_notes`;
CREATE TABLE `license_notes`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `license_id` bigint UNSIGNED NOT NULL,
  `body` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `license_notes_license_id_foreign`(`license_id` ASC) USING BTREE,
  CONSTRAINT `license_notes_license_id_foreign` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of license_notes
-- ----------------------------
INSERT INTO `license_notes` VALUES (1, 1, 'this is first lc notes', NULL, NULL, NULL);
INSERT INTO `license_notes` VALUES (2, 1, 'this is second notes', NULL, NULL, NULL);
INSERT INTO `license_notes` VALUES (3, 1, 'this is third notes', NULL, NULL, NULL);
INSERT INTO `license_notes` VALUES (5, 3, 'Fourth notes', '2025-02-13 11:36:50', NULL, '2025-02-13 11:36:50');
INSERT INTO `license_notes` VALUES (6, 5, 'test test test', NULL, NULL, NULL);
INSERT INTO `license_notes` VALUES (7, 3, 'asd', '2025-02-13 11:38:41', '2025-02-13 11:24:37', '2025-02-13 11:38:41');
INSERT INTO `license_notes` VALUES (8, 3, 'asd', '2025-02-13 11:38:46', '2025-02-13 11:24:59', '2025-02-13 11:38:46');
INSERT INTO `license_notes` VALUES (9, 3, 'asdf', '2025-02-13 11:38:51', '2025-02-13 11:31:16', '2025-02-13 11:38:51');
INSERT INTO `license_notes` VALUES (10, 3, 'aaaassdfasdf', NULL, '2025-02-13 11:31:27', '2025-02-13 11:31:27');
INSERT INTO `license_notes` VALUES (11, 2, 'test notes count', NULL, '2025-02-17 01:09:40', '2025-02-17 01:09:40');
INSERT INTO `license_notes` VALUES (12, 2, 'second test counts', '2025-02-17 01:10:09', '2025-02-17 01:09:52', '2025-02-17 01:10:09');

-- ----------------------------
-- Table structure for licenses
-- ----------------------------
DROP TABLE IF EXISTS `licenses`;
CREATE TABLE `licenses`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` bigint UNSIGNED NOT NULL,
  `customer_id` bigint UNSIGNED NOT NULL,
  `studio_id` bigint UNSIGNED NOT NULL,
  `initial_currency_id` bigint UNSIGNED NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` int NOT NULL,
  `duration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `starting_date` date NOT NULL,
  `deposit_amount` int NULL DEFAULT NULL,
  `deposit_date` date NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `stripe_session_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `licenses_company_id_foreign`(`company_id` ASC) USING BTREE,
  INDEX `licenses_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  INDEX `licenses_studio_id_foreign`(`studio_id` ASC) USING BTREE,
  INDEX `licenses_initial_currency_id_foreign`(`initial_currency_id` ASC) USING BTREE,
  CONSTRAINT `licenses_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `licenses_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `licenses_initial_currency_id_foreign` FOREIGN KEY (`initial_currency_id`) REFERENCES `currencies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `licenses_studio_id_foreign` FOREIGN KEY (`studio_id`) REFERENCES `studios` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of licenses
-- ----------------------------
INSERT INTO `licenses` VALUES (1, 3, 1, 1, 1, 'license', 'USA', 150000, '30', '2024-12-16', 0, NULL, 1, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33', NULL);
INSERT INTO `licenses` VALUES (2, 2, 1, 1, 1, 'exclusivity', 'USA', 20000, '48', '2024-12-24', NULL, NULL, 1, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58', NULL);
INSERT INTO `licenses` VALUES (3, 2, 8, 5, 1, 'license', 'International', 50000, '10', '2024-12-31', 50000, '2024-12-31', 1, NULL, '2024-12-30 06:49:31', '2025-01-15 15:50:38', NULL);
INSERT INTO `licenses` VALUES (4, 2, 8, 3, 1, 'license', 'USA', 50000, '10', '2024-12-31', NULL, NULL, 1, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41', NULL);
INSERT INTO `licenses` VALUES (5, 2, 8, 4, 1, 'license', 'USA', 52000, '4', '2024-12-31', NULL, NULL, 1, NULL, '2024-12-30 09:30:06', '2024-12-30 09:33:41', NULL);
INSERT INTO `licenses` VALUES (21, 2, 1, 2, 1, 'license', 'International', 2300, '12', '2025-03-06', 1200, '2025-11-04', 1, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07', NULL);
INSERT INTO `licenses` VALUES (22, 3, 1, 8, 1, 'license', 'USA', 35000, '12', '2025-03-06', NULL, NULL, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL);
INSERT INTO `licenses` VALUES (23, 2, 23, 1, 1, 'license', 'International', 24000, '12', '2025-03-06', NULL, NULL, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18', NULL);
INSERT INTO `licenses` VALUES (24, 2, 23, 1, 1, 'license', 'International', 35000, '10', '2025-03-12', NULL, NULL, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09', NULL);
INSERT INTO `licenses` VALUES (31, 1, 23, 2, 1, 'license', 'International', 1200, '12', '2025-03-06', 1200, '2025-03-06', 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30', NULL);
INSERT INTO `licenses` VALUES (49, 5, 23, 8, 1, 'license', 'USA', 20000, '10', '2025-03-07', NULL, NULL, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33', NULL);

-- ----------------------------
-- Table structure for machines
-- ----------------------------
DROP TABLE IF EXISTS `machines`;
CREATE TABLE `machines`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of machines
-- ----------------------------
INSERT INTO `machines` VALUES (1, 'The Mega Pro', NULL, '2024-12-24 01:21:29', '2024-12-29 01:41:26');
INSERT INTO `machines` VALUES (2, 'The Micro', NULL, '2024-12-24 02:10:19', '2024-12-24 02:10:19');
INSERT INTO `machines` VALUES (3, 'The Mini', NULL, '2024-12-24 02:10:24', '2024-12-24 02:10:24');
INSERT INTO `machines` VALUES (4, 'The Mini Pro', NULL, '2024-12-24 02:10:29', '2024-12-24 02:10:29');
INSERT INTO `machines` VALUES (5, 'The Mega Pro', NULL, '2024-12-24 02:10:34', '2024-12-24 02:10:34');

-- ----------------------------
-- Table structure for migrations
-- ----------------------------
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of migrations
-- ----------------------------
INSERT INTO `migrations` VALUES (1, '2014_10_12_000000_create_users_table', 1);
INSERT INTO `migrations` VALUES (2, '2014_10_12_100000_create_password_reset_tokens_table', 1);
INSERT INTO `migrations` VALUES (3, '2018_08_08_100000_create_telescope_entries_table', 1);
INSERT INTO `migrations` VALUES (4, '2019_08_19_000000_create_failed_jobs_table', 1);
INSERT INTO `migrations` VALUES (5, '2019_12_14_000001_create_personal_access_tokens_table', 1);
INSERT INTO `migrations` VALUES (6, '2023_01_01_115520_create_currencies_table', 1);
INSERT INTO `migrations` VALUES (7, '2023_05_14_103336_create_customers_table', 1);
INSERT INTO `migrations` VALUES (8, '2023_06_13_075456_create_states_table', 1);
INSERT INTO `migrations` VALUES (9, '2023_06_14_202230_create_companies_table', 1);
INSERT INTO `migrations` VALUES (10, '2023_06_27_104532_create_studios_table', 1);
INSERT INTO `migrations` VALUES (11, '2023_08_05_121755_create_licenses_table', 1);
INSERT INTO `migrations` VALUES (12, '2023_09_19_143259_create_notifications_table', 1);
INSERT INTO `migrations` VALUES (13, '2023_11_12_110715_create_machines_table', 1);
INSERT INTO `migrations` VALUES (14, '2023_11_13_085357_create_leases_table', 1);
INSERT INTO `migrations` VALUES (15, '2023_11_14_202809_create_payments_table', 1);
INSERT INTO `migrations` VALUES (16, '2024_07_31_091414_create_files_table', 1);
INSERT INTO `migrations` VALUES (17, '2024_08_09_182936_create_permission_tables', 1);
INSERT INTO `migrations` VALUES (18, '2024_08_19_180619_create_notes_table', 1);
INSERT INTO `migrations` VALUES (19, '2024_08_23_095854_create_invoices_table', 1);
INSERT INTO `migrations` VALUES (20, '2024_09_09_074324_create_suppliers_table', 1);
INSERT INTO `migrations` VALUES (21, '2024_09_09_075540_create_contact_people_table', 1);
INSERT INTO `migrations` VALUES (22, '2024_09_12_072230_create_orders_table', 1);
INSERT INTO `migrations` VALUES (23, '2024_09_12_072512_create_order_items_table', 1);
INSERT INTO `migrations` VALUES (24, '2024_09_30_085251_create_agreements_table', 1);
INSERT INTO `migrations` VALUES (25, '2024_10_01_092322_create_webhooks_table', 1);
INSERT INTO `migrations` VALUES (26, '2024_10_02_123833_create_jobs_table', 1);
INSERT INTO `migrations` VALUES (27, '2024_10_09_091256_create_admin_notifications_table', 1);
INSERT INTO `migrations` VALUES (28, '2024_10_10_105429_create_admin_settings_table', 1);
INSERT INTO `migrations` VALUES (29, '2024_10_15_094705_create_conversion_rates_table', 1);
INSERT INTO `migrations` VALUES (30, '2024_10_16_114654_add_initial_currency_id_to_licenses_table', 1);
INSERT INTO `migrations` VALUES (31, '2024_10_16_114700_add_initial_currency_id_to_leases_table', 1);
INSERT INTO `migrations` VALUES (32, '2024_10_18_074814_add_customer_id_to_invoices_table', 1);
INSERT INTO `migrations` VALUES (33, '2024_10_18_102013_add_soft_delete_to_invoices_table', 1);
INSERT INTO `migrations` VALUES (34, '2024_10_21_070051_create_email_templates_table', 1);
INSERT INTO `migrations` VALUES (35, '2024_12_01_081312_create_products_table', 1);
INSERT INTO `migrations` VALUES (36, '2024_12_02_111553_create_invoice_products_table', 1);
INSERT INTO `migrations` VALUES (37, '2024_12_03_080408_create_invoice_product_items_table', 1);
INSERT INTO `migrations` VALUES (38, '2024_12_16_144726_create_countries_table', 2);
INSERT INTO `migrations` VALUES (39, '2024_12_16_162447_add_country_id_to_suppliers_table', 2);
INSERT INTO `migrations` VALUES (40, '2024_12_23_224305_add_category_to_products_table', 3);
INSERT INTO `migrations` VALUES (41, '2024_12_25_232102_add_discount_type_to_invoice_product_items_table', 4);
INSERT INTO `migrations` VALUES (42, '2024_12_29_132050_add_show_tax_to_invoice_products_table', 5);
INSERT INTO `migrations` VALUES (43, '2024_12_29_151509_add_address_to_customers_table', 5);
INSERT INTO `migrations` VALUES (44, '2024_12_30_150432_add_country_id_to_studios_table', 6);
INSERT INTO `migrations` VALUES (45, '2024_12_31_094142_alter_category_from_products_table', 7);
INSERT INTO `migrations` VALUES (46, '2024_12_31_151204_add_location_to_studios_table', 8);
INSERT INTO `migrations` VALUES (47, '2024_12_31_162250_add_nullable_to_customers_table', 8);
INSERT INTO `migrations` VALUES (48, '2025_01_17_161227_add_paid_to_invoice_products_table', 9);
INSERT INTO `migrations` VALUES (49, '2025_01_17_224237_add_reminder_date_to_invoice_products_table', 10);
INSERT INTO `migrations` VALUES (50, '2025_01_18_193335_add_type_to_products_table', 11);
INSERT INTO `migrations` VALUES (51, '2025_01_22_173850_create_order_products_table', 12);
INSERT INTO `migrations` VALUES (53, '2025_01_23_163651_add_supplier_to_products_table', 13);
INSERT INTO `migrations` VALUES (54, '2025_01_23_173438_add_fields_to_order_items_table', 13);
INSERT INTO `migrations` VALUES (55, '2025_01_25_142332_add_stock_to_products_table', 14);
INSERT INTO `migrations` VALUES (56, '2025_01_28_125053_add_email2_to_users_table', 15);
INSERT INTO `migrations` VALUES (57, '2025_01_28_125102_add_email2_to_suppliers_table', 15);
INSERT INTO `migrations` VALUES (58, '2025_01_30_205835_create_bundle_table', 15);
INSERT INTO `migrations` VALUES (59, '2025_01_30_210559_create_bundle_item_table', 15);
INSERT INTO `migrations` VALUES (60, '2025_02_11_071129_create_invoice_templates_table', 16);
INSERT INTO `migrations` VALUES (61, '2025_02_11_071329_create_invoice_template_products_table', 16);
INSERT INTO `migrations` VALUES (62, '2025_02_10_170851_add_shipping_address_to_customers_table', 17);
INSERT INTO `migrations` VALUES (63, '2025_02_10_171624_add_shipping_location_to_customers_table', 17);
INSERT INTO `migrations` VALUES (64, '2025_02_11_103753_alter_phone_from_users_table', 17);
INSERT INTO `migrations` VALUES (65, '2025_02_13_181954_create_license_notes_table', 17);
INSERT INTO `migrations` VALUES (66, '2025_02_18_051407_create_license_files_table', 18);
INSERT INTO `migrations` VALUES (67, '2025_02_18_051758_create_license_files_table', 19);
INSERT INTO `migrations` VALUES (68, '2025_02_18_052126_add_original_filename_to_license_files', 20);
INSERT INTO `migrations` VALUES (69, '2025_02_18_052602_add_original_filename_to_license_files', 21);
INSERT INTO `migrations` VALUES (70, '2025_02_18_082148_create_lease_notes', 22);
INSERT INTO `migrations` VALUES (71, '2025_02_18_082240_create_lease_files', 22);
INSERT INTO `migrations` VALUES (72, '2025_02_26_095153_create_customer_agreements_table', 23);
INSERT INTO `migrations` VALUES (73, '2025_02_26_204252_add_location_to_customer_agreements_table', 24);
INSERT INTO `migrations` VALUES (74, '2025_03_06_063405_update_is_active_default_in_licenses_table', 25);
INSERT INTO `migrations` VALUES (75, '2025_03_06_175758_add_stripe_session_id_to_licenses_table', 26);

-- ----------------------------
-- Table structure for model_has_permissions
-- ----------------------------
DROP TABLE IF EXISTS `model_has_permissions`;
CREATE TABLE `model_has_permissions`  (
  `permission_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL,
  PRIMARY KEY (`permission_id`, `model_id`, `model_type`) USING BTREE,
  INDEX `model_has_permissions_model_id_model_type_index`(`model_id` ASC, `model_type` ASC) USING BTREE,
  CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of model_has_permissions
-- ----------------------------

-- ----------------------------
-- Table structure for model_has_roles
-- ----------------------------
DROP TABLE IF EXISTS `model_has_roles`;
CREATE TABLE `model_has_roles`  (
  `role_id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL,
  PRIMARY KEY (`role_id`, `model_id`, `model_type`) USING BTREE,
  INDEX `model_has_roles_model_id_model_type_index`(`model_id` ASC, `model_type` ASC) USING BTREE,
  CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of model_has_roles
-- ----------------------------
INSERT INTO `model_has_roles` VALUES (1, 'App\\Models\\User', 1);
INSERT INTO `model_has_roles` VALUES (1, 'App\\Models\\User', 21);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 2);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 3);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 4);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 5);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 6);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 7);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 8);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 9);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 10);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 11);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 12);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 13);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 14);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 15);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 16);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 17);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 18);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 19);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 20);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 22);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 23);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 24);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 25);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 26);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 27);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 32);
INSERT INTO `model_has_roles` VALUES (2, 'App\\Models\\User', 33);

-- ----------------------------
-- Table structure for notes
-- ----------------------------
DROP TABLE IF EXISTS `notes`;
CREATE TABLE `notes`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` bigint UNSIGNED NOT NULL,
  `body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `notes_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `notes_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notes
-- ----------------------------
INSERT INTO `notes` VALUES (1, 2, 'this costumer sucks', NULL, '2024-12-18 08:27:32', '2024-12-18 08:27:32');
INSERT INTO `notes` VALUES (2, 4, 'dadada', NULL, '2024-12-23 22:09:23', '2024-12-23 22:09:23');
INSERT INTO `notes` VALUES (3, 1, 'Non minor, inquit, voluptas percipitur ex vilissimis rebus quam ex pretiosissimis. Hoc est non modo cor non habere, sed ne palatum quidem. Duo Reges: constructio interrete', NULL, '2024-12-24 04:46:44', '2024-12-24 04:46:44');
INSERT INTO `notes` VALUES (4, 1, 'Ut enim consuetudo loquitur, id solum dicitur honestum, quod est populari fama gloriosum. Luxuriam non reprehendit, modo sit vacua infinita cupiditate et timore. Haec mihi videtur delicatior, ut ita dicam, molliorque ratio, quam virtutis vis gravitasque postulat.', NULL, '2024-12-24 04:46:55', '2024-12-24 04:46:55');
INSERT INTO `notes` VALUES (5, 15, 'my new note', NULL, '2025-01-22 04:00:00', '2025-01-22 04:00:00');
INSERT INTO `notes` VALUES (6, 1, 'adsf asdfa dsffasdfa sdf asdf asdfa sfdasdf asdfadsf', NULL, '2025-02-13 06:47:27', '2025-02-13 06:47:27');
INSERT INTO `notes` VALUES (7, 8, 'asdfasdf', '2025-02-13 10:57:19', '2025-02-13 10:55:54', '2025-02-13 10:57:19');

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint UNSIGNED NOT NULL,
  `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `notifications_notifiable_type_notifiable_id_index`(`notifiable_type` ASC, `notifiable_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of notifications
-- ----------------------------
INSERT INTO `notifications` VALUES ('625e5e2d-f32d-4a11-b4d5-e26234f886c6', 'App\\Notifications\\AdminNotification', 'App\\Models\\User', 2, '{\"title\":\"First text\",\"data\":\"This is very first notification.\",\"link\":\"http:\\/\\/somelink.com\"}', NULL, '2024-12-26 21:00:04', '2024-12-26 21:00:04');

-- ----------------------------
-- Table structure for order_items
-- ----------------------------
DROP TABLE IF EXISTS `order_items`;
CREATE TABLE `order_items`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` bigint UNSIGNED NOT NULL,
  `product_id` bigint UNSIGNED NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `quantity` int NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_items_order_id_foreign`(`order_id` ASC) USING BTREE,
  INDEX `FK_order_items_suppliers_foreign_key`(`product_id` ASC) USING BTREE,
  CONSTRAINT `FK_order_items_suppliers_foreign_key` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order_items
-- ----------------------------
INSERT INTO `order_items` VALUES (1, 1, 1, 'Micro Pro', 0.00, 2, NULL, '2024-12-23 05:56:40', '2024-12-26 07:45:47');
INSERT INTO `order_items` VALUES (2, 2, 2, 'Micro Pro 2', 0.00, 2, NULL, '2024-12-27 02:22:01', '2024-12-31 04:37:39');
INSERT INTO `order_items` VALUES (3, 2, 3, 'Micro Pro 3', 0.00, 2, NULL, '2024-12-27 02:22:01', '2024-12-31 04:37:39');
INSERT INTO `order_items` VALUES (4, 4, 4, '-', 130.00, 7, NULL, '2024-12-31 04:27:05', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (5, 4, 5, '-', 220.00, 8, NULL, '2024-12-31 05:07:53', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (6, 4, 224, '-', 330.00, 1, NULL, '2025-01-26 05:42:28', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (7, 4, 35, '-', 1.69, 100, NULL, '2025-01-26 05:57:48', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (8, 4, 88, '-', 1.16, 100, NULL, '2025-01-26 05:57:48', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (9, 4, 88, '-', 1.16, 1, NULL, '2025-01-26 06:57:45', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (10, 4, 28, '-', 2.55, 1, NULL, '2025-01-26 06:57:45', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (11, 4, 22, '-', 3.88, 1, NULL, '2025-01-26 06:57:45', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (12, 4, 88, '-', 1.16, 1, NULL, '2025-01-26 06:57:45', '2025-01-26 06:57:45');
INSERT INTO `order_items` VALUES (13, 5, 1, '-', 3.37, 2, NULL, '2025-01-26 08:26:08', '2025-01-26 08:28:39');
INSERT INTO `order_items` VALUES (14, 6, 149, '-', 45.48, 12, NULL, '2025-01-26 08:32:05', '2025-01-26 08:32:05');
INSERT INTO `order_items` VALUES (15, 6, 149, '-', 42.96, 12, NULL, '2025-01-26 08:32:05', '2025-01-26 08:32:05');
INSERT INTO `order_items` VALUES (16, 7, 149, '-', 1.03, 1, NULL, '2025-01-26 08:33:33', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (17, 7, 151, '-', 20.00, 12, NULL, '2025-01-26 08:33:33', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (18, 8, 149, '-', 42.96, 1, NULL, '2025-01-26 08:34:09', '2025-01-26 08:34:09');
INSERT INTO `order_items` VALUES (19, 9, 149, '-', 1.69, 1, NULL, '2025-01-26 08:35:26', '2025-01-26 08:35:26');
INSERT INTO `order_items` VALUES (20, 10, 149, '-', 1.69, 1, NULL, '2025-01-26 08:41:39', '2025-01-26 08:41:39');
INSERT INTO `order_items` VALUES (21, 13, 149, '-', 1.03, 1, NULL, '2025-01-26 08:46:33', '2025-01-26 08:46:33');
INSERT INTO `order_items` VALUES (22, 13, 29, '-', 2.43, 1, NULL, '2025-01-26 08:46:33', '2025-01-26 08:46:33');
INSERT INTO `order_items` VALUES (23, 14, 34, '-', 5.06, 1, NULL, '2025-01-26 08:48:30', '2025-01-26 08:48:30');
INSERT INTO `order_items` VALUES (24, 15, 149, '-', 1.03, 1, NULL, '2025-01-26 08:53:19', '2025-01-26 13:27:36');
INSERT INTO `order_items` VALUES (25, 11, 88, '-', 1.16, 1, NULL, '2025-01-26 11:32:49', '2025-01-26 12:18:24');
INSERT INTO `order_items` VALUES (26, 11, 229, '-', 22.00, 2, NULL, '2025-01-26 11:32:49', '2025-01-26 12:18:24');
INSERT INTO `order_items` VALUES (27, 7, 230, '-', 22.00, 12, NULL, '2025-01-26 12:46:30', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (28, 7, 38, '-', 3.44, 12, NULL, '2025-01-26 12:46:30', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (29, 7, 231, '-', 44.00, 12, NULL, '2025-01-26 12:46:30', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (30, 15, 149, '-', 1.03, 1, NULL, '2025-01-26 13:27:14', '2025-01-26 13:27:36');
INSERT INTO `order_items` VALUES (31, 15, 29, '-', 2.43, 12, NULL, '2025-01-26 13:27:36', '2025-01-26 13:27:36');
INSERT INTO `order_items` VALUES (32, 7, 232, '-', 22.00, 12, NULL, '2025-01-26 13:28:12', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (33, 7, 233, '-', 33.00, 12, NULL, '2025-01-26 13:28:12', '2025-01-26 13:28:12');
INSERT INTO `order_items` VALUES (34, 7, 38, '-', 3.44, 12, NULL, '2025-01-26 13:28:12', '2025-01-26 13:28:12');

-- ----------------------------
-- Table structure for orders
-- ----------------------------
DROP TABLE IF EXISTS `orders`;
CREATE TABLE `orders`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_id` bigint UNSIGNED NOT NULL,
  `supplier_id` bigint UNSIGNED NOT NULL,
  `note` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `sent_at` date NULL DEFAULT NULL,
  `status` int NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `orders_company_id_foreign`(`company_id` ASC) USING BTREE,
  INDEX `orders_supplier_id_foreign`(`supplier_id` ASC) USING BTREE,
  CONSTRAINT `orders_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `orders_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of orders
-- ----------------------------
INSERT INTO `orders` VALUES (1, 1, 1, NULL, '2024-12-24', 1, '2024-12-26 07:45:47', '2024-12-23 05:56:40', '2024-12-26 07:45:47');
INSERT INTO `orders` VALUES (2, 2, 1, 'This is order note', NULL, 0, '2024-12-31 04:37:39', '2024-12-27 02:22:01', '2024-12-31 04:37:39');
INSERT INTO `orders` VALUES (3, 2, 1, NULL, NULL, 0, '2024-12-31 04:37:35', '2024-12-31 04:27:05', '2024-12-31 04:37:35');
INSERT INTO `orders` VALUES (4, 2, 1, NULL, NULL, 0, NULL, '2024-12-31 05:07:53', '2024-12-31 05:07:53');
INSERT INTO `orders` VALUES (5, 1, 3, NULL, NULL, 0, NULL, '2025-01-26 08:26:08', '2025-01-26 08:26:08');
INSERT INTO `orders` VALUES (6, 1, 4, 'asd as dasd', NULL, 0, NULL, '2025-01-26 08:32:05', '2025-01-26 08:32:05');
INSERT INTO `orders` VALUES (7, 2, 2, 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi tempus, risus quis ultricies pharetra, dui tortor condimentum risus, at tristique ante elit vel velit. Proin elementum, nisi vitae auctor commodo, lectus ipsum dapibus velit, eu suscipit sapien ligula at tellus. Proin placerat eros vel arcu feugiat, id luctus orci eleifend. Curabitur nulla tortor, feugiat ut faucibus quis, consectetur eu augue. Duis sed convallis purus. Sed vel finibus dolor, non tincidunt augue.', '2025-01-26', 1, NULL, '2025-01-26 08:33:33', '2025-01-26 13:19:51');
INSERT INTO `orders` VALUES (8, 1, 4, NULL, NULL, 0, NULL, '2025-01-26 08:34:09', '2025-01-26 08:34:09');
INSERT INTO `orders` VALUES (9, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 08:35:26', '2025-01-26 08:35:26');
INSERT INTO `orders` VALUES (10, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 08:41:39', '2025-01-26 08:41:39');
INSERT INTO `orders` VALUES (11, 1, 1, NULL, '2025-01-10', 0, NULL, '2025-01-26 08:44:39', '2025-01-26 12:18:24');
INSERT INTO `orders` VALUES (12, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 08:45:48', '2025-01-26 08:45:48');
INSERT INTO `orders` VALUES (13, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 08:46:33', '2025-01-26 08:46:33');
INSERT INTO `orders` VALUES (14, 1, 3, NULL, '2025-01-26', 0, NULL, '2025-01-26 08:48:30', '2025-01-26 08:48:30');
INSERT INTO `orders` VALUES (15, 1, 1, NULL, '2025-01-26', 0, NULL, '2025-01-26 08:53:19', '2025-01-26 08:53:19');

-- ----------------------------
-- Table structure for password_reset_tokens
-- ----------------------------
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens`  (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of password_reset_tokens
-- ----------------------------

-- ----------------------------
-- Table structure for payments
-- ----------------------------
DROP TABLE IF EXISTS `payments`;
CREATE TABLE `payments`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `license_id` bigint UNSIGNED NULL DEFAULT NULL,
  `lease_id` bigint UNSIGNED NULL DEFAULT NULL,
  `customer_id` bigint UNSIGNED NOT NULL,
  `machine_id` bigint UNSIGNED NULL DEFAULT NULL,
  `payment_number` int NOT NULL,
  `payment_amount` int NOT NULL,
  `payment_date` date NOT NULL,
  `status` int NOT NULL DEFAULT 0,
  `payment_reminder` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `payments_license_id_foreign`(`license_id` ASC) USING BTREE,
  INDEX `payments_lease_id_foreign`(`lease_id` ASC) USING BTREE,
  INDEX `payments_customer_id_foreign`(`customer_id` ASC) USING BTREE,
  INDEX `payments_machine_id_foreign`(`machine_id` ASC) USING BTREE,
  CONSTRAINT `payments_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `payments_lease_id_foreign` FOREIGN KEY (`lease_id`) REFERENCES `leases` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `payments_license_id_foreign` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `payments_machine_id_foreign` FOREIGN KEY (`machine_id`) REFERENCES `machines` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 481 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of payments
-- ----------------------------
INSERT INTO `payments` VALUES (1, 1, NULL, 1, NULL, 1, 150000, '2025-01-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (2, 1, NULL, 1, NULL, 2, 150000, '2025-02-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (3, 1, NULL, 1, NULL, 3, 150000, '2025-03-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (4, 1, NULL, 1, NULL, 4, 150000, '2025-04-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (5, 1, NULL, 1, NULL, 5, 150000, '2025-05-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (6, 1, NULL, 1, NULL, 6, 150000, '2025-06-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (7, 1, NULL, 1, NULL, 7, 150000, '2025-07-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (8, 1, NULL, 1, NULL, 8, 150000, '2025-08-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (9, 1, NULL, 1, NULL, 9, 150000, '2025-09-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (10, 1, NULL, 1, NULL, 10, 150000, '2025-10-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (11, 1, NULL, 1, NULL, 11, 150000, '2025-11-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (12, 1, NULL, 1, NULL, 12, 150000, '2025-12-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (13, 1, NULL, 1, NULL, 13, 150000, '2026-01-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (14, 1, NULL, 1, NULL, 14, 150000, '2026-02-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (15, 1, NULL, 1, NULL, 15, 150000, '2026-03-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (16, 1, NULL, 1, NULL, 16, 150000, '2026-04-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (17, 1, NULL, 1, NULL, 17, 150000, '2026-05-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (18, 1, NULL, 1, NULL, 18, 150000, '2026-06-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (19, 1, NULL, 1, NULL, 19, 150000, '2026-07-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (20, 1, NULL, 1, NULL, 20, 150000, '2026-08-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (21, 1, NULL, 1, NULL, 21, 150000, '2026-09-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (22, 1, NULL, 1, NULL, 22, 150000, '2026-10-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (23, 1, NULL, 1, NULL, 23, 150000, '2026-11-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (24, 1, NULL, 1, NULL, 24, 150000, '2026-12-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (25, 1, NULL, 1, NULL, 25, 150000, '2027-01-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (26, 1, NULL, 1, NULL, 26, 150000, '2027-02-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (27, 1, NULL, 1, NULL, 27, 150000, '2027-03-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (28, 1, NULL, 1, NULL, 28, 150000, '2027-04-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (29, 1, NULL, 1, NULL, 29, 150000, '2027-05-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (30, 1, NULL, 1, NULL, 30, 150000, '2027-06-01', 0, 0, NULL, '2024-12-16 11:02:34', '2024-12-16 11:04:33');
INSERT INTO `payments` VALUES (31, 2, NULL, 1, NULL, 1, 20000, '2025-01-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (32, 2, NULL, 1, NULL, 2, 20000, '2025-02-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (33, 2, NULL, 1, NULL, 3, 20000, '2025-03-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (34, 2, NULL, 1, NULL, 4, 20000, '2025-04-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (35, 2, NULL, 1, NULL, 5, 20000, '2025-05-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (36, 2, NULL, 1, NULL, 6, 20000, '2025-06-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (37, 2, NULL, 1, NULL, 7, 20000, '2025-07-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (38, 2, NULL, 1, NULL, 8, 20000, '2025-08-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (39, 2, NULL, 1, NULL, 9, 20000, '2025-09-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (40, 2, NULL, 1, NULL, 10, 20000, '2025-10-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (41, 2, NULL, 1, NULL, 11, 20000, '2025-11-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (42, 2, NULL, 1, NULL, 12, 20000, '2025-12-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (43, 2, NULL, 1, NULL, 13, 20000, '2026-01-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (44, 2, NULL, 1, NULL, 14, 20000, '2026-02-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (45, 2, NULL, 1, NULL, 15, 20000, '2026-03-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (46, 2, NULL, 1, NULL, 16, 20000, '2026-04-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (47, 2, NULL, 1, NULL, 17, 20000, '2026-05-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (48, 2, NULL, 1, NULL, 18, 20000, '2026-06-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (49, 2, NULL, 1, NULL, 19, 20000, '2026-07-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (50, 2, NULL, 1, NULL, 20, 20000, '2026-08-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (51, 2, NULL, 1, NULL, 21, 20000, '2026-09-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (52, 2, NULL, 1, NULL, 22, 20000, '2026-10-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (53, 2, NULL, 1, NULL, 23, 20000, '2026-11-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (54, 2, NULL, 1, NULL, 24, 20000, '2026-12-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (55, 2, NULL, 1, NULL, 25, 20000, '2027-01-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (56, 2, NULL, 1, NULL, 26, 20000, '2027-02-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (57, 2, NULL, 1, NULL, 27, 20000, '2027-03-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (58, 2, NULL, 1, NULL, 28, 20000, '2027-04-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (59, 2, NULL, 1, NULL, 29, 20000, '2027-05-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (60, 2, NULL, 1, NULL, 30, 20000, '2027-06-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (61, 2, NULL, 1, NULL, 31, 20000, '2027-07-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (62, 2, NULL, 1, NULL, 32, 20000, '2027-08-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (63, 2, NULL, 1, NULL, 33, 20000, '2027-09-01', 0, 0, NULL, '2024-12-24 01:16:58', '2024-12-24 01:16:58');
INSERT INTO `payments` VALUES (64, 2, NULL, 1, NULL, 34, 20000, '2027-10-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (65, 2, NULL, 1, NULL, 35, 20000, '2027-11-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (66, 2, NULL, 1, NULL, 36, 20000, '2027-12-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (67, 2, NULL, 1, NULL, 37, 20000, '2028-01-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (68, 2, NULL, 1, NULL, 38, 20000, '2028-02-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (69, 2, NULL, 1, NULL, 39, 20000, '2028-03-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (70, 2, NULL, 1, NULL, 40, 20000, '2028-04-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (71, 2, NULL, 1, NULL, 41, 20000, '2028-05-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (72, 2, NULL, 1, NULL, 42, 20000, '2028-06-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (73, 2, NULL, 1, NULL, 43, 20000, '2028-07-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (74, 2, NULL, 1, NULL, 44, 20000, '2028-08-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (75, 2, NULL, 1, NULL, 45, 20000, '2028-09-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (76, 2, NULL, 1, NULL, 46, 20000, '2028-10-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (77, 2, NULL, 1, NULL, 47, 20000, '2028-11-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (78, 2, NULL, 1, NULL, 48, 20000, '2028-12-01', 0, 0, NULL, '2024-12-24 01:16:59', '2024-12-24 01:16:59');
INSERT INTO `payments` VALUES (79, NULL, 1, 1, 1, 1, 1500000, '2025-01-01', 0, 0, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');
INSERT INTO `payments` VALUES (80, NULL, 1, 1, 1, 2, 1500000, '2025-02-01', 0, 0, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');
INSERT INTO `payments` VALUES (81, NULL, 1, 1, 1, 3, 1500000, '2025-03-01', 0, 0, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');
INSERT INTO `payments` VALUES (82, NULL, 1, 1, 1, 4, 1500000, '2025-04-01', 0, 0, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');
INSERT INTO `payments` VALUES (83, NULL, 1, 1, 1, 5, 1500000, '2025-05-01', 0, 0, NULL, '2024-12-24 01:22:16', '2024-12-24 01:22:16');
INSERT INTO `payments` VALUES (84, NULL, 1, 1, 1, 6, 1500000, '2025-06-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (85, NULL, 1, 1, 1, 7, 1500000, '2025-07-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (86, NULL, 1, 1, 1, 8, 1500000, '2025-08-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (87, NULL, 1, 1, 1, 9, 1500000, '2025-09-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (88, NULL, 1, 1, 1, 10, 1500000, '2025-10-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (89, NULL, 1, 1, 1, 11, 1500000, '2025-11-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (90, NULL, 1, 1, 1, 12, 1500000, '2025-12-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (91, NULL, 1, 1, 1, 13, 1500000, '2026-01-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (92, NULL, 1, 1, 1, 14, 1500000, '2026-02-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (93, NULL, 1, 1, 1, 15, 1500000, '2026-03-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (94, NULL, 1, 1, 1, 16, 1500000, '2026-04-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (95, NULL, 1, 1, 1, 17, 1500000, '2026-05-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (96, NULL, 1, 1, 1, 18, 1500000, '2026-06-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (97, NULL, 1, 1, 1, 19, 1500000, '2026-07-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (98, NULL, 1, 1, 1, 20, 1500000, '2026-08-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (99, NULL, 1, 1, 1, 21, 1500000, '2026-09-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (100, NULL, 1, 1, 1, 22, 1500000, '2026-10-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (101, NULL, 1, 1, 1, 23, 1500000, '2026-11-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (102, NULL, 1, 1, 1, 24, 1500000, '2026-12-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (103, NULL, 1, 1, 1, 25, 1500000, '2027-01-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (104, NULL, 1, 1, 1, 26, 1500000, '2027-02-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (105, NULL, 1, 1, 1, 27, 1500000, '2027-03-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (106, NULL, 1, 1, 1, 28, 1500000, '2027-04-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (107, NULL, 1, 1, 1, 29, 1500000, '2027-05-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (108, NULL, 1, 1, 1, 30, 1500000, '2027-06-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (109, NULL, 1, 1, 1, 31, 1500000, '2027-07-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (110, NULL, 1, 1, 1, 32, 1500000, '2027-08-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (111, NULL, 1, 1, 1, 33, 1500000, '2027-09-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (112, NULL, 1, 1, 1, 34, 1500000, '2027-10-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (113, NULL, 1, 1, 1, 35, 1500000, '2027-11-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (114, NULL, 1, 1, 1, 36, 1500000, '2027-12-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (115, NULL, 1, 1, 1, 37, 1500000, '2028-01-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (116, NULL, 1, 1, 1, 38, 1500000, '2028-02-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (117, NULL, 1, 1, 1, 39, 1500000, '2028-03-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (118, NULL, 1, 1, 1, 40, 1500000, '2028-04-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (119, NULL, 1, 1, 1, 41, 1500000, '2028-05-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (120, NULL, 1, 1, 1, 42, 1500000, '2028-06-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (121, NULL, 1, 1, 1, 43, 1500000, '2028-07-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (122, NULL, 1, 1, 1, 44, 1500000, '2028-08-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (123, NULL, 1, 1, 1, 45, 1500000, '2028-09-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (124, NULL, 1, 1, 1, 46, 1500000, '2028-10-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (125, NULL, 1, 1, 1, 47, 1500000, '2028-11-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (126, NULL, 1, 1, 1, 48, 1500000, '2028-12-01', 0, 0, NULL, '2024-12-24 01:22:17', '2024-12-24 01:22:17');
INSERT INTO `payments` VALUES (127, 3, NULL, 8, NULL, 1, 50000, '2025-01-01', 1, 0, NULL, '2024-12-30 06:49:31', '2025-01-15 15:50:15');
INSERT INTO `payments` VALUES (128, 3, NULL, 8, NULL, 2, 50000, '2025-02-01', 1, 0, NULL, '2024-12-30 06:49:31', '2025-01-15 15:50:38');
INSERT INTO `payments` VALUES (129, 3, NULL, 8, NULL, 3, 50000, '2025-03-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (130, 3, NULL, 8, NULL, 4, 50000, '2025-04-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (131, 3, NULL, 8, NULL, 5, 50000, '2025-05-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (132, 3, NULL, 8, NULL, 6, 50000, '2025-06-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (133, 3, NULL, 8, NULL, 7, 50000, '2025-07-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (134, 3, NULL, 8, NULL, 8, 50000, '2025-08-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (135, 3, NULL, 8, NULL, 9, 50000, '2025-09-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (136, 3, NULL, 8, NULL, 10, 50000, '2025-10-01', 0, 0, NULL, '2024-12-30 06:49:31', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (137, 4, NULL, 8, NULL, 1, 50000, '2025-01-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (138, 4, NULL, 8, NULL, 2, 50000, '2025-02-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (139, 4, NULL, 8, NULL, 3, 50000, '2025-03-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (140, 4, NULL, 8, NULL, 4, 50000, '2025-04-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (141, 4, NULL, 8, NULL, 5, 50000, '2025-05-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (142, 4, NULL, 8, NULL, 6, 50000, '2025-06-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (143, 4, NULL, 8, NULL, 7, 50000, '2025-07-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (144, 4, NULL, 8, NULL, 8, 50000, '2025-08-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (145, 4, NULL, 8, NULL, 9, 50000, '2025-09-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (146, 4, NULL, 8, NULL, 10, 50000, '2025-10-01', 0, 0, NULL, '2024-12-30 08:19:03', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (147, 5, NULL, 8, NULL, 1, 52000, '2025-01-01', 0, 0, NULL, '2024-12-30 09:30:06', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (148, 5, NULL, 8, NULL, 2, 52000, '2025-02-01', 0, 0, NULL, '2024-12-30 09:30:06', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (149, 5, NULL, 8, NULL, 3, 52000, '2025-03-01', 0, 0, NULL, '2024-12-30 09:30:06', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (150, 5, NULL, 8, NULL, 4, 52000, '2025-04-01', 0, 0, NULL, '2024-12-30 09:30:06', '2024-12-30 09:33:41');
INSERT INTO `payments` VALUES (284, 21, NULL, 1, NULL, 1, 2300, '2025-04-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (285, 21, NULL, 1, NULL, 2, 2300, '2025-05-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (286, 21, NULL, 1, NULL, 3, 2300, '2025-06-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (287, 21, NULL, 1, NULL, 4, 2300, '2025-07-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (288, 21, NULL, 1, NULL, 5, 2300, '2025-08-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (289, 21, NULL, 1, NULL, 6, 2300, '2025-09-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (290, 21, NULL, 1, NULL, 7, 2300, '2025-10-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (291, 21, NULL, 1, NULL, 8, 2300, '2025-11-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (292, 21, NULL, 1, NULL, 9, 2300, '2025-12-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (293, 21, NULL, 1, NULL, 10, 2300, '2026-01-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (294, 21, NULL, 1, NULL, 11, 2300, '2026-02-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (295, 21, NULL, 1, NULL, 12, 2300, '2026-03-01', 0, 0, NULL, '2025-03-05 22:26:07', '2025-03-05 22:26:07');
INSERT INTO `payments` VALUES (296, 22, NULL, 1, NULL, 1, 35000, '2025-04-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (297, 22, NULL, 1, NULL, 2, 35000, '2025-05-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (298, 22, NULL, 1, NULL, 3, 35000, '2025-06-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (299, 22, NULL, 1, NULL, 4, 35000, '2025-07-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (300, 22, NULL, 1, NULL, 5, 35000, '2025-08-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (301, 22, NULL, 1, NULL, 6, 35000, '2025-09-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (302, 22, NULL, 1, NULL, 7, 35000, '2025-10-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (303, 22, NULL, 1, NULL, 8, 35000, '2025-11-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (304, 22, NULL, 1, NULL, 9, 35000, '2025-12-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (305, 22, NULL, 1, NULL, 10, 35000, '2026-01-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (306, 22, NULL, 1, NULL, 11, 35000, '2026-02-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (307, 22, NULL, 1, NULL, 12, 35000, '2026-03-01', 0, 0, NULL, '2025-03-05 22:30:20', '2025-03-05 22:30:20');
INSERT INTO `payments` VALUES (308, 23, NULL, 23, NULL, 1, 24000, '2025-04-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (309, 23, NULL, 23, NULL, 2, 24000, '2025-05-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (310, 23, NULL, 23, NULL, 3, 24000, '2025-06-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (311, 23, NULL, 23, NULL, 4, 24000, '2025-07-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (312, 23, NULL, 23, NULL, 5, 24000, '2025-08-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (313, 23, NULL, 23, NULL, 6, 24000, '2025-09-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (314, 23, NULL, 23, NULL, 7, 24000, '2025-10-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (315, 23, NULL, 23, NULL, 8, 24000, '2025-11-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (316, 23, NULL, 23, NULL, 9, 24000, '2025-12-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (317, 23, NULL, 23, NULL, 10, 24000, '2026-01-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (318, 23, NULL, 23, NULL, 11, 24000, '2026-02-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (319, 23, NULL, 23, NULL, 12, 24000, '2026-03-01', 0, 0, NULL, '2025-03-05 23:02:18', '2025-03-05 23:02:18');
INSERT INTO `payments` VALUES (320, 24, NULL, 23, NULL, 1, 35000, '2025-04-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (321, 24, NULL, 23, NULL, 2, 35000, '2025-05-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (322, 24, NULL, 23, NULL, 3, 35000, '2025-06-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (323, 24, NULL, 23, NULL, 4, 35000, '2025-07-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (324, 24, NULL, 23, NULL, 5, 35000, '2025-08-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (325, 24, NULL, 23, NULL, 6, 35000, '2025-09-01', 0, 0, NULL, '2025-03-05 23:23:09', '2025-03-05 23:23:09');
INSERT INTO `payments` VALUES (326, 24, NULL, 23, NULL, 7, 35000, '2025-10-01', 0, 0, NULL, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `payments` VALUES (327, 24, NULL, 23, NULL, 8, 35000, '2025-11-01', 0, 0, NULL, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `payments` VALUES (328, 24, NULL, 23, NULL, 9, 35000, '2025-12-01', 0, 0, NULL, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `payments` VALUES (329, 24, NULL, 23, NULL, 10, 35000, '2026-01-01', 0, 0, NULL, '2025-03-05 23:23:10', '2025-03-05 23:23:10');
INSERT INTO `payments` VALUES (424, 31, NULL, 23, NULL, 1, 1200, '2025-04-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (425, 31, NULL, 23, NULL, 2, 1200, '2025-05-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (426, 31, NULL, 23, NULL, 3, 1200, '2025-06-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (427, 31, NULL, 23, NULL, 4, 1200, '2025-07-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (428, 31, NULL, 23, NULL, 5, 1200, '2025-08-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (429, 31, NULL, 23, NULL, 6, 1200, '2025-09-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (430, 31, NULL, 23, NULL, 7, 1200, '2025-10-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (431, 31, NULL, 23, NULL, 8, 1200, '2025-11-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (432, 31, NULL, 23, NULL, 9, 1200, '2025-12-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (433, 31, NULL, 23, NULL, 10, 1200, '2026-01-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (434, 31, NULL, 23, NULL, 11, 1200, '2026-02-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (435, 31, NULL, 23, NULL, 12, 1200, '2026-03-01', 0, 0, NULL, '2025-03-06 11:30:30', '2025-03-06 11:30:30');
INSERT INTO `payments` VALUES (471, 49, NULL, 23, NULL, 1, 20000, '2025-04-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (472, 49, NULL, 23, NULL, 2, 20000, '2025-05-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (473, 49, NULL, 23, NULL, 3, 20000, '2025-06-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (474, 49, NULL, 23, NULL, 4, 20000, '2025-07-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (475, 49, NULL, 23, NULL, 5, 20000, '2025-08-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (476, 49, NULL, 23, NULL, 6, 20000, '2025-09-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (477, 49, NULL, 23, NULL, 7, 20000, '2025-10-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (478, 49, NULL, 23, NULL, 8, 20000, '2025-11-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (479, 49, NULL, 23, NULL, 9, 20000, '2025-12-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');
INSERT INTO `payments` VALUES (480, 49, NULL, 23, NULL, 10, 20000, '2026-01-01', 0, 0, NULL, '2025-03-06 22:43:33', '2025-03-06 22:43:33');

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `permissions_name_guard_name_unique`(`name` ASC, `guard_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permissions
-- ----------------------------

-- ----------------------------
-- Table structure for personal_access_tokens
-- ----------------------------
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `personal_access_tokens_token_unique`(`token` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of personal_access_tokens
-- ----------------------------

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `price` decimal(8, 2) NOT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `custom_product` int NOT NULL DEFAULT 0,
  `supplier_price` decimal(8, 2) NOT NULL DEFAULT 0.00,
  `supplier_id` bigint UNSIGNED NULL DEFAULT NULL,
  `stock` int NOT NULL DEFAULT 0,
  `wp_id` bigint UNSIGNED NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK_products_suppliers_foreign_key`(`supplier_id` ASC) USING BTREE,
  CONSTRAINT `FK_products_suppliers_foreign_key` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `products_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 255 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of products
-- ----------------------------
INSERT INTO `products` VALUES (1, 'Ankle Strap (set of 2)', 70.00, 'product', 0, 3.37, 2, 0, NULL, '2025-01-29 05:18:25', '2024-12-12 11:46:22', '2025-01-29 05:18:25');
INSERT INTO `products` VALUES (2, 'Black Cables, Classic (set of 2)', 240.00, 'product', 0, 0.00, NULL, 0, NULL, '2025-01-29 09:32:37', '2024-12-12 11:46:22', '2025-01-29 09:32:37');
INSERT INTO `products` VALUES (3, 'Black Moon Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (4, 'Bungee Cuff, Classic', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (5, 'Footstraps, Classic (set of 2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (6, 'Bungee, Classic (M2)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (7, 'Bungee, Classic (M3)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (12, 'Carabiner (set of 2)', 3.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (13, 'Carabiner (Single)', 2.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (14, 'Door Mount Bracket', 50.00, 'product', 0, 27.22, 4, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (15, 'Extension Straps', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (16, 'Inflatable Pad', 50.00, 'product', 0, 5.90, 3, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (17, 'Bungee Attachment (Micro)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (18, 'Cables, Micro (set of 2)', 95.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (19, 'The Micro Cables w/ Black Handle, Bundle (set of 2)', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (20, 'Micro Cables w/ Footstrap Handle Bundle (set of 2)', 190.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (21, 'The Micro Cables w/ Footstraps & Black Handles, Bundle (set of 2)', 280.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (22, 'Grey Spring, Medium (Micro)', 30.00, 'product', 0, 3.88, 2, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (23, 'Handles (Micro)', 190.00, 'product', 0, 42.96, 4, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (24, 'Pulley Cable Bundle (Micro)', 370.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (25, 'Pulley Cables (Micro)', 320.00, 'product', 0, 45.48, 4, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (26, 'Rear Platform (Micro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (27, 'Storage Wall Bracket (Micro)', 40.00, 'product', 0, 4.82, 3, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (28, 'Strap (Micro)', 60.00, 'product', 0, 2.55, 2, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (29, 'White Spring, Super Light (Micro)', 19.00, 'product', 0, 2.43, 1, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (30, 'The Lift Kit', 900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (31, 'Strap (Mini)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (32, 'Handles (Mini/Mini Pro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (33, 'Rear Platform (Mini/Mini Pro)', 490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (34, 'Rope Handle (set of 2)', 70.00, 'product', 0, 5.06, 3, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (35, 'Sliders', 20.00, 'product', 0, 1.69, 1, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (36, 'To Go Kit', 461.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (37, 'Universal Belt', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (38, 'Universal Bungee', 70.00, 'product', 0, 3.44, 2, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (39, 'Water Bottle', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (40, 'Water Bottle + Sliders (Bundle)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (41, 'Black Spring, Medium (M2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (42, 'Blue Spring, Heavy (M2)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (43, 'Carriage Strap (M2)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (44, 'Platform Strap (M2)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (46, 'Yellow Spring, Light (M2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (48, 'Top Handle Covers, M2/M3 (set of 4)', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (51, 'Carriage Strap (M3)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (52, 'Curved Covers, M3 (set of 4)', 220.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (53, 'Platform Strap (M3)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (54, 'Red Spring, Heavy (M3)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (56, 'Yellow Spring, Light (M3)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (61, 'Platform Strap (M3K)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (65, 'Mega Wheels', 10.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (66, 'Micro Black Spring - Light', 25.00, 'product', 0, 3.11, 2, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (68, 'Micro Red Spring - Heavy', 35.00, 'product', 0, 3.94, 3, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (70, 'New Footstraps (set of 2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (71, 'New Long Cables (set of 2)', 260.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (73, 'Red Mega Spring Knobs', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (74, 'Resistance Bungee (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (76, 'Side Panels (No Light)', 500.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (77, 'Side Panels (with Light)', 650.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (78, 'The Micro', 990.00, 'product', 0, 211.04, 4, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (79, 'The Micro Prime', 2215.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (80, 'The Mini Pro', 3490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (81, 'The Micro (Fully Loaded)', 2290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (82, 'The Ramp', 5900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (84, 'The Micro (OPEN BOX)', 990.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (85, 'The Micro Deluxe', 1995.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (87, 'Universal Cuff (set of 2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (88, 'Universal Cuff (Single)', 20.00, 'product', 0, 1.16, 1, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (91, 'Willow Balm', 10.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (92, 'Mega Spring Knobs (Yellow)', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (93, 'The Mini', 2490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (95, 'Soft Shell Moon Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (96, 'Soft Shell Crossbody Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (97, 'Soft Shell Nano Bag (Lagree x Got Bag)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (98, 'Black Nano Bag (Lagree x Got Bag)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (100, 'Mat (Lagree x Stakt)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (101, 'The Micro Pro', 1590.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (102, 'Handles (Micro Pro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (103, 'Rear Platform (Micro Pro)', 490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (105, 'Training fee', 1500.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:25:47', '2024-12-24 14:20:13', '2025-02-05 01:25:47');
INSERT INTO `products` VALUES (106, 'License - Micro', 1990.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:15:44', '2024-12-24 14:20:27', '2025-02-05 01:15:44');
INSERT INTO `products` VALUES (107, 'License - Mini', 1990.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:19', '2024-12-24 14:21:19');
INSERT INTO `products` VALUES (108, 'License - Mega', 3990.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-04 15:41:02', '2024-12-24 14:21:32', '2025-02-04 15:41:02');
INSERT INTO `products` VALUES (109, 'License - EVO', 3990.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-04 15:39:34', '2024-12-24 14:21:46', '2025-02-04 15:39:34');
INSERT INTO `products` VALUES (110, 'Bundle #1 (minimum)', 800.00, 'bundle', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:22:58', '2024-12-24 14:22:58');
INSERT INTO `products` VALUES (111, 'Bundle #2 (recommended)', 1320.00, 'bundle', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:23:14', '2024-12-24 14:23:14');
INSERT INTO `products` VALUES (112, 'Black rubberized steel cables', 260.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (113, 'Double handle/Foot strap (set)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (114, 'Platform straps', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (115, 'Universal strap', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (116, 'The Pole', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (117, 'Red rubberized steel cables', 250.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (118, 'Red handles (pair)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (119, 'Bungee chord + Bungee bar', 150.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (120, 'X-Strap (M3)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (121, 'The Mega Pro', 8900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (122, 'Ab Wheel + Mat (Bundle)', 140.00, 'product', 0, 60.00, NULL, 22, NULL, '2025-01-29 01:42:16', '2024-12-12 11:46:23', '2025-01-29 01:42:16');
INSERT INTO `products` VALUES (123, 'Bear Hoodie (Ivory)', 102.00, 'product', 0, 0.00, NULL, 0, NULL, '2025-01-29 09:33:50', '2024-12-12 11:46:23', '2025-01-29 09:33:50');
INSERT INTO `products` VALUES (124, 'Bear Shorts (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, '2025-01-29 09:00:14', '2024-12-12 11:46:23', '2025-01-29 09:00:14');
INSERT INTO `products` VALUES (125, 'Black Follow Me Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, '2025-01-29 10:03:59', '2024-12-12 11:46:23', '2025-01-29 10:03:59');
INSERT INTO `products` VALUES (126, 'Black I Totally Lagree Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (127, 'Black Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (128, 'Gray Zip-up', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (129, 'Classic Short Cables (set of 2)', 230.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (130, 'Cobra Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (131, 'Cobra Bra (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (132, 'Cropped Crewneck (White)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (133, 'Follow Me Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (134, 'Gray Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (135, 'Grip Tops Crop (Lagree x Arebesk)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (136, 'Handles, Classic (set of 2)', 60.00, 'product', 0, 1.17, 1, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (137, 'I Totally Lagree Sweatshirt (Dark Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (138, 'I Totally Lagree Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (139, 'Ice Breaker Bra (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (140, 'Ice Breaker Bra (Oatmeal)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:20:27', '2024-12-24 14:20:27');
INSERT INTO `products` VALUES (141, 'Lunger Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (142, 'Lunger Legging (Charcoal)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (143, 'Mega Ab Wheel', 40.00, 'product', 0, 10.95, 4, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (144, 'Mega Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (145, 'Megababe Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (146, 'Megababe Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (147, 'Misc', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (148, 'Muse Closed Toe (Black)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (149, 'New Handles (set of 2)', 60.00, 'product', 0, 1.03, 1, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (150, 'New Short Cables (set of 2)', 250.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (151, 'Newspaper Tank (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (152, 'Newspaper Tank (Ivory)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (153, 'Pardon My French Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (154, 'Phish Net Closed Toe (Black/White)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (155, 'Pike V-Neck Tee (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (156, 'Pike V-Neck Tee (Ivory)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (157, 'Puffer Tote Bag (Lagree x Got Bag)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (158, 'Racerback', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (159, 'Racerback + Lagree Water Bottle (Bundle)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (160, 'Racerback + Lagree Water Bottle + SCSG Sweater (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (161, 'Racerback + Lagree Water Bottle + Sweater (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:32', '2024-12-24 14:21:32');
INSERT INTO `products` VALUES (162, 'Resistance Bungees', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (163, 'Ribbon Tank (Pink)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (164, 'Rower Tee (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (165, 'Rower Tee (Oatmeal)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:46', '2024-12-24 14:21:46');
INSERT INTO `products` VALUES (166, 'Runners Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (167, 'Saw Tank (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (168, 'Saw Tank (Mocha)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (169, 'SCSG Sweater + Lagree Water Bottle (Bundle)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (170, 'Sexy Angel Brami (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (171, 'Sexy Angel Brami (Oatmeal)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (172, 'Sexy Back Bra (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (173, 'Skater Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (174, 'Skater Legging (Walnut)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (175, 'SoCal, So Lagree Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (176, 'Soul Train Long Sleeve (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (177, 'Soul Train Long Sleeve (Oatmeal)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (178, 'Spider Leggings (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (179, 'Spoon Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (180, 'Spoon Bra (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (181, 'Spoon Bra (Mocha)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (182, 'Spring with Shroud', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (184, 'Spring with Shroud (Sets)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (185, 'Square Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:34:14', '2024-12-24 14:34:14');
INSERT INTO `products` VALUES (186, 'Supra Short (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (187, 'Supra Short (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (188, 'Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (189, 'Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (192, 'Totally Lagree', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (193, 'Twisted Brami (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (194, 'Twisted Brami (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (195, 'Twisted Brami (Walnut)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:23', '2024-12-12 11:46:23');
INSERT INTO `products` VALUES (196, 'White Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (197, 'X-Strap (M2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL);
INSERT INTO `products` VALUES (198, 'Black Zip-up', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 11:46:22', '2024-12-12 11:46:22');
INSERT INTO `products` VALUES (199, 'License - Micro (Deposit)', 490.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:20:27', '2024-12-24 14:20:27');
INSERT INTO `products` VALUES (200, 'License - Mini (Deposit)', 490.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 14:21:19', '2024-12-24 14:21:19');
INSERT INTO `products` VALUES (201, 'License - Mega (Deposit)', 493.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:10:57', '2024-12-24 14:21:32', '2025-02-05 01:10:57');
INSERT INTO `products` VALUES (202, 'Lease - Mega Pro (Deposit)', 4002.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-04 15:40:41', '2024-12-24 14:20:27', '2025-02-04 15:40:41');
INSERT INTO `products` VALUES (204, 'Platform Straps', 80.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 11:00:40', '2025-01-24 11:00:40');
INSERT INTO `products` VALUES (205, 'Carriage Straps', 100.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 11:00:40', '2025-01-24 11:00:40');
INSERT INTO `products` VALUES (206, 'X-strap', 90.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 11:00:40', '2025-01-24 11:00:40');
INSERT INTO `products` VALUES (213, 'ASD ASD AS D', 222.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:23:16', '2025-01-26 12:09:58');
INSERT INTO `products` VALUES (214, 'asdasdasd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:28:14', '2025-01-26 05:28:14');
INSERT INTO `products` VALUES (215, 'asdasd aaaaaaaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:36:30', '2025-01-26 12:09:58');
INSERT INTO `products` VALUES (216, 'bbbbbbbbbbbbbb', 21.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:36:49', '2025-01-26 12:09:58');
INSERT INTO `products` VALUES (217, 'asd asd asd ads', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:37:09', '2025-01-26 05:37:09');
INSERT INTO `products` VALUES (218, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:37:26', '2025-01-26 05:37:26');
INSERT INTO `products` VALUES (219, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:38:05', '2025-01-26 05:38:05');
INSERT INTO `products` VALUES (220, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:38:16', '2025-01-26 05:38:16');
INSERT INTO `products` VALUES (221, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:39:48', '2025-01-26 05:39:48');
INSERT INTO `products` VALUES (222, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:40:39', '2025-01-26 05:40:39');
INSERT INTO `products` VALUES (223, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:41:30', '2025-01-26 05:41:30');
INSERT INTO `products` VALUES (224, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 05:42:28', '2025-01-26 06:57:45');
INSERT INTO `products` VALUES (225, 'custom door', 12.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 08:00:12', '2025-01-26 08:00:12');
INSERT INTO `products` VALUES (226, 'asdasd asd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 08:04:12', '2025-01-26 08:04:12');
INSERT INTO `products` VALUES (227, 'asd asd asd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 08:09:08', '2025-01-26 08:09:08');
INSERT INTO `products` VALUES (228, 'aaaaaaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 11:32:27', '2025-01-26 11:32:27');
INSERT INTO `products` VALUES (229, 'asdasdas das dasd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 11:32:49', '2025-01-26 12:18:24');
INSERT INTO `products` VALUES (230, 'aaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 12:46:30', '2025-01-26 13:28:12');
INSERT INTO `products` VALUES (231, 'bbbbbbb', 44.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 12:46:30', '2025-01-26 13:28:12');
INSERT INTO `products` VALUES (232, 'ccccc', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 13:28:12', '2025-01-26 13:28:12');
INSERT INTO `products` VALUES (233, 'cddddddd', 33.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 13:28:12', '2025-01-26 13:28:12');
INSERT INTO `products` VALUES (234, 'Test product', 350.00, NULL, 0, 0.00, NULL, 0, NULL, '2025-01-30 05:26:52', '2025-01-30 05:12:25', '2025-01-30 05:26:52');
INSERT INTO `products` VALUES (235, 'New new', 350.00, NULL, 0, 50.00, 1, 0, NULL, '2025-01-30 05:26:40', '2025-01-30 05:26:22', '2025-01-30 05:26:40');
INSERT INTO `products` VALUES (236, 'Test product', 275.00, NULL, 1, 32.00, 4, 0, NULL, NULL, '2025-01-30 05:27:10', '2025-01-30 05:27:10');
INSERT INTO `products` VALUES (237, 'another test', 1600.00, 'product', 0, 300.00, 4, 0, NULL, '2025-02-05 01:10:45', '2025-01-30 05:30:43', '2025-02-05 01:10:45');
INSERT INTO `products` VALUES (238, 'another test', 2341.00, 'product', 0, 23.00, 1, 0, NULL, NULL, '2025-01-30 05:32:51', '2025-01-30 05:32:51');
INSERT INTO `products` VALUES (239, 'asdf', 123.00, NULL, 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:31:51', '2025-02-05 01:31:51');
INSERT INTO `products` VALUES (240, 'another', 1231.00, NULL, 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:32:05', '2025-02-05 01:32:05');
INSERT INTO `products` VALUES (241, 'test fee', 123.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:33:51', '2025-02-05 01:33:51');
INSERT INTO `products` VALUES (242, 'asetdss', 222.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:36:32', '2025-02-05 01:36:32');
INSERT INTO `products` VALUES (243, '22222', 111.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:57:21', '2025-02-05 01:37:07', '2025-02-05 01:57:21');
INSERT INTO `products` VALUES (244, '22222', 111.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:57:25', '2025-02-05 01:39:41', '2025-02-05 01:57:25');
INSERT INTO `products` VALUES (245, '22222', 111.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:57:31', '2025-02-05 01:44:57', '2025-02-05 01:57:31');
INSERT INTO `products` VALUES (246, '22222', 111.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-10 07:16:36', '2025-02-05 01:45:06', '2025-02-10 07:16:36');
INSERT INTO `products` VALUES (247, '22222', 111.00, 'fee', 0, 0.00, NULL, 0, NULL, '2025-02-05 01:57:38', '2025-02-05 01:49:39', '2025-02-05 01:57:38');
INSERT INTO `products` VALUES (248, '2e2e2', 123.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:50:00', '2025-02-05 01:50:00');
INSERT INTO `products` VALUES (249, '2e2e2', 123.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:50:49', '2025-02-05 01:50:49');
INSERT INTO `products` VALUES (250, 'asdfasdf', 123.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 01:59:41', '2025-02-05 01:59:41');
INSERT INTO `products` VALUES (251, 'asdfasdf', 123.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 02:00:16', '2025-02-05 02:00:16');
INSERT INTO `products` VALUES (252, 'Test Fee Final', 12311.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 02:00:30', '2025-02-05 02:00:30');
INSERT INTO `products` VALUES (253, 'Black I', 122223.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2025-02-05 02:02:41', '2025-02-05 02:02:41');
INSERT INTO `products` VALUES (254, 'cxvzxcvzxcvzxv', 123.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-02-12 12:42:26', '2025-02-12 12:52:57');

-- ----------------------------
-- Table structure for role_has_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_has_permissions`;
CREATE TABLE `role_has_permissions`  (
  `permission_id` bigint UNSIGNED NOT NULL,
  `role_id` bigint UNSIGNED NOT NULL,
  PRIMARY KEY (`permission_id`, `role_id`) USING BTREE,
  INDEX `role_has_permissions_role_id_foreign`(`role_id` ASC) USING BTREE,
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_has_permissions
-- ----------------------------

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `roles_name_guard_name_unique`(`name` ASC, `guard_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (1, 'super-admin', 'web', '2024-12-12 12:46:22', '2024-12-12 12:46:22');
INSERT INTO `roles` VALUES (2, 'customer', 'web', '2024-12-12 12:46:22', '2024-12-12 12:46:22');

-- ----------------------------
-- Table structure for states
-- ----------------------------
DROP TABLE IF EXISTS `states`;
CREATE TABLE `states`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of states
-- ----------------------------
INSERT INTO `states` VALUES (1, 'Alabama', NULL, NULL);
INSERT INTO `states` VALUES (2, 'Alaska', NULL, NULL);
INSERT INTO `states` VALUES (3, 'Arizona', NULL, NULL);
INSERT INTO `states` VALUES (4, 'Arkansas', NULL, NULL);
INSERT INTO `states` VALUES (5, 'California', NULL, NULL);
INSERT INTO `states` VALUES (6, 'Colorado', NULL, NULL);
INSERT INTO `states` VALUES (7, 'Connecticut', NULL, NULL);
INSERT INTO `states` VALUES (8, 'Delaware', NULL, NULL);
INSERT INTO `states` VALUES (9, 'Florida', NULL, NULL);
INSERT INTO `states` VALUES (10, 'Georgia', NULL, NULL);
INSERT INTO `states` VALUES (11, 'Hawaii', NULL, NULL);
INSERT INTO `states` VALUES (12, 'Idaho', NULL, NULL);
INSERT INTO `states` VALUES (13, 'Illinois', NULL, NULL);
INSERT INTO `states` VALUES (14, 'Indiana', NULL, NULL);
INSERT INTO `states` VALUES (15, 'Iowa', NULL, NULL);
INSERT INTO `states` VALUES (16, 'Kansas', NULL, NULL);
INSERT INTO `states` VALUES (17, 'Kentucky', NULL, NULL);
INSERT INTO `states` VALUES (18, 'Louisiana', NULL, NULL);
INSERT INTO `states` VALUES (19, 'Maine', NULL, NULL);
INSERT INTO `states` VALUES (20, 'Maryland', NULL, NULL);
INSERT INTO `states` VALUES (21, 'Massachusetts', NULL, NULL);
INSERT INTO `states` VALUES (22, 'Michigan', NULL, NULL);
INSERT INTO `states` VALUES (23, 'Minnesota', NULL, NULL);
INSERT INTO `states` VALUES (24, 'Mississippi', NULL, NULL);
INSERT INTO `states` VALUES (25, 'Missouri', NULL, NULL);
INSERT INTO `states` VALUES (26, 'Montana', NULL, NULL);
INSERT INTO `states` VALUES (27, 'Nebraska', NULL, NULL);
INSERT INTO `states` VALUES (28, 'Nevada', NULL, NULL);
INSERT INTO `states` VALUES (29, 'New Hampshire', NULL, NULL);
INSERT INTO `states` VALUES (30, 'New Jersey', NULL, NULL);
INSERT INTO `states` VALUES (31, 'New Mexico', NULL, NULL);
INSERT INTO `states` VALUES (32, 'New York', NULL, NULL);
INSERT INTO `states` VALUES (33, 'North Carolina', NULL, NULL);
INSERT INTO `states` VALUES (34, 'North Dakota', NULL, NULL);
INSERT INTO `states` VALUES (35, 'Ohio', NULL, NULL);
INSERT INTO `states` VALUES (36, 'Oklahoma', NULL, NULL);
INSERT INTO `states` VALUES (37, 'Oregon', NULL, NULL);
INSERT INTO `states` VALUES (38, 'Pennsylvania', NULL, NULL);
INSERT INTO `states` VALUES (39, 'Rhode Island', NULL, NULL);
INSERT INTO `states` VALUES (40, 'South Carolina', NULL, NULL);
INSERT INTO `states` VALUES (41, 'South Dakota', NULL, NULL);
INSERT INTO `states` VALUES (42, 'Tennessee', NULL, NULL);
INSERT INTO `states` VALUES (43, 'Texas', NULL, NULL);
INSERT INTO `states` VALUES (44, 'Utah', NULL, NULL);
INSERT INTO `states` VALUES (45, 'Vermont', NULL, NULL);
INSERT INTO `states` VALUES (46, 'Virginia', NULL, NULL);
INSERT INTO `states` VALUES (47, 'Washington', NULL, NULL);
INSERT INTO `states` VALUES (48, 'West Virginia', NULL, NULL);
INSERT INTO `states` VALUES (49, 'Wisconsin', NULL, NULL);
INSERT INTO `states` VALUES (50, 'Wyoming', NULL, NULL);
INSERT INTO `states` VALUES (51, 'American Samoa', NULL, NULL);
INSERT INTO `states` VALUES (52, 'District of Columbia', NULL, NULL);
INSERT INTO `states` VALUES (53, 'Federated States of Micronesia', NULL, NULL);
INSERT INTO `states` VALUES (54, 'Guam', NULL, NULL);
INSERT INTO `states` VALUES (55, 'Marshall Islands', NULL, NULL);
INSERT INTO `states` VALUES (56, 'Northern Mariana Islands', NULL, NULL);
INSERT INTO `states` VALUES (57, 'Palau', NULL, NULL);
INSERT INTO `states` VALUES (58, 'Puerto Rico', NULL, NULL);
INSERT INTO `states` VALUES (59, 'Virgin Islands', NULL, NULL);

-- ----------------------------
-- Table structure for studios
-- ----------------------------
DROP TABLE IF EXISTS `studios`;
CREATE TABLE `studios`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `state_id` bigint UNSIGNED NULL DEFAULT NULL,
  `country_id` bigint UNSIGNED NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner_last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `studios_country_id_foreign`(`country_id` ASC) USING BTREE,
  INDEX `studios_state_id_foreign`(`state_id` ASC) USING BTREE,
  CONSTRAINT `studios_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `studios_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of studios
-- ----------------------------
INSERT INTO `studios` VALUES (1, 35, NULL, 'Sandra Howe', 'Bethany', 'Krajcik', '<EMAIL>', '675-883-8711', '3860 Adriel Park', 'Greensboro', '02828-0436', '2024-12-16 11:02:34', '2024-12-16 11:02:34', NULL, NULL);
INSERT INTO `studios` VALUES (2, 4, NULL, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 06:49:31', '2024-12-30 06:49:31', NULL, NULL);
INSERT INTO `studios` VALUES (3, 43, NULL, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 08:19:03', '2024-12-30 08:19:03', NULL, NULL);
INSERT INTO `studios` VALUES (4, 5, 2, 'test', 'test', 'qweq', '<EMAIL>', '78678', 'qwe', 'qweqwe', 'qwe', '2024-12-30 09:30:06', '2024-12-30 09:30:06', NULL, NULL);
INSERT INTO `studios` VALUES (5, 4, 5, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 09:30:43', '2024-12-30 09:30:43', NULL, NULL);
INSERT INTO `studios` VALUES (8, 32, NULL, 'Yukio_studio', 'Yukio', 'Takahashi', '<EMAIL>', '123456789', 'address 1', 'city 2', '37400', '2025-03-05 22:30:20', '2025-03-05 22:30:20', NULL, 'USA');

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `state_id` bigint UNSIGNED NULL DEFAULT NULL,
  `country_id` bigint UNSIGNED NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `zip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `suppliers_email_unique`(`email` ASC) USING BTREE,
  INDEX `suppliers_state_id_foreign`(`state_id` ASC) USING BTREE,
  INDEX `suppliers_country_id_foreign`(`country_id` ASC) USING BTREE,
  CONSTRAINT `suppliers_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `suppliers_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of suppliers
-- ----------------------------
INSERT INTO `suppliers` VALUES (1, 1, 192, 'Anlap', '<EMAIL>', NULL, '+84 38 4545 777', 'The Prince Residence, 17-19-21 Nguyen Van Troi St.', 'Ho Chi Minh', '00700', 1, 'International', NULL, '2024-12-23 05:55:30', '2024-12-23 05:55:30');
INSERT INTO `suppliers` VALUES (2, 29, 152, 'V-test supplier', '<EMAIL>', NULL, '+1 (593) 947-5027', 'Nisi earum ipsum mo', 'Et blanditiis nobis', '69430', 0, 'International', NULL, '2025-01-03 01:50:19', '2025-01-08 11:17:36');
INSERT INTO `suppliers` VALUES (3, 52, NULL, 'Lindsey Stroman', '<EMAIL>', NULL, '************', 'asdasd', 'asdasd', '123123', 1, 'International', NULL, '2025-01-08 12:28:47', '2025-01-08 12:28:47');
INSERT INTO `suppliers` VALUES (4, NULL, NULL, 'asdasd', '<EMAIL>', NULL, '123123', NULL, NULL, NULL, 1, 'USA', NULL, '2025-01-09 06:47:32', '2025-01-09 06:47:32');

-- ----------------------------
-- Table structure for telescope_entries
-- ----------------------------
DROP TABLE IF EXISTS `telescope_entries`;
CREATE TABLE `telescope_entries`  (
  `sequence` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `family_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`sequence`) USING BTREE,
  UNIQUE INDEX `telescope_entries_uuid_unique`(`uuid` ASC) USING BTREE,
  INDEX `telescope_entries_batch_id_index`(`batch_id` ASC) USING BTREE,
  INDEX `telescope_entries_family_hash_index`(`family_hash` ASC) USING BTREE,
  INDEX `telescope_entries_created_at_index`(`created_at` ASC) USING BTREE,
  INDEX `telescope_entries_type_should_display_on_index_index`(`type` ASC, `should_display_on_index` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of telescope_entries
-- ----------------------------

-- ----------------------------
-- Table structure for telescope_entries_tags
-- ----------------------------
DROP TABLE IF EXISTS `telescope_entries_tags`;
CREATE TABLE `telescope_entries_tags`  (
  `entry_uuid` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`entry_uuid`, `tag`) USING BTREE,
  INDEX `telescope_entries_tags_tag_index`(`tag` ASC) USING BTREE,
  CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of telescope_entries_tags
-- ----------------------------

-- ----------------------------
-- Table structure for telescope_monitoring
-- ----------------------------
DROP TABLE IF EXISTS `telescope_monitoring`;
CREATE TABLE `telescope_monitoring`  (
  `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`tag`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of telescope_monitoring
-- ----------------------------

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `first_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `is_contact` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `users_email_unique`(`email` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'Super Admin', '', '<EMAIL>', NULL, '2024-12-12 12:46:22', '$2y$10$irx52rrQMQubIuckvZlTD.C6Fo/WWxBn95uQPt8PeCVdzafNwQz8S', '12345689', 'YCvRxO9dkIE7fF6cyxwZoti35zXYSKPK5n8Wfp0iPsTV8mNhKLmbfLOKf8Ud', 0, 0, '2024-12-12 12:46:22', '2025-01-16 03:19:16', NULL);
INSERT INTO `users` VALUES (2, 'Sebastien', 'Lagree', '<EMAIL>', NULL, NULL, '$2y$10$S52EGQaCOsf7FON5i/lsVuS2iazC8tgrhvjR4XRSzNvuwgM/Bbksa', '123', NULL, 1, 0, '2024-12-12 04:51:42', '2024-12-31 06:22:01', NULL);
INSERT INTO `users` VALUES (3, 'Michael', 'Something', '<EMAIL>', NULL, NULL, '$2y$10$uyWRF9B4UINIZvL5P55dTufg58OxwsU1jlsBXaYw4vDk9jFVQsDrm', '1233', NULL, 1, 0, '2024-12-18 08:21:01', '2024-12-21 01:47:17', NULL);
INSERT INTO `users` VALUES (4, 'Sasha', 'BIRON', '<EMAIL>', NULL, NULL, '$2y$10$W4QO2Sk.6gyGqk4uhFdIo.4qevRl8UH7LeFEsCwyLMUgGsz4cDK1W', '555', NULL, 1, 0, '2024-12-21 14:38:09', '2024-12-21 14:38:09', NULL);
INSERT INTO `users` VALUES (5, 'Proba', '123', '<EMAIL>', NULL, NULL, '$2y$10$B3Daqxbdpck1XF8/VVz2HeNxW7hh2NwxAlOk7IqysdZOCDhW/6IPO', '4564654564', NULL, 1, 0, '2024-12-23 07:06:32', '2024-12-23 07:06:32', NULL);
INSERT INTO `users` VALUES (6, 'Julie', 'Fayer', '<EMAIL>', NULL, NULL, '$2y$10$COcFwmcbS5eDbUY1yG9f4O4Lw.bP9VvchG3ciX130dl8nP04Sz0su', '***********', NULL, 1, 0, '2024-12-26 08:35:41', '2025-01-03 18:25:31', NULL);
INSERT INTO `users` VALUES (7, 'Romy', 'Drouby', '<EMAIL>', NULL, NULL, '$2y$10$TexBosVSCwf6dLPySOGE5eX5M.sf47iLtTuVFgH5QO.3fcqzDkTWW', '+974-5545-7144', NULL, 1, 0, '2024-12-26 12:10:13', '2024-12-31 04:20:55', NULL);
INSERT INTO `users` VALUES (8, 'Caitlyn', 'Coyle', '<EMAIL>', NULL, NULL, '$2y$10$r6dw6y8SlQAfocWVAg6TSOkPMtCbr8h4iEVY57ZNafmJpKg66WAyO', '555', NULL, 1, 0, '2024-12-27 11:12:58', '2024-12-30 06:03:51', NULL);
INSERT INTO `users` VALUES (9, 'Luka', 'Anđelković', '<EMAIL>', NULL, NULL, '$2y$10$UN6J2dEAQQpJKylYCe7gKOFQObBBiSRRJyz.ECbAwDMQwe/lKZeD.', '0631399084', NULL, 1, 0, '2024-12-30 06:40:40', '2024-12-30 07:56:49', NULL);
INSERT INTO `users` VALUES (10, 'Luka', 'Anđelković', '<EMAIL>', NULL, NULL, '$2y$10$KgBdiv8iRL2hgmHkRLFKAOAa.rsqGAAj4vOIK291Ar40j1eh6uchG', '0631399084', NULL, 1, 0, '2024-12-30 07:07:36', '2024-12-30 07:07:36', NULL);
INSERT INTO `users` VALUES (11, 'Luka', 'Anđelković', '<EMAIL>', NULL, NULL, '$2y$10$QqAroZa9gnNlrrEyHSKU5.CmnZmH.J1Q2qRGP0fMJb1OVjTxSHYne', '0631399084', NULL, 1, 0, '2024-12-30 07:08:27', '2024-12-30 07:08:27', NULL);
INSERT INTO `users` VALUES (12, 'Luka', 'Anđelković', '<EMAIL>', NULL, NULL, '$2y$10$BwhPAhYPtGafpYa7tEYjFOiNYd7dnTPQrlNc4fJUhQ0OxiZbmy./q', '78787897', NULL, 1, 0, '2024-12-31 06:22:53', '2024-12-31 06:22:53', NULL);
INSERT INTO `users` VALUES (13, 'dada', 'dada', '<EMAIL>', NULL, NULL, '$2y$10$rjJY9KlfrOY9P587XKL2SueHPyezpQ1sWUUjNYmZs9qG.0Q9erlKq', '2222', NULL, 1, 0, '2024-12-31 07:48:34', '2024-12-31 07:48:34', NULL);
INSERT INTO `users` VALUES (14, 'dgdg', 'dgdgh', '<EMAIL>', NULL, NULL, '$2y$10$dM/VkL8kCm.ClMGqE8F5MuXL1F9ozw39dAjtxT97tXJfzcyWpdqxS', 'dh', NULL, 1, 0, '2024-12-31 08:27:22', '2024-12-31 08:27:22', NULL);
INSERT INTO `users` VALUES (15, 'milan', 'milan', '<EMAIL>', NULL, NULL, '$2y$10$fFIvGIiPJyltAx0.yIE2s.QBSdPiWcwxy.lX8gojunOFPTWaqmLd.', '123', NULL, 1, 0, '2024-12-31 08:37:10', '2024-12-31 08:37:10', NULL);
INSERT INTO `users` VALUES (16, 'test', 'test', '<EMAIL>', NULL, NULL, '$2y$10$t04XSPNUNwB0mCQy/QGYZu/1zfGaoG4258M6nrogoOzTcDy7T.By6', 'test', NULL, 1, 0, '2024-12-31 08:40:56', '2024-12-31 08:40:56', NULL);
INSERT INTO `users` VALUES (17, 'test', 'test', '<EMAIL>', NULL, NULL, '$2y$10$vVGzfxz7urW0arDG6vnCpec0TRYm/PjoKZaCti3RW28yhKwlq4ewW', 'test', NULL, 0, 0, '2024-12-31 08:41:59', '2024-12-31 08:41:59', NULL);
INSERT INTO `users` VALUES (18, 'Milan', 'Ckomi', '<EMAIL>', NULL, NULL, '$2y$10$iOUAd1SNfHT8kb9fuKGjyOHkrIUAKPLnktxTD6DMgqh0psdEUPD7S', '1230002', NULL, 1, 0, '2024-12-31 08:42:21', '2024-12-31 08:42:21', NULL);
INSERT INTO `users` VALUES (19, 'kdoakod', 'kodako', '<EMAIL>', NULL, NULL, '$2y$10$LQv1kfYI2OWa7YPefoVyB.RcU.isUzjyXOgGvrS09oTcSa7ktgiua', '20202020', NULL, 1, 0, '2024-12-31 08:49:31', '2024-12-31 08:49:31', NULL);
INSERT INTO `users` VALUES (20, 'No name', 'No name', '<EMAIL>', NULL, NULL, '$2y$10$aQgT/ClMpjfe5eHAAqoMhur435byUwxtwNXzDdV/9luk6CNld4rTi', 'No name', NULL, 1, 0, '2024-12-31 08:54:00', '2024-12-31 08:54:00', NULL);
INSERT INTO `users` VALUES (21, 'test', 'test', '<EMAIL>', NULL, '2024-12-12 12:46:22', '$2y$10$8ObqWvcVrFB0rREw4JKB2ezlL.7..Xi4LMMVHOW.XkWezIuSTYWb6', 'tetst', NULL, 0, 0, '2024-12-31 09:42:20', '2025-01-09 03:29:15', NULL);
INSERT INTO `users` VALUES (22, 'Marco', 'Sarco', '<EMAIL>', NULL, NULL, '$2y$10$S5GKJdBx.xGlEY5BsxcGl.4Rgqf/Pu8COjx3VuMopjT8L0msYwhsG', '12121212', NULL, 1, 0, '2024-12-31 10:24:33', '2024-12-31 10:24:33', NULL);
INSERT INTO `users` VALUES (23, 'dada', 'rara', '<EMAIL>', NULL, NULL, '$2y$10$zv.hvoT.73YDv/IeLeEMpe3mRpWFSbnNbWhFq2JB7VwGle0enD.CO', '234556', NULL, 1, 0, '2024-12-31 10:26:55', '2025-01-04 02:44:07', NULL);
INSERT INTO `users` VALUES (24, 'Denise', 'Chakoian', '<EMAIL>', NULL, NULL, '$2y$10$phQ.F4szXdN4d.tZZAQkZ.9lHpxU0fCGvNFz3qDRQWH03HlYZ2436', '555-444-333', NULL, 1, 0, '2025-01-03 14:57:41', '2025-01-03 14:57:41', NULL);
INSERT INTO `users` VALUES (25, 'Sarah', 'Hill', '<EMAIL>', NULL, NULL, '$2y$10$w7uWowOL4ZZPYaWs2ZTUtOb1m5LtlgMu9Qs2./TAO1Rcf3jOI7LLq', '1-604-897-4988', NULL, 1, 0, '2025-01-06 11:49:29', '2025-01-06 11:49:29', NULL);
INSERT INTO `users` VALUES (26, 'Janick', 'Dibbert', '<EMAIL>', NULL, NULL, '$2y$10$tcu0gllVOeNCceKEY1qQm.kuHKMiHSSh1jpp1b1Rbs02MJVru/vi2', '900-868-6984', NULL, 1, 0, '2025-01-08 12:37:10', '2025-01-08 12:37:10', NULL);
INSERT INTO `users` VALUES (27, 'Berniece', 'Bergstrom', '<EMAIL>', NULL, NULL, '$2y$10$C5tZ6CPo3NNzFrEH9z0f0es.6pTyCOathVlC00d.8qKkpVsUz4jI6', '179-756-2230', NULL, 1, 0, '2025-01-08 12:37:18', '2025-01-09 06:42:35', NULL);
INSERT INTO `users` VALUES (28, 'Sebastien', 'Lagree', '<EMAIL>', NULL, '2024-12-12 12:46:22', '$2y$10$x0wp2mmBNL2JTt1F8QDihOcysoWc1amlUTWda6BqXEZkPAwDfl7l6', '1-604-897-4988', NULL, 0, 0, '2025-01-06 11:49:29', '2025-01-06 11:49:29', NULL);
INSERT INTO `users` VALUES (29, 'Jessica', 'Luna', 'jessica@‌imslagree.com', NULL, '2024-12-12 12:46:22', '$2y$10$VE2kOVqFnIy9LlD3dktwqO5/dGy3kjbYHMpNB9gjJ5.qrDVcJBCyW', '1-604-897-4988', NULL, 0, 0, '2025-01-06 11:49:29', '2025-01-06 11:49:29', NULL);
INSERT INTO `users` VALUES (30, 'Rachel', 'Lagree', 'rachel@‌imslagree.com', NULL, '2024-12-12 12:46:22', '$2y$10$PVoiDNcdLw.oNwpsAF4hee28w4MpH6rvS3ou0KmkyTmholxDU1xAq', '1-604-897-4988', NULL, 0, 0, '2025-01-06 11:49:29', '2025-01-06 11:49:29', NULL);
INSERT INTO `users` VALUES (31, 'Chris', 'Thompson', 'chris@‌imslagree.com', NULL, '2024-12-12 12:46:22', '$2y$10$xcEu7cq2GtlXE6AYKaIc6ujbb7m1V9FmXEU1xrvWYmhNTifgral1a', '1-604-897-4988', NULL, 0, 0, '2025-01-06 11:49:29', '2025-01-06 11:49:29', NULL);
INSERT INTO `users` VALUES (32, 'Tasdasd', 'asdasda', '<EMAIL>', NULL, NULL, '$2y$10$lrBzbfTBrqHI8iMBwlPsZ.3ded3ErMSNpLLg3gtFeKF8mbi6S8yhy', 'sdasda', NULL, 1, 0, '2025-01-09 06:43:05', '2025-01-13 08:09:44', NULL);
INSERT INTO `users` VALUES (33, 'Yukio', 'Takahashi', '<EMAIL>', NULL, NULL, '$2y$10$0730JJ43pm1qHGyJb9Ih/ucdnER5/H/CKwByAldWNhKKETuddeRjC', '123456789', NULL, 1, 0, '2025-03-05 08:21:16', '2025-03-05 08:21:16', NULL);

-- ----------------------------
-- Table structure for webhooks
-- ----------------------------
DROP TABLE IF EXISTS `webhooks`;
CREATE TABLE `webhooks`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of webhooks
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
