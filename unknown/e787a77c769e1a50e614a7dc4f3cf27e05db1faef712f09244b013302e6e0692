//  Select2 library styles
@import "select2/src/scss/core"

.select2-dropdown
    min-width: fit-content
    border: 1px solid $light
    border-radius: 0
    box-shadow: 0 0 50px rgba(51, 51, 51, 0.1)
    padding: 20px 25px
    z-index: 10511111111

.select2-results__options
    display: flex
    flex-direction: column
    gap: 20px
    white-space: nowrap
    @extend %sc-scroll-bar

.select2-results__option
    padding: 0
    line-height: 1

.select2-container--default
    display: block

    .select2-selection--single
        height: auto
        border: 1px solid $light
        border-radius: 0
        outline: none

    .select2-selection__rendered
        padding: 0.94rem 1.2rem !important
        font-size: 0.875rem
        line-height: 1.5 !important

    .select2-results__option--selectable
        background-color: transparent
        color: $secondary
        &.select2-results__option--highlighted
            background-color: transparent
            color: $primary

    .select2-results__option--selected
        background-color: transparent
        color: $primary

    // Arrows
    .select2-selection--single .select2-selection__arrow
        height: 48px
        width: 48px

        b
            border: none
            content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='7.41' viewBox='0 0 12 7.41'%3E%3Cpath id='Path_1038' data-name='Path 1038' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)'/%3E%3C/svg%3E%0A")
            width: 12px
            height: 8px
            +sc-transition

    &.select2-container--open .select2-selection--single .select2-selection__arrow b
        transform: rotate(180deg)

    // Search field
    .select2-search--dropdown
        padding: 0

        .select2-search__field
            border-color: $secondary
            margin-bottom: 20px
            padding: 10px
            outline: none
