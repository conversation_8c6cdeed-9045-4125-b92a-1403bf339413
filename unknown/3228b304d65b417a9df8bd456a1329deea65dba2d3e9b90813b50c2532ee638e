@extends('layouts.login')
@section('content')
    <div class="auth-card login-wrap align-items-center pb-4">
        <div class="login-above">
            <a class="btn btn-link text-decoration-none text-info d-block close-btn-mobile" href="{{ url('login') }}">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                    <path id="Union_1" data-name="Union 1"
                        d="M19482,14626.723l-8.277,8.277-1.723-1.723,8.277-8.277-8.277-8.277,1.723-1.723,8.277,8.277,8.277-8.277,1.723,1.723-8.277,8.277,8.277,8.277-1.723,1.723Z"
                        transform="translate(-19472 -14615)" fill="#f0f0f0" />
                </svg>
            </a>
            <h1 class="mb-7 f-16 semibold ls-1.8px text-uppercase lh-small">{{ __('Forgot Password') }}</h1>
            <div>
                <p class="mb-4 pb-05 fs-12px text-center text-md-start">
                    {{ __('To reset your password, please enter the email address associated with your IMS account.') }}</p>
                @if (session('status'))
                    <div class="alert alert-success" role="alert">
                        {{ session('status') }}
                    </div>
                @endif
                <form method="POST" action="{{ route('password.email') }}">
                    @csrf
                    <div class="input-placeholder">
                        <input id="email" type="email"
                            class="line-style ls-0.8px ps-2 @error('email') is-invalid @enderror" name="email"
                            value="{{ old('email') }}" required autocomplete="email" autofocus placeholder="Email">
                        <!--<span class="placeholder-shown">Email</span>-->
                        @error('email')
                            <span class="invalid-feedback mb-3" role="alert">{{ $message }}</span>
                        @enderror
                    </div>
                    <div
                        class="login-submit d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-between pt-05 gap-4">
                        <button type="submit" class="btn btn-primary ms-0 ls-1.2px fw-normal">
                            {{ __('Reset') }}
                        </button>
                        <a class="text-secondary f-12 text-decoration-none text-center" href="{{ url('login') }}">
                            {{ __('← Go Back') }}
                        </a>
                    </div>
            </div>
            </form>
        </div>
        <div class="login-bottom">
            <p>If you still need help,<a href=""> contact us.</a></p>
        </div>
    </div>
@endsection
