<div class="pt-2 pl-2 d-flex justify-content-between align-items-center">
    <div class="d-flex py-2">
        <div>
            <span>{{ $paginator->firstItem() }}</span>
            {{ __('-') }}
            <span>{{ $paginator->lastItem() }}</span>
        </div>
        <div class="ms-1 text-info">
            {{ __('of') }}
            <span>{{ $paginator->total() }}</span>
        </div>
    </div>

    @if ($paginator->hasPages())
        <div>
            <nav>
                <ul class="pagination">
                    {{-- Previous Page Link --}}
                    @if ($paginator->onFirstPage())
                        <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.previous')">
                            <span class="page-link" aria-hidden="true">
                                <div style="transform: rotate(90deg)">
                                    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='17' viewBox='0 0 12 7.41'><path id='Path_1038' data-name='Path 1038' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)'/></svg>
                                </div>
                            </span>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev"
                                aria-label="@lang('pagination.previous')">
                                <div style="transform: rotate(90deg)">
                                    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='17' viewBox='0 0 12 7.41'><path id='Path_1038' data-name='Path 1038' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)'/></svg>
                                </div>
                            </a>
                        </li>
                    @endif

                    {{-- Pagination Elements --}}
                    @foreach ($elements as $element)
                        {{-- "Three Dots" Separator --}}
                        @if (is_string($element))
                            <li class="page-item disabled" aria-disabled="true"><span
                                    class="page-link">{{ $element }}</span></li>
                        @endif

                        {{-- Array Of Links --}}
                        @if (is_array($element))
                            @foreach ($element as $page => $url)
                                @if ($page == $paginator->currentPage())
                                    <li class="page-item active" aria-current="page"><span
                                            class="page-link">{{ $page }}</span></li>
                                @else
                                    <li class="page-item"><a class="page-link"
                                            href="{{ $url }}">{{ $page }}</a></li>
                                @endif
                            @endforeach
                        @endif
                    @endforeach

                    {{-- Next Page Link --}}
                    @if ($paginator->hasMorePages())
                        <li class="page-item">
                            <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next"
                                aria-label="@lang('pagination.next')">
                                <div style="transform: rotate(-90deg)">
                                    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='17' viewBox='0 0 12 7.41'><path id='Path_1038' data-name='Path 1038' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)'/></svg>
                                </div>
                            </a>
                        </li>
                    @else
                        <li class="page-item disabled" aria-disabled="true" aria-label="@lang('pagination.next')">
                            <span class="page-link" aria-hidden="true">
                                <div style="transform: rotate(-90deg)">
                                    <svg xmlns='http://www.w3.org/2000/svg' width='12' height='17' viewBox='0 0 12 7.41'><path id='Path_1038' data-name='Path 1038' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)'/></svg>
                                </div>
                            </span>
                        </li>
                    @endif
                </ul>
            </nav>
        </div>
    @endif
    <div class="d-flex align-items-center">
            <div id="pagination-per-page" class="position-relative text-secondary">{{ __('Per Page:') }}&nbsp;</div>
            <div>
                <select class="form-select table-filter-select pagination-select" aria-label="Default select example">
                    <option selected value="10">{{ __('10') }}</option>
                    <option value="25">{{ __('25') }}</option>
                    <option value="50">{{ __('50') }}</option>
                    <option value="0">{{ __('All') }}</option>
                </select>
            </div>
        </div>
</div>
