<?php

namespace App\Services\Admin\Supplier;

use App\Models\Supplier;
use Illuminate\Pagination\LengthAwarePaginator;

interface ISupplierService
{
    public function store(array $data);
    public function update(array $data, Supplier $supplier);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator;
    public function delete(Supplier $supplier);
    public function deleteMultiple(array $ids);
}
