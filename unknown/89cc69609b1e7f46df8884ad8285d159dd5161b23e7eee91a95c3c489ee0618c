<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Bundle;
use App\Models\Products;
use App\Models\BundleItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Company>
 */
class BundleItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'bundle_id' => Bundle::query()->inRandomOrder()->first()?->id ?? Bundle::factory(),
            'product_id' => Products::query()->inRandomOrder()->first()?->id ?? Products::factory(),
            'quantity' => fake()->numberBetween(1, 10),
        ];
    }
}
