<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class PaymentSecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // 1. Log all payment-related requests for monitoring
        Log::info('Payment request', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'timestamp' => now()
        ]);

        // 2. Check for suspicious patterns
        $this->checkSuspiciousActivity($request);

        // 3. Validate payment form access
        if ($request->route()->getName() === 'payment.form') {
            $this->validatePaymentFormAccess($request);
        }

        // 4. Add security headers
        $response = $next($request);
        
        if (method_exists($response, 'header')) {
            $response->header('X-Content-Type-Options', 'nosniff');
            $response->header('X-Frame-Options', 'DENY');
            $response->header('X-XSS-Protection', '1; mode=block');
            $response->header('Referrer-Policy', 'strict-origin-when-cross-origin');
        }

        return $response;
    }

    /**
     * Check for suspicious activity patterns
     */
    private function checkSuspiciousActivity(Request $request)
    {
        $ip = $request->ip();
        $cacheKey = "payment_attempts_{$ip}";
        
        // Track payment attempts per IP
        $attempts = Cache::get($cacheKey, 0);
        
        if ($attempts > 50) { // More than 50 attempts in an hour
            Log::warning('Suspicious payment activity detected', [
                'ip' => $ip,
                'attempts' => $attempts,
                'user_agent' => $request->userAgent()
            ]);
            
            // Could implement additional blocking logic here
        }
        
        Cache::put($cacheKey, $attempts + 1, 3600); // 1 hour
    }

    /**
     * Validate payment form access
     */
    private function validatePaymentFormAccess(Request $request)
    {
        $type = $request->route('type');
        $id = $request->route('id');
        
        // Validate type parameter
        if (!in_array($type, ['purchase', 'license', 'lease'])) {
            Log::warning('Invalid payment type accessed', [
                'type' => $type,
                'id' => $id,
                'ip' => $request->ip()
            ]);
            abort(404);
        }
        
        // Validate ID format (should be IMS + 128 hex chars)
        if (!preg_match('/^IMS[A-F0-9]{128}$/', $id)) {
            Log::warning('Invalid payment ID format', [
                'type' => $type,
                'id' => $id,
                'ip' => $request->ip()
            ]);
            abort(404);
        }
        
        // Check for repeated access to same payment link
        $accessKey = "payment_access_{$id}_{$request->ip()}";
        $accessCount = Cache::get($accessKey, 0);
        
        if ($accessCount > 10) { // More than 10 accesses to same link
            Log::warning('Excessive payment link access', [
                'type' => $type,
                'id' => $id,
                'ip' => $request->ip(),
                'access_count' => $accessCount
            ]);
        }
        
        Cache::put($accessKey, $accessCount + 1, 3600); // 1 hour
    }
}
