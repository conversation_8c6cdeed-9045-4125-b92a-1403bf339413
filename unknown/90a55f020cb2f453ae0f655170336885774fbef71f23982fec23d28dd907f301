@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="row">
            <form method="GET" action="{{route('create-agreement')}}" enctype="multipart/form-data">
                @csrf
                <div class="d-flex justify-content-between align-items-center py-5 py-md-9 border-bottom border-light">
                    <h3 class="page-title mb-0 text-uppercase">{{ __('Welcome') . ' ' . Auth::user()->first_name . '!' }}</h3>
                    <a href="{{ route('get-all-agreements') }}"
                       class="btn btn-primary float-right">{{ __('Show all agreements') }}</a>
                </div>

            <div class="col">
                <div class="form-group @if($errors->has('customers')) has-danger @endif">
                    <label for="customers" class="asterisk">{{ __('Customers') }}</label>
                    <select class="form-select form-select-lg mb-3" aria-label=".form-select-lg" name="customer" required>
                        @foreach($customers as $key => $value)
                            <option
                                    value="{{ $key}}" {{ (collect(old('addresses'))->contains($key)) ? 'selected':'' }}>
                                {{$value}}
                            </option>
                        @endforeach
                    </select>
                    @if($errors->has('customers'))
                        <span class="text-danger input-error">*{{ $errors->first('customers') }}</span>
                    @endif
                </div>
            </div>
            <div class="col">
                <div class="form-group @if($errors->has('templates')) has-danger @endif">
                    <label for="templates" class="asterisk">{{ __('Templates') }}</label>
                    <select class="form-select form-select-lg mb-3" aria-label=".form-select-lg" name="template" required>
                        @foreach($templates as $value)
                            <option
                                    value="{{ $value['id'] }}" {{ (collect(old('templates'))->contains($value['id'])) ? 'selected':'' }}>
                                {{$value['name']}}
                            </option>
                        @endforeach
                    </select>
                    @if($errors->has('templates'))
                        <span class="text-danger input-error">*{{ $errors->first('templates') }}</span>
                    @endif
                </div>
            </div>
            <button type="submit" class="btn btn-primary mt-4">{{ __('Create agreement') }}</button>
            </form>

            <div class="col-3 mt-5">
                @if (Session::has('message'))
                    <div class="alert bg-success-subtle p-2 text-center">{{ Session::get('message') }}</div>
                @endif

                @if (Session::has('error'))
                    <div class="alert bg-danger  p-2 text-center" role="alert">{{ Session::get('error') }}</div>
                @endif
            </div>
        </div>
    </div>
@endsection
