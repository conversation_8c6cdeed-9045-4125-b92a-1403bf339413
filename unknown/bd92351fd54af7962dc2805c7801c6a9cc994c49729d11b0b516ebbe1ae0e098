<div class="form-check d-flex {{ $wrap_class_name ?? ''}} align-items-center @if($errors->has($field_name)) has-danger @endif">
    <input
        id="{{$field_name ?? ''}}"
        class="form-check-input"
        @if ($field_name == 'use_billing_address')
        onclick="$(this).is(':checked') ? $('#use_billing_address_field').val('1') : $('#use_billing_address_field').val('0')"
        @endif
        @if ($field_name != 'toggle' AND $field_name != 'use_billing_address')
            name="{{$field_name}}"            
        @endif
        placeholder="{{$field_label}}"
        value="{{$field_value ?? '' }}"
        type="checkbox"
        @checked(isset($checked) && $checked === 'checked')
        @required(isset($required) && $required === 'required')
        @readonly(isset($readonly) && $readonly === 'readonly')
        @disabled(isset($disabled) && $disabled === 'disabled')
    >

    <label class="form-check-label ms-3 fs-14px ls-0.7px" for="{{ $field_name }}">{!! $field_label !!}</label>
    @if ($field_name == 'use_billing_address')
        <input type="hidden" id="use_billing_address_field" name="use_billing_address" value="{{$field_value ?? '0' }}">        
    @endif

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</div>
@if (($field_name == 'toggle' OR $field_name == 'use_billing_address') AND isset($field_to_toggle))
<script type="module">
    $(document).ready(function () {
        $('#{{ $field_name }}').on('change', function () {
            if (@if($field_name == 'use_billing_address') !this.checked @else this.checked @endif) {
                $('.{{ $field_to_toggle }}').removeClass('d-none');
            } else {
                $('.{{ $field_to_toggle }}').addClass('d-none');
            }
        });
    });
</script>
@endif
