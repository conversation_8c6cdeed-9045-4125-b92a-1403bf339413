{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"autoprefixer": "^10.4.2", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.8", "postcss": "^8.4.6", "vite": "^4.5.9"}, "dependencies": {"@flasher/flasher": "^1.3.2", "@fortawesome/fontawesome-free": "^6.4.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.0", "bootstrap-datepicker": "^1.10.0", "datatables.net-bs5": "^1.13.4", "datatables.net-rowreorder-bs5": "^1.3.3", "flatpickr": "^4.6.13", "inputmask": "^5.0.9", "jquery": "^3.7.0", "sass": "^1.62.1", "sass-loader": "^13.3.1", "select2": "^4.1.0-rc.0"}, "name": "erp", "description": "This README would normally document whatever steps are necessary to get your application up and running.", "version": "1.0.0", "main": "postcss.config.js", "directories": {"test": "tests"}, "repository": {"type": "git", "url": "git+ssh://*****************/superclusterdev/erp.git"}, "keywords": [], "author": "", "license": "ISC", "homepage": "https://bitbucket.org/superclusterdev/erp#readme"}