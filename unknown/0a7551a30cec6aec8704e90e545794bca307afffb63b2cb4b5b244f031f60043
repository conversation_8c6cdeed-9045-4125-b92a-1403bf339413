<?php

namespace App\Http\Requests\Admin\settings;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class UpdateAdminSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'logo'               => 'image|mimes:jpg,jpeg,png|max:1024',
            'photo'              => 'image|mimes:jpg,jpeg,png|max:1024',
            'timezone'           => 'required|string|max:190',
            'date_format'        => 'required|string|max:190',
            'time_format'        => 'required|string|max:190',
            'week_start'         => 'required|string|max:190',
            'currency'           => 'required|string|max:190',
            'seo_title'          => 'nullable|string|max:190',
            'seo_description'    => 'nullable|string|max:200',
            'smtp_from_username' => 'required_with:smtp_*|string|max:190',
            'smtp_from_email'    => 'required_with:smtp_*|string|email|max:190',
            'email'              => 'string|email|max:190',
            'password'           => ['nullable', Password::min(8)->letters()->mixedCase()->numbers()],
        ];
    }
}
