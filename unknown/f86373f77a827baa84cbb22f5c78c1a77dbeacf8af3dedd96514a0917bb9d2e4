<?php

namespace App\Services\Admin\InvoiceProduct;

use App\Models\InvoiceProduct;
use App\Models\Customers;
use Illuminate\Pagination\LengthAwarePaginator;

interface IInvoiceProductService
{
    public function store(array $data): void;
    public function update(array $data, InvoiceProduct $invoiceProduct): void;
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $customerId): LengthAwarePaginator;
    public function delete(InvoiceProduct $invoiceProduct): void;

}
