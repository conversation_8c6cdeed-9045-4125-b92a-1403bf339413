<div class="modal fade" id="addUploadAgreement" tabindex="-1" aria-labelledby="exampleModalLabel">
    <style>
        .btn-upload-multiple {
            position: absolute;
            display: flex;
            padding: 5px 12px;
            color: #fff;
            font-size: 12px;
            line-height: 13.2px;
            font-weight: 500;
            top: 9px;
            right: 10px;
            border-radius: 12px;
            background: #969696;
            border: none;
            outline: none;
            letter-spacing: 0.05em !important;
        }
        .btn-close:focus {
            outline: 0;
            box-shadow: none;
            opacity: 0.5;
        }
    </style>
    <div class="modal-dialog modal-dialog-centered w-500">
        <div class="w-100">
            <div class="modal-content">
                <div class="modal-header py-4 border-bottom">
                    <h1 class="modal-title text-uppercase fs-14px lh-33 fw-medium">UPLOAD AGREEMENT(S)</h1>
                    <button type="button" class="btn-close ms-0 position-absolute " style="right: 20px;"
                        data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="p-2 p-sm-5 py-10 py-sm-5">
                    <form id="uploadAgreementForm" method="POST" enctype="multipart/form-data">
                        @csrf
                        <div class="modal-body p-0">
                            <div>
                                <input id="file_name" name="file_name" class="form-control" placeholder="File name (optional)" type="text" autocomplete="off">
                            </div>
                            @include('partials.forms.select', [
                                'field_name' => 'location',
                                'field_label' => '',
                                'field_class' => 'mb-0 mt-3',
                                'values' => $locations,
                                'field' => 'location',
                                'placeholder' => 'Locations',
                                'option_key' => 'id',
                                'option_label' => 'name',
                                'field_value' => old('location', NULL),
                                'field_label_class' => 'd-none',
                                'include_empty' => true,
                                'field_id' => 'location_id'
                            ])
                            <span class="location-error text-danger"></span>
                            @include('partials.forms.select', [
                                'field_name' => 'type',
                                'field_label' => '',
                                'field_class' => 'mb-0',
                                'values' => $types,
                                'field' => 'types',
                                'placeholder' => 'Type',
                                'option_key' => 'id',
                                'option_label' => 'name',
                                'field_value' => old('type', null),
                                'include_empty' => true,
                            ])
                            <span class="type-error text-danger"></span>
                            <div class="mb-5 mt-3" style="position: relative;">
                                <input id="file" name="file" class="form-control" placeholder="Select a file(s)"
                                    type="text" autocomplete="off">
                                <button class="btn-upload-multiple agree-files-btn" id="btn-upload-multiple" type="button">
                                    Browse</button>
                                <input type="file" id="fileInput" name="files[]" style="display: none;" multiple>
                                <span class="files-error text-danger"></span>
                            </div>
                        </div>
                        <div class="modal-footer justify-content-between mt-0 p-0 fdc">
                            <p class="m-0"></p>
                            <button type="button" id="submitForm"
                                class="btn btn-primary w-100 fw-normal m-0">{{ __('Submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('btn-upload-multiple').addEventListener('click', function() {
        document.getElementById('fileInput').click();
    });
    document.getElementById('fileInput').addEventListener('change', function() {
        var fileInput = document.getElementById('fileInput');
        var fileNames = [];
        for (var i = 0; i < fileInput.files.length; i++) {
            fileNames.push(fileInput.files[i].name);
        }
        document.getElementById('file').value = fileNames.join(', ');
    });

    document.getElementById('submitForm').addEventListener('click', function(event) {
        event.preventDefault();
        var location = document.querySelector('[name="location"]').value;
        var type = document.querySelector('[name="type"]').value;
        var fileInput = document.getElementById('fileInput');

        var formData = new FormData(document.getElementById('uploadAgreementForm'));
        if (!location || !type || fileInput.files.length === 0) {
            if (!location) {
                $('.location-error').html('* The location field is required.');
            } else {
                $('.location-error').html('');
            }
            if (!type) {
                $('.type-error').html('* The type field is required.');
            } else {
                $('.type-error').html('');
            }
            if (fileInput.files.length === 0) {
                $('.files-error').html('* The files are required.');
            } else {
                $('.files-error').html('');
            }
            return;
        }

        $.ajax({
            url: "{{ route('admin.customers.store.agreements', ['customer' => $customer]) }}", // Form action
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr, status, error) {
                $('.text-danger').html('');
                var response = JSON.parse(xhr.responseText);
                if (response.errors) {
                    Object.keys(response.errors).forEach(function(field) {
                        var errorMessages = response.errors[field];
                        var errorElement = document.querySelector(`.${field}-error`);
                        if (errorElement) {
                            errorElement.innerHTML = '* ' + errorMessages.join(', ');
                        }
                    });
                }
            }
        });
    });
</script>
