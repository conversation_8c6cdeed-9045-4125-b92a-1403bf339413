<?php

namespace App\Http\Requests\Admin\CustomerAgreement;

use Illuminate\Foundation\Http\FormRequest;

class AddCustomerAgreementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            // 'files.*' => 'required|file|mimes:pdf,jpg,png,docx|max:2048',
            'file_name' => 'nullable|string',
            'files.*' => 'required|file|max:2048',
            'location' => 'required|string',
            'type' => 'required|string',
        ];
    }
}
