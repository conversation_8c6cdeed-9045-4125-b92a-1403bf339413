@extends('layouts.app')

@section('content')
<div class="page-title">
    <div class="title-left">
        <h3>{{ __('New Template') }}</h3>
        <a href="{{ route('admin.invoice-templates.index') }}" class="back-link colorGrey">← Back</a>
    </div>
</div>

<div class="mb-6 mt-45">
    <form method="POST" action="{{ route('admin.invoice-templates.store') }}" id="studio-location-form" class="invoice-fields">
        @csrf
            <div class="order-info three-fields">
                <div class="input-wrap" style="margin-right: 5px;">
                    @include('partials.forms.input', [
                        'field_name' => 'title',
                        'field_label' => 'TEMPLATE TITLE *',
                        'placeholder' => 'Enter Title',
                        'field_type' => 'text',
                        'field_value' => '',
                        'field_class' => 'w-100',
                    ])
                </div>
            </div>

            <h5 class="form-section-title mt-20">{{ __('invoice items') }}</h5>
        <div class="border-div table-invoice-tpl">
            <div class="invoice-items">
                <div class="">
                    <div class="items-header list-header d-grid border-bottom border-light text-info text-uppercase fw-normal pb-3 fw-medium">
                        <div class="inv-id">{{ __('Id') }}</div>
                        <div class="inv-product">{{ __('Product') }}</div>
                        <div class="inv-unitprice">{{ __('Unit Price') }}</div>
                        <div class="inv-qty">{{ __('Qty') }}</div>
                        <div class="inv-discount_type">{{ __('DIS. TYPE') }}</div>
                        <div class="inv-discount">{{ __('Discount') }}</div>
                        <div class="inv-deposit">{{ __('Deposit') }}</div>
                        <div class="inv-total">{{ __('Total') }}</div>
                    </div>
                </div>
                @foreach($new_products as $key => $product)
                    @foreach($product as $productt)
                    <input type="hidden" value="{{$productt->price}}" name="product-price[{{$productt->id}}]">
                    @endforeach
                @endforeach
                @php
                    $oldItems = old('items', []);
                @endphp

                @foreach($oldItems as $index => $item)

                <div class="invoice-items-table items-group list-group-item list-group-item-action input-h40 d-grid align-items-center border-bottom border-light">
                    <div class="item-number">{{ $index + 1 }}</div>

                    @if (isset($item->product) AND $item->product['custom_product'] == 1)
                        @include('partials.forms.input', [
                            'field_name' => "items[{$index}][product_name]",
                            'field_label' => null,
                            'field_type' => 'text',
                            'field_value' => old("items.$index.product_name"),
                            'field_class' => ''
                        ])
                        <input type="hidden" name="items[{{$index}}][product_id]" value="{{ old("items.$index.product_id") }}">
                        <input type="hidden" name="items[{{$index}}][custom_product]" value="1">
                    @else                        
                        @include('partials.forms.select', [
                            'field_name' => "items[{$index}][product_id]",
                            'field_label' => '',
                            'field_class' => '',
                            'select_class' => 'dynamic-select',
                            'values' => $new_products,
                            'group_values' => $new_products,
                            'field' => 'product',
                            'option_key' => 'id',
                            'option_label' => 'name',
                            'field_value' => old("items.$index.product_id"),
                            'include_empty' => true,
                            'index' => $index,
                        ])
                    @endif

                    <div></div>

                    <div class="remove-div">
                        <a href="#" class="remove-item text-danger ms-3" data-id="{{$index}}">Remove</a>
                    </div>

                    @include('partials.forms.input', [
                        'field_name' => "items[{$index}][price]",
                        'field_label' => null,
                        'field_type' => 'text',
                        'placeholder' => '$0.00',
                        'field_value' => old('price'),
                        'field_class' => 'currency-input unitprice-field'
                    ])

                    @include('partials.forms.input', [
                        'field_name' => "items[{$index}][quantity]",
                        'field_label' => null,
                        'field_type' => 'number',
                        'placeholder' => 'Qty',
                        'field_value' => old('quantity'),
                        'field_class' => 'unitqty-field'
                    ])

                    @include('partials.forms.select', [
                        'field_name' => "items[{$index}][discount_type]",
                        'field_label' => '',
                        'field_class' => 'discount_type-field',
                        'values' => ['$' => '$', '%' => '%'],
                        'field' => 'discount_type',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => old('discount_type'),
                        // 'include_empty' => true,
                        'index' => $index,
                    ])

                    @include('partials.forms.input', [
                        'field_name' => "items[{$index}][discount]",
                        'field_label' => null,
                        'field_type' => 'text',
                        'placeholder' => '$0.00',
                        'field_value' => old('discount'),
                        'field_class' => 'unitdiscount-field'
                    ])

                    @include('partials.forms.input', [
                        'field_name' => "items[{$index}][deposit]",
                        'field_label' => null,
                        'field_type' => 'text',
                        'placeholder' => '$0.00',
                        'field_value' => old('deposit'),
                        'field_class' => 'currency-input unitdeposit-field'
                    ])

                    <div class="total custom-fw-500">
                        <span>$0.00</span>
                    </div>
                    
                </div>
                @endforeach
            </div>

            <div class="invoice-items-table add-item">
                <div class="new-items-box">
                    <a href="javascript:;" onclick="add_item()">
                        <span>
                            {{ __('+ new item') }}
                        </span>
                    </a>
                    <a href="javascript:;" class="text-secondary" onclick="add_custom_item()">
                        {{ __('New custom item') }}
                    </a>
                </div>
                <div class="remove-div">
                    <a href="#" class="remove-item text-danger ms-3" style="opacity: 0;pointer-events: none;visibility: hidden" data-id="7">Remove</a>
                </div>
                <div class="form-group position-relative lh-1 currency-input unitprice-field text-start" style="opacity: 0;pointer-events: none;visibility: hidden">
                    <div class="input-placeholder max500 ">
                        <input class="form-control" placeholder="$0.00" value="21.00" style="text-align: right;" type="text">
                    </div>
                </div>
                <div class="form-group  position-relative   lh-1  unitqty-field text-start">
                    <div class="input-placeholder max500 input-h40">
                        <input id="bulk_quantity" class="form-control" placeholder="Qty" type="number" autocomplete="off">
                    </div>
                </div>
                <div style="opacity: 0;pointer-events: none;visibility: hidden"></div>

                <div class="form-group discount_type-field" style="opacity: 0;pointer-events: none;visibility: hidden">
                    <select class="form-control dynamic-select">
                        <option value="$" selected>$</option>
                        <option value="%">%</option>
                    </select>
                </div>

                <div class="form-group unitdiscount-field" style="opacity: 0;pointer-events: none;visibility: hidden">
                    <input type="text" class="form-control" value="0" placeholder="$0.00">
                </div>

                <div class="form-group unitdeposit-field" style="opacity: 0;pointer-events: none;visibility: hidden"></div>

                <div class="total custom-fw-500" style="opacity: 0;pointer-events: none;visibility: hidden">
                    <span>$0.00</span>
                </div>
            </div>
        </div>

        <div class="container pb-1 px-0">
            <div class="row mt-50">
                <!-- Left Side: Invoice Note -->
                <div class="col-md-6">
                    <div class="form-group mb-0">
                        <textarea class="form-control" name="note" rows="5" placeholder="Invoice note (optional)">{{ old('note') }}</textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between">
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('SAVE') }}</button>
                <a type="button" href="{{route('admin.invoice-products.create')}}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </div>
    </form>
</div>

@endsection

<script type="module">
    document.addEventListener('DOMContentLoaded', function() {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        };

        $('.invoice-items').on('input', 'input', function() {
            let row = $(this).closest('.items-group'); // Get the closest row
            calculateRowTotal(row); // Recalculate the total for the current row
            calculateInvoiceTotals(); // Recalculate overall totals
        });

        $('.invoice-items').on('change', 'select', function() {
            let row = $(this).closest('.items-group'); // Get the closest row
            calculateRowTotal(row); // Recalculate the total for the current row
            calculateInvoiceTotals(); // Recalculate overall totals
        });

        $('#bulk_quantity').on('keyup', function() {
            var val = $(this).val();
            $("[name*='[quantity]']").each(function(i, el) {
                var old = $(this).data('old');
                $(el).val(val.length == 0 ? old : val);
            });
            $('.items-group').each(function(i, elem) {
                calculateRowTotal($(elem)); // Recalculate the total for the current row
                calculateInvoiceTotals(); // Recalculate overall totals                
            });
        });

        $('input[name="shipping_fee"], input[name="handling_fee"]').on('input', function() {
            if ($('input[name="shipping_fee"]').val().length > 0 || $('input[name="handling_fee"]').val().length > 0) {
                $('.shipping').show();
            } else {
                $('.shipping').hide();
            }
            calculateInvoiceTotals();
        });

        // Initial calculation for prefilled data (if any)
        $('.items-group').each(function() {
            calculateRowTotal($(this));
        });
        calculateInvoiceTotals();

        let itemCount = $('.invoice-items-table').length;
        // let itemCount = 1;

        // $('.add-item a span').on('click', function(e) {
        function add_item(id = '', qty = 1, price = '') {
            // e.preventDefault();
            // console.log(itemCount);

            itemCount = $('.invoice-items-table').length + 1;

            const newItemRow = `
                 <div class="invoice-items-table items-group list-group-item list-group-item-action input-h40 d-grid align-items-center border-bottom border-light" id="item-${itemCount}">
                    <div class="item-number">${itemCount}</div>

                    <div class="form-group">
                        <select name="items[${itemCount-1}][product_id]" class="form-control dynamic-select">
                            <option></option>
                            @foreach($new_products as $types => $products)
                                @if($types == 'bundle')
                                    <optgroup label="{{ $types }}">
                                    @foreach($products as $key => $product)
                                        <option value="{{ $product->id ?? $product }}" data-type="bundle" data-name="{{ $product->title }}" data-price="{{ $product->price }}" ${id == {{ $product->id }} ? 'selected' : ''}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                    </optgroup>
                                @endif
                            @endforeach
                            @foreach($new_products as $types => $products)
                                @if($types == 'fee')
                                    <optgroup label="{{ $types }}">
                                    @foreach($products as $key => $product)
                                        <option value="{{ $product->id ?? $product }}" data-type="fee" data-name="{{ $product->name }}" data-price="{{ $product->price }}" ${id == {{ $product->id }} ? 'selected' : ''}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                    </optgroup>
                                @endif
                            @endforeach
                            @foreach($new_products as $types => $products)
                                @if($types == 'products')
                                    <optgroup label="{{ $types }}">
                                    @foreach($products as $key => $product)
                                        <option value="{{ $product->id ?? $product }}" data-type="products" data-name="{{ $product->name }}" data-price="{{ $product->price }}" ${id == {{ $product->id }} ? 'selected' : ''}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                    </optgroup>
                                @endif
                            @endforeach
                        </select>
                    </div>

                    <div class="remove-div">
                        <a href="#" class="remove-item text-danger ms-3" data-id="${itemCount}">Remove</a>
                        </div>

                    <div class="form-group currency-input unitprice-field">
                        <input type="text" name="items[${itemCount-1}][price]" class="form-control" placeholder="$0.00" value="${price}">
                    </div>

                    <div class="form-group unitqty-field">
                        <input type="text" name="items[${itemCount-1}][quantity]" class="form-control" placeholder="Qty" data-old="${qty}" value="${qty}">
                    </div>

                    <div class="form-group discount_type-field">
                        <select name="items[${itemCount-1}][discount_type]" class="form-control dynamic-select">
                            <option value="$" selected>$</option>
                            <option value="%">%</option>
                        </select>
                    </div>
                    
                    <div class="form-group unitdiscount-field">
                        <input type="text" name="items[${itemCount-1}][discount]" class="form-control" value="0" placeholder="$0.00">
                    </div>

                    <div class="form-group currency-input unitdeposit-field">
                        <input type="text" name="items[${itemCount-1}][deposit]" class="form-control" value="0" placeholder="$0.00">
                    </div>

                    <div class="total custom-fw-500">
                        <span>$0.00</span>
                    </div>
                        
                </div>`;

            // Append the new item row before the "Add another item" link
            $('.invoice-items').append(newItemRow);
            // calculateInvoiceTotals();
            const inputElements = document.querySelectorAll('.currency-input input');
            inputElements.forEach(inputElement => {
                if (inputElement) {
                    Inputmask({
                        alias: "numeric",
                        groupSeparator: ",",
                        autoGroup: true,
                        digits: 2,
                        digitsOptional: false,
                        prefix: "$",
                        placeholder: "0",
                        showMaskOnHover: false
                    }).mask(inputElement);
                }
            });
            const selectElementSelector = `[name="items[${itemCount-1}][product_id]"]`;

            setTimeout(function() {
                $('.invoice-items-table').each((i, el) => {
                    calculateRowTotal($(el));
                    calculateInvoiceTotals();
                });
            }, 50);

            $(selectElementSelector).trigger('change');

            updateItemNumbers();
        };

        function add_custom_item() {
            // e.preventDefault();
            // console.log(itemCount);

            itemCount = $('.invoice-items-table').length + 1;

            const newItemRow = `
                 <div class="invoice-items-table items-group list-group-item list-group-item-action input-h40 d-grid align-items-center border-bottom border-light" id="item-${itemCount}">
                    <div class="item-number">${itemCount}</div>

                    <div class="form-group">
                        <input type="text" name="items[${itemCount-1}][product_name]" class="form-control" placeholder="Name" value="">
                        <input type="hidden" name="items[${itemCount-1}][custom_product]" value="1">
                    </div>

                    <div class="remove-div">
                        <a href="#" class="remove-item text-danger ms-3" data-id="${itemCount}">Remove</a>
                    </div>

                    <div class="form-group currency-input unitprice-field">
                        <input type="text" name="items[${itemCount-1}][price]" class="form-control" placeholder="$0.00" value="">
                    </div>

                    <div class="form-group unitqty-field">
                        <input type="text" name="items[${itemCount-1}][quantity]" class="form-control" placeholder="Qty" data-old="1" value="1">
                    </div>

                    <div class="form-group discount_type-field">
                        <select name="items[${itemCount-1}][discount_type]" class="form-control dynamic-select">
                            <option value="$" selected>$</option>
                            <option value="%">%</option>
                        </select>
                    </div>
                    
                    <div class="form-group unitdiscount-field">
                        <input type="text" name="items[${itemCount-1}][discount]" class="form-control" value="0" placeholder="$0.00">                         
                    </div>

                    <div class="form-group currency-input unitdeposit-field">
                        <input type="text" name="items[${itemCount-1}][deposit]" class="form-control" value="0" placeholder="$0.00">
                    </div>

                    <div class="total custom-fw-500">
                        <span>$0.00</span>
                    </div>
                        
                </div>`;

            // Append the new item row before the "Add another item" link
            $('.invoice-items').append(newItemRow);
            // calculateInvoiceTotals();
            const inputElements = document.querySelectorAll('.currency-input input');
            inputElements.forEach(inputElement => {
                if (inputElement) {
                    Inputmask({
                        alias: "numeric",
                        groupSeparator: ",",
                        autoGroup: true,
                        digits: 2,
                        digitsOptional: false,
                        prefix: "$",
                        placeholder: "0",
                        showMaskOnHover: false
                    }).mask(inputElement);
                }
            });
            setTimeout(function() {
                $('.invoice-items-table').each((i, el) => {
                    calculateRowTotal($(el));
                    calculateInvoiceTotals();
                });
            }, 50);

            updateItemNumbers();
        };

        var observer = new MutationObserver(function(mutationsList) {
            mutationsList.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    $(mutation.addedNodes).each(function() {
                        var row = $(this);
                        if ($(this).find('.dynamic-select').length) {
                            $(this).find('.dynamic-select').select2({
                                minimumResultsForSearch: 10,
                                placeholder: "Select",
                                closeOnSelect: true, // Ensure the dropdown closes after selection
                                // dropdownParent: $('.dynamic-select').closest('form'),  
                                templateResult: function(data) {
                                    // Retrieve custom attributes
                                    const name = $(data.element).data('name');
                                    const price = $(data.element).data('price');

                                    if (!data.id || name == undefined || price == undefined) {
                                        return data.text; // Return default option text if no ID
                                    }

                                    // Return a custom template
                                    return $(`
                                        <div class="max-title-width" style="display: flex; justify-content: space-between; align-items: center;">
                                            <span>${name}</span>
                                            <span style="color: #969696 !important;">${price}</span>
                                        </div>
                                    `);
                                },
                                templateSelection: function(data) {
                                    // Customize how the selected item appears
                                    const name = $(data.element).data('name');
                                    const price = $(data.element).data('price');

                                    if (!data.id || name == undefined || price == undefined) {
                                        return data.text;
                                    }

                                    return `${name} - ${price}`;
                                }
                            });
                            $(this).find('.dynamic-select').on('select2:select', function(e) {
                                const selectedData = e.params.data;
                                const originalOption = row.find('.dynamic-select').find(`option[value="${selectedData.id}"][data-type="${selectedData.element.dataset.type}"]`);
                                // const productId = selectedData.id;
                                const productId = selectedData.id;
                                console.log("productId: " + productId);
                                console.log(originalOption);

                                // Get the selected value
                                const value = `input[name="product-price[${productId}]"]`;

                                // Find the corresponding input field by its name
                                const inputField = row.find(`input[name*="[price]"]`);
                                const qtyField = row.find(`input[name*="[quantity]"]`);

                                console.log('TYPE: ', originalOption.data('type'));

                                if (originalOption.data('type') === 'bundle') {
                                    row.remove();
                                    generateBundle(productId);
                                }
                                if (inputField) {
                                    inputField.val($(value).val());
                                    qtyField.val(1);
                                    calculateRowTotal(row);
                                    calculateInvoiceTotals();
                                }
                            });
                            $('.discount_type-field').find('select').select2({
                                minimumResultsForSearch: 10,
                                placeholder: "Select",
                                closeOnSelect: true, // Ensure the dropdown closes after selection
                            });
                            $(document).on('click', '.select2-results__option', function(e) {
                                e.stopPropagation();
                            });
                            $(document).on('click', '.select2-results__option', function() {
                                $('.select2-selection').focus(); // Ensure the Select2 field retains focus
                            });
                        }
                    });
                }
            });
        });

        // Start observing the .invoice-items container for added nodes
        observer.observe(document.querySelector('.invoice-items'), {
            childList: true, // Observe addition of child elements
            subtree: true // Observe all descendants, not just immediate children
        });

        $(document).on('click', '.remove-item', function(e) {
            e.preventDefault();
            $(this).closest('.items-group').remove(); // Remove the specific item row
            updateItemNumbers(); // Recalculate item numbers after removal
            calculateInvoiceTotals();
        });

        $(document).on('change', '#show-tax', function(e) {
            $(this).is(":checked") ? $('tr.tax').show() : $('tr.tax').hide();
            calculateInvoiceTotals();
        });

        // Function to update item row numbers and IDs sequentially
        function updateItemNumbers() {
            itemCount = 0; // Reset the item count
            $('.items-group').each(function(index) {
                itemCount++; // Increment the new item count
                $(this).attr('id', `item-${itemCount}`); // Update the row ID
                $(this).find('.item-number').text(itemCount); // Update the displayed item number
                $(this).find('input[name^="items"], select[name^="items"]').each(function() {
                    const inputName = $(this).attr('name');
                    const newName = inputName.replace(/\d+/, itemCount - 1); // Update the input name index
                    $(this).attr('name', newName); // Set the updated name attribute
                });
                $(this).find('.remove-item').attr('data-id', itemCount); // Update the remove button's data-id
            });
        }

        function calculateRowTotal(row) {
            // Get the values
            let price = parseCurrency(row.find('input[name$="[price]"]').val()); // Get price, default to 0
            let quantity = parseInt(row.find('input[name$="[quantity]"]').val()) || 0; // Get quantity, default to 0
            let discount_type = row.find('select[name$="[discount_type]"]').val(); // Get discount type, default to 0
            let discount = parseCurrency(row.find('input[name$="[discount]"]').val()); // Get discount, default to 0

            // Calculate discount           
            let discount_val = 0;
            if (discount_type == "$") {
                discount_val = discount;
            } else {
                if (discount != 0) {
                    discount_val = (price * quantity) * (discount / 100);
                } else {
                    discount_val = 0;
                }

                // discount_val = (price * quantity) * (discount / 100);
            }

            // console.log("discount_type: ", discount_type, discount);

            // Calculate total
            let total = (price * quantity) - discount_val;
            total = total < 0 ? 0 : total; // Ensure total is not negative

            // Update the total span
            row.find('.total span').text(`$${formatCurrency(total)}`);
        };

        function calculateInvoiceTotals() {
            let subTotal = 0;
            let show_tax = $('#show-tax').is(':checked') ? 1 : 0;
            let totalDiscount = 0;
            let shippingFee = parseCurrency($('input[name="shipping_fee"]').val());
            let handlingFee = parseCurrency($('input[name="handling_fee"]').val());

            // Calculate Sub-Total, Total Discount, and Tax
            $('.items-group').each(function() {
                let row = $(this);
                let price = parseCurrency(row.find('input[name$="[price]"]').val());
                let quantity = parseInt(row.find('input[name$="[quantity]"]').val()) || 0;
                let discount_type = row.find('select[name$="[discount_type]"]').val(); // Get discount type, default to 0
                let discount = parseCurrency(row.find('input[name$="[discount]"]').val()); // Get discount, default to 0

                let discount_val = 0;
                if (discount_type == "$") {
                    discount_val = discount;
                } else {
                    discount_val = (price * quantity) * (discount / 100);
                }

                subTotal += (price * quantity);
                totalDiscount += discount_val;
            });

            // Calculate Tax (9.5% of Sub-Total minus Discount)
            let tax = show_tax ? ((subTotal - totalDiscount) * 0.095) : 0;
            tax = tax < 0 ? 0 : tax; // Ensure tax is not negative

            // Update Sub-Total, Discount, Tax, and Grand Total in the UI
            $('.container .table .sub-total span').text(`$${formatCurrency(subTotal)}`);
            $('.container .table .discount span').text(`-$${formatCurrency(totalDiscount)}`);
            $('.container .table .tax span').text(`$${formatCurrency(tax)}`);

            if (shippingFee > 0 || handlingFee > 0) {
                $('.container .table .shipping span').text(`$${formatCurrency(shippingFee+handlingFee)}`).show();
            } else {
                $('.container .table .shipping span').hide();
            }

            // if (handlingFee > 0) {
            //     $('.container .table .handling span').text(`$${formatCurrency(handlingFee)}`);
            // } else {
            //     $('.container .table .handling span').text('N/A');
            // }

            let grandTotal = subTotal - totalDiscount + tax + shippingFee + handlingFee;
            $('.container .table .grand-total span').text(`$${formatCurrency(grandTotal)}`);
        };


        function parseCurrency(value) {
            if (!value) return 0; // Default to 0 if the value is empty or undefined
            return parseFloat(value.replace(/[^0-9.-]+/g, '')); // Remove non-numeric characters
        }

        function formatCurrency(amount) {
            return amount.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        function generateBundle(bundle_id) {

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                }
            });
            $.ajax({
                url: "{{ route('admin.invoice-products.get-bundle') }}",
                type: 'POST',
                data: {
                    bundle_id: bundle_id
                },
                success: function(data) {
                    console.log(data);
                    console.log('Success');
                    if (data.success) {
                        if (data.bundle) {
                            const bundle = data.bundle;
                            bundle.items.forEach((item, index) => {
                                itemCount++;
                                add_item(item.product_id, item.quantity, item.price);
                            });
                        }
                    }
                },
                error: function(request, status, error) {
                    console.log('PHP Error');
                }
            });
            $('.main-content').unbind("scroll");
        }

        window.generateBundle = generateBundle;
        window.calculateInvoiceTotals = calculateInvoiceTotals;
        window.calculateRowTotal = calculateRowTotal;
        window.formatCurrency = formatCurrency;
        window.parseCurrency = parseCurrency;
        window.updateItemNumbers = updateItemNumbers;
        window.add_item = add_item;
        window.add_custom_item = add_custom_item;
    });
</script>