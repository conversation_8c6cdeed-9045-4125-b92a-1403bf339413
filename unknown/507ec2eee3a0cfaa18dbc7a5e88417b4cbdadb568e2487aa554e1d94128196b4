<?php

namespace Database\Factories;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lease>
 */
class LeaseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => $this->faker->randomElement([1,2,3]),
            'studio_id' => 1,
            'machine_id' => 1,
            'machine_price' => 6000,
            'machine_quantity' => 10,
            'duration' => 10,
            'starting_date' => now(),
            'deposit_amount' => 400,
            'deposit_date' => now(),
            'is_active' => true,
            'initial_currency_id' => 1
        ];
    }
}
