<?php

namespace App\Http\Requests\Admin\LicenseSettings;

use Illuminate\Foundation\Http\FormRequest;

class StoreLicenseSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name'               => 'required|string|max:255',
            'package_id'         => 'required|numeric',
            'price'              => 'required|numeric',
            'deposit'            => 'required|numeric',
        ];
    }

    public function attributes()
    {
        return [
            'name'               => 'Name',
            'package_id'         => 'Package',
            'price'              => 'Price',
            'deposit'            => 'Deposit',
        ];
    }
}
