<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AgreementTemplate extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'file_path',
        'name',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    /**
     * Get the agreements associated with the agreement template.
     */
    public function agreements(): HasMany
    {
        return $this->hasMany(Agreement::class);
    }
}
