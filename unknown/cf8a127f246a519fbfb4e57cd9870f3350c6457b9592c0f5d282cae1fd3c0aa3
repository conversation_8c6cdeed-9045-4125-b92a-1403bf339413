<?php

namespace App\Services\Admin\Woocommerce;

// use App\Models\Supplier;
// use Illuminate\Pagination\LengthAwarePaginator;

interface IWoocommerceService
{
    public function getProductById(int $id);
    public function getAllProducts();
    public function updateProduct(int $id, array $data);
    public function createProduct(array $data);
    public function getUpdatedProducts(string $date);
}
