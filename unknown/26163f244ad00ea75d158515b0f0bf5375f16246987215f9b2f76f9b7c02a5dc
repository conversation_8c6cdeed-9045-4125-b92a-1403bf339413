<?php

namespace App\Http\Requests\Admin\Order;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;

class StoreOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            'company_id' => 'required|integer|exists:companies,id',
            'supplier_id' => 'required|integer|exists:suppliers,id',
            'sent_at' => 'required|date',
            'items' => 'required|array',
            'items.*.name' => 'required|string|max:255',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric',
            'note' => 'nullable|string|max:500',
        ];
    }
    protected function prepareForValidation()
    {
        // Clean all items' price and add custom product
        if ($this->has('items')) {
            $items = $this->input('items');
            foreach ($items as $index => $item) {
                $items[$index]['name'] = '-';
                if (isset($item['price'])) {
                    $items[$index]['price'] = $this->cleanCurrency($item['price']);
                }
                if (isset($item['custom_product']) AND $item['custom_product'] == 1) {
                    if (isset($item['product_id']) AND $item['product_id'] > 0) {
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $edit_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'],
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];

                        DB::table('products')->where('id', $item['product_id'])->update($edit_custom_product);
                        // dd($items); // Dumps and stops execution
                    }else{
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $new_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'],
                            'category' => 'product',
                            'custom_product' => 1,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        DB::table('products')->insert($new_custom_product);
                        $items[$index]['product_id'] = DB::getPdo()->lastInsertId();
                    }
                }
            }
            $this->merge([
                'items' => $items
            ]);
        }
    }

    // Helper function to clean currency values by removing non-numeric characters
    private function cleanCurrency($value)
    {
        if ($value === '') {
            return null;
        }
        // Remove everything except numbers, dots, and negative sign
        return preg_replace('/[^0-9.-]+/', '', $value);
    }
    public function attributes()
    {
        return [
            'items.*.name' => 'name',
            'items.*.quantity'  => 'quantity',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()->has('items')) {
            toastr()->addError('The items field is required.');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
