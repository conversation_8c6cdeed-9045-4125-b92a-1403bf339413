<?php

namespace App\Helpers;


class ModelNamespaceHelper
{
    const MODELS = [
        'customer' => 'App\Models\Customer',
        'users'    => 'App\Models\User',
        'company'  => 'App\Models\Company',
        'machine'  => 'App\Models\Machine',
        'invoice'  => 'App\Models\Invoice',
        'payment'  => 'App\Models\Payment',
        'order'    => 'App\Models\Order',
        'supplier' => 'App\Models\Supplier',
        'studio' => 'App\Models\Studio',
    ];

    public static function getModelNamespaces($modelName): string
    {
        return self::MODELS[$modelName];
    }
}
