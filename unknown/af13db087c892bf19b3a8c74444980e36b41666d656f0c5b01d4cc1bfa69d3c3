<?php

namespace App\Providers;

use App\Models\Address;
use App\Models\AgreementTemplate;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Document;
use App\Models\DocumentStatus;
use App\Models\DocumentType;
use App\Models\Payment;
use App\Models\PaymentOption;
use App\Models\PaymentStatus;
use App\Models\Agreement;
use App\Models\BankAccount;
use App\Models\DocumentCategory;
use App\Models\DocumentItem;
use App\Models\Products;
use App\Models\ProductCategory;
use App\Models\ProductPhoto;
use App\Models\StudioLocation;
use App\Models\User;
use App\Repositories\Contracts\AddressRepository;
use App\Repositories\Contracts\AgreementRepository;
use App\Repositories\Contracts\AgreementTemplateRepository;
use App\Repositories\Contracts\BankAccountRepository;
use App\Repositories\Contracts\CompanyRepository;
use App\Repositories\Contracts\CustomerRepository;
use App\Repositories\Contracts\DocumentCategoryRepository;
use App\Repositories\Contracts\DocumentItemRepository;
use App\Repositories\Contracts\DocumentRepository;
use App\Repositories\Contracts\DocumentStatusRepository;
use App\Repositories\Contracts\DocumentTypeRepository;
use App\Repositories\Contracts\PaymentOptionRepository;
use App\Repositories\Contracts\PaymentRepository;
use App\Repositories\Contracts\PaymentStatusRepository;
use App\Repositories\Contracts\ProductCategoryRepository;
use App\Repositories\Contracts\ProductPhotoRepository;
use App\Repositories\Contracts\ProductRepository;
use App\Repositories\Contracts\StudioLocationRepository;
use App\Repositories\Contracts\UsersRepository;
use Illuminate\Support\ServiceProvider;

class RepositoryServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->app->singleton(
            UsersRepository::class,
            static function () {
                return new \App\Repositories\UsersRepository(new User());
            }
        );
        
        $this->app->singleton(
            AddressRepository::class,
            static function () {
                return new \App\Repositories\AddressRepository(new Address());
            }
        );

        $this->app->singleton(
            CompanyRepository::class,
            static function () {
                return new \App\Repositories\CompanyRepository(new Company());
            }
        );

        $this->app->singleton(
            CustomerRepository::class,
            static function () {
                return new \App\Repositories\CustomerRepository(new Customer());
            }
        );

        $this->app->singleton(
            AgreementRepository::class,
            static function () {
                return new \App\Repositories\AgreementRepository(new Agreement());
            }
        );

        $this->app->singleton(
            AgreementTemplateRepository::class,
            static function () {
                return new \App\Repositories\AgreementTemplateRepository(new AgreementTemplate());
            }
        );

        $this->app->singleton(
            StudioLocationRepository::class,
            static function () {
                return new \App\Repositories\StudioLocationRepository(new StudioLocation());
            }
        );

        $this->app->singleton(
            DocumentRepository::class,
            static function () {
                return new \App\Repositories\DocumentRepository(new Document());
            }
        );

        $this->app->singleton(
            DocumentTypeRepository::class,
            static function () {
                return new \App\Repositories\DocumentTypeRepository(new DocumentType());
            }
        );

        $this->app->singleton(
            DocumentStatusRepository::class,
            static function () {
                return new \App\Repositories\DocumentStatusRepository(new DocumentStatus());
            }
        );

        $this->app->singleton(
            DocumentCategoryRepository::class,
            static function () {
                return new \App\Repositories\DocumentCategoryRepository(new DocumentCategory());
            }
        );

        $this->app->singleton(
            DocumentItemRepository::class,
            static function () {
                return new \App\Repositories\DocumentItemRepository(new DocumentItem());
            }
        );

        $this->app->singleton(
            PaymentRepository::class,
            static function () {
                return new \App\Repositories\PaymentRepository(new Payment());
            }
        );

        $this->app->singleton(
            PaymentStatusRepository::class,
            static function () {
                return new \App\Repositories\PaymentStatusRepository(new PaymentStatus());
            }
        );

        $this->app->singleton(
            PaymentOptionRepository::class,
            static function () {
                return new \App\Repositories\PaymentOptionRepository(new PaymentOption());
            }
        );

        $this->app->singleton(
            BankAccountRepository::class,
            static function () {
                return new \App\Repositories\BankAccountRepository(new BankAccount());
            }
        );

        $this->app->singleton(
            ProductRepository::class,
            static function () {
                return new \App\Repositories\ProductRepository(new Product());
            }
        );

        $this->app->singleton(
            ProductPhotoRepository::class,
            static function () {
                return new \App\Repositories\ProductPhotoRepository(new ProductPhoto());
            }
        );

        $this->app->singleton(
            ProductCategoryRepository::class,
            static function () {
                return new \App\Repositories\ProductCategoryRepository(new ProductCategory());
            }
        );
    }
}
