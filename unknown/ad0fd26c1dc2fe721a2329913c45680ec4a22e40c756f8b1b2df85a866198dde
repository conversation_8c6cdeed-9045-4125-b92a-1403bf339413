<?php

namespace App\Http\Requests\Admin\Bundle;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class StoreBundleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            'title' => 'required',
            'items' => 'required|array',
            'items.*' => 'required_with:bundle_item|array',
            'items.*.id' => 'nullable|integer|exists:bundle_item,id',
            'items.*.product_id' => 'required|integer|exists:products,id',
            // 'items.*.bundle_id' => 'required|integer|exists:bundle,id',
            'items.*.quantity' => 'required|integer',
            'items.*.product_name' => 'string',
        ];
    }

    protected function prepareForValidation() 
    {        
        if(!$this->has('items')) {
            return;
        }
        $this->merge([
            'items' => array_values(array_filter(array_map(function($item) {
                // return item if has product_id
                if (isset($item['product_id'])) {
                    return $item;
                }
                // return null;
            }, $this->items)))
        ]);
    }
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()->has('items')) {

            toastr()->addError('Bundle items are required.');
        }

        if ($validator->errors()->has('title')) {
            toastr()->addError('The title field is required.');
        }

        if ($validator->errors()->has('items.*.product_id')) {
            toastr()->addError('Each item must have a product selected.');
        }

        if ($validator->errors()->has('items.*.quantity')) {
            toastr()->addError('Each product must have a valid quantity.');
        }

        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo(route('admin.bundles.create'));
    }
}
