<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncWooCommerceProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-woo-commerce-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $woocommerceService = new WoocommerceService();
        $products = $woocommerceService->getProducts();

        dd($products);
        foreach ($products as $product) {
            // Update Laravel database with WooCommerce product details
        }

        $this->info('WooCommerce products synced successfully.');
    }
}
