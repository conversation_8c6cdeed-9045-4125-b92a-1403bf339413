@if(count($payments) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Name') }}</div>
        {{-- <div>{{ __('Type') }}</div> --}}
        <div>{{ __('Description') }}</div>
        <div>{{ __('Amount') }}</div>
        <div>{{ __('Invoice') }}</div>
        <div id="payment_date" class="sortable-list-header" data-sort="payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach($payments as $payment)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <p class="my-0 hide-transp">{{ $payment->sequential_id }}</p>
                <p class="my-0">{{ $license->studio->name }}</p>
                {{-- <p class="my-0 text-secondary">{{ ucfirst($license->type) }}</p> --}}
                <p class="my-0 text-secondary">{{ $payment->description }}</p>
                @if($payment->status == \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                    <p class="my-0 text-success">+ {{ $admin_currency_symbol }}{{ number_format($payment->payment_amount, 2) }}</p>
                @else
                    <p class="my-0 text-danger">+ {{ $admin_currency_symbol }}{{ number_format($payment->payment_amount, 2) }}</p>
                @endif
                <p class="my-0 text-secondary">{{ $payment->invoice ? 'Invoice #' . $payment->invoice?->formatted_number : 'No invoice found' }}</p>
                <p class="my-0">{{ \Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y') }}</p>
                <div class="status-div">
                    @if($payment->status === \App\Helpers\Constants::PAYMENT_STATUS['not_paid'])
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-secondary text-secondary">
                            {{ __('Not Paid') }}
                        </div>
                    @elseif($payment->status === \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-success">
                            {{ __('Paid') }}
                        </div>
                    @else
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-danger text-danger">
                            {{ __('Declined') }}
                        </div>
                    @endif
                </div>     
                @if ($payment->invoice)
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-content" id="payment-history-dropdown-content">
                        <a href="{{ route('admin.download-invoice', ['customer' => $customer, 'invoice' => $payment->invoice ])}}">{{ __('Download Invoice') }}</a>
                        <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{ $payment->invoice->id }}">{{ __('Email Invoice') }}</a>
                        <a href=""  class="select-status" data-bs-toggle="modal" data-bs-target="#selectStatus{{ $payment->id }}">{{ __('Change Status') }}</a>
                    </div>
                </div>
                @endif     
            </div>
            @if ($payment->invoice)
                @include('partials.modals.select-status', [
                    'id' => $payment->id,
                    'route' => route('admin.payments.change-status', ['payment' => $payment]),
                    'payment' => $payment
                ])

                @include('partials.modals.email-invoice', [
                    'id' => $payment->invoice->id,
                    'route' => route('admin.email-invoice', ['customer' => $customer, 'invoice' => $payment->invoice]),
                    'invoice' => $payment->invoice
                ])
            @endif
        @endforeach
        <div id="paginate paginate-payment-history">
            @if($payments)
                <div class="">
                    {!! $payments->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const paymentsCount = @json($payments).total;
    const descriptiveLabel = paymentsCount === 1 ? ' Payment' : ' Payments';
    $('#paymentsCount').text(paymentsCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        // Control all on click
        selectAllElement.change(function() {
            if (this.checked) {
                tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', true);
            } else {
                tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        tableWrapper.find('.list-group input[type="checkbox"]').change(function(){
            let allChecked = true;
            let allNonChecked = false;
            tableWrapper.find('.list-group input[type="checkbox"]').each(function () {
                allChecked &= $(this).prop('checked');
                allNonChecked ||= $(this).prop('checked');
            })
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (allNonChecked === false) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.select-status').on('click', function(event) {
        event.preventDefault()
        event.stopPropagation();
    })

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

</script>
