<?php

namespace App\Services\Admin\Lease;

use App\Models\Customer;
use App\Models\Lease;
use App\Models\Machine;
use Illuminate\Pagination\LengthAwarePaginator;

interface ILeaseService
{
    public function store(array $data, Customer $customer);
    public function update(array $data, Lease $lease, Customer $customer);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator;
    public function delete(Lease $lease);
}
