@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="page-title no-border-btm">
            <div class="title-left">
                <h3>{{ __('edit location') }}</h3>
                <a href="{{ route('studio.lease.show', ['customer' => $customer, 'lease' => $lease]) }}"
                    class="back-link">← Back</a>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('studio.lease.update', ['customer' => $customer, 'lease' => $lease]) }}"
            id="studio-location-form">
            @csrf
            {{-- @dd($lease->studio); --}}
            <div class="studio_info_wrap">
                <h5 class="form-section-title mt-0">{{ __('location info') }}</h5>
                @include('partials.forms.input', [
                    'field_name' => 'studio[name]',
                    'field_label' => 'LOCATION NAME *',
                    'field_type' => 'text',
                    'field_value' => old('studio[name]', $lease->studio->name),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_first_name]',
                    'field_label' => 'FIRST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_first_name]', $lease->studio->owner_first_name),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_last_name]',
                    'field_label' => 'LAST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_last_name]', $lease->studio->owner_last_name),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[phone]',
                    'field_label' => 'PHONE # *',
                    'field_type' => 'text',
                    'field_value' => old('studio[phone]', $lease->studio->phone),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[email]',
                    'field_label' => 'EMAIL ADDRESS *',
                    'field_type' => 'email',
                    'field_value' => old('studio[email]', $lease->studio->email),
                    'field_class' => 'studio-input',
                ])

                <h5 class="form-section-title">{{ __('address info') }}</h5>
                @include('partials.forms.input', [
                    'field_name' => 'studio[address]',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => old('studio[address]', $lease->studio->address),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[city]',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => old('studio[city]', $lease->studio->city),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])

                @include('partials.forms.input', [
                    'field_name' => 'studio[zip]',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => old('studio[zip]', $lease->studio->zip),
                    'field_class' => 'studio-input',
                    'disabled' => 'disabled'
                ])
                @if($lease->studio->location === "USA")
                    @include('partials.forms.select', [
                        'field_name' => 'studio[state_id]',
                        'field_label' => 'STATE',
                        'values' => $states_countries['states'],
                        'field' => 'state',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => old('studio[state_id]', $lease->studio->state_id),
                        'include_empty' => true,
                        'disabled' => 'disabled'
                    ])
                @else
                    @include('partials.forms.select', [
                        'field_name' => 'studio[country_id]',
                        'field_label' => 'COUNTRY',
                        'values' => $states_countries['countries'],
                        'field' => 'countries',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => old('studio[country_id]', $lease->studio->country_id),
                        'include_empty' => true,
                        'disabled' => 'disabled'
                    ])
                @endif
            </div>
            <div
                class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button"
                    href="{{ route('studio.lease.show', ['customer' => $customer, 'lease' => $lease]) }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button" data-bs-toggle="modal"
                    data-bs-target="#deleteModal{{ 'Lease' }}{{ $lease->id }}"
                    data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>
@endsection

@include('partials.modals.delete', [
    'type' => 'License',
    'id' => $lease->id,
    'route' => route('studio.lease.destroy', ['customer' => $customer, 'lease' => $lease]),
    'title' => $lease->name,
])
