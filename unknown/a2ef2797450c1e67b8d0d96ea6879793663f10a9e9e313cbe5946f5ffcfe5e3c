<div class="offcanvas offcanvas-bottom py-3 h-auto" tabindex="-1"
     id="offcanvasFilterPayments"
     aria-labelledby="offcanvasFilterPaymentsLabel">
    <div class="d-flex flex-column align-items-start m-n3 m-sm-0 px-5 py-5 border-bottom position-relative">
        <div class="d-flex align-items-center">
            <h1 class="mb-0 custom-fs-12px text-uppercase fw-semibold">{{ __('FILTER BY') }}</h1>
        </div>
        <button type="button" class="btn-close d-block d-sm-none position-absolute top-50 mt-n2 end-0 me-6"
                data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="row justify-content-center mt-4 mobile-dropdown-content">
        <div class="col-11 py-4 border-bottom mobile-dropdown-option text-secondary" data-value="week">
            Last 7 days
        </div>
        <div class="col-11 py-4 border-bottom mobile-dropdown-option" data-value="month">
            Last 30 days
        </div>
        <div class="col-11 py-4 border-bottom mobile-dropdown-option" data-value="year">
            This year
        </div>
        <div class="col-11 my-4">
            <button type="button" class="btn btn-primary no-uppercase-no-bold w-100 apply-filters">
                {{ __('APPLY') }}
            </button>
        </div>
    </div>
</div>
<script type="module">
    document.addEventListener('DOMContentLoaded', function () {
        // Define the mapping for period display text
        let periodText = {
            'week': 'In the last 7 days',
            'month': 'In the last 30 days',
            'year': 'In the current year'
        };
        let selectedValue = 'week';
        $('.mobile-dropdown-option').click(function (e) {
            e.preventDefault();
            e.stopPropagation();

            selectedValue = $(this).data('value');

            $('.mobile-dropdown-content .mobile-dropdown-option').removeClass('text-secondary');
            $(this).addClass('text-secondary');
        });

        $('.apply-filters').click(function (e) {
            e.preventDefault();
            e.stopPropagation();

            // Update the text of the period on the card
            $('#dashboard-sum-card').text(periodText[selectedValue]);

            window.location.href = "{{ route('admin.dashboard.index', ['period' => '__period__']) }}".replace('__period__', selectedValue);
        });
    });
</script>
