<?php

namespace App\Services\Admin\LicenseSettings;

use App\Models\LicenseSettings;
use Illuminate\Pagination\LengthAwarePaginator;

class LicenseSettingsService implements ILicenseSettingsService
{
    public function store(array $data)
    {
        $licenseSettings = LicenseSettings::create($data);
    }

    public function update(array $data, LicenseSettings $licenseSettings)
    {
        $licenseSettings->update($data);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator
    {
        $query = LicenseSettings::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        // $query->when($status !== 'all', function ($query) use ($status) {
        //     $query->where('is_active', $status);
        // });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('id', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('price', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('deposit', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function delete(LicenseSettings $licenseSettings)
    {
        $licenseSettings->contact()->delete();
        $licenseSettings->orders()->delete();
        $licenseSettings->delete();
    }

    public function deleteMultiple(array $ids)
    {
        foreach ($ids as $id) {
            $licenseSettings = LicenseSettings::find($id);
            $this->delete($licenseSettings);
        }
    }
}
