<?php

namespace App\Helpers;

use App\Models\Currency;
use App\Models\Lease;
use App\Models\License;
use App\Models\Payment;
use App\Models\Purchase;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CurrencyConversionHelper
{
    public static function calculatePaymentsSum(Collection $payments, Currency $currency): float
    {
        $paymentsSum = 0;
        foreach ($payments as $payment) {
            if($payment->license_id){
                $paymentConversionRate =  $payment->license->conversionRate->where('currency_id', $currency->id)->first()->rate;
                $paymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);                
            }else if($payment->lease_id){
                $paymentConversionRate = $payment->lease->conversionRate->where('currency_id', $currency->id)->first()->rate;
                $paymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);                
            }else if($payment->purchase_id){
                $purchase = Purchase::find($payment->purchase_id);
                if($purchase){
                    $paymentConversionRate = $purchase->conversionRate->where('currency_id', $currency->id)->first()->rate;
                    $paymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);
                }
            }
        }
        return $paymentsSum;
    }

    public static function calculatePaymentsSeparately(Collection $payments, Currency $currency): array
    {
        $licensePaymentsSum = 0;
        $leasePaymentsSum = 0;
        $purchasePaymentsSum = 0;
        foreach ($payments as $payment) {
            if($payment->license_id){
                $paymentConversionRate =  $payment->license->conversionRate->where('currency_id', $currency->id)->first()->rate;
                $licensePaymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);                
            }else if($payment->lease_id){
                $paymentConversionRate = $payment->lease->conversionRate->where('currency_id', $currency->id)->first()->rate;
                $leasePaymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);                
            }else if($payment->purchase_id){
                $purchase = Purchase::find($payment->purchase_id);
                if($purchase){
                    $paymentConversionRate = $purchase->conversionRate->where('currency_id', $currency->id)->first()->rate;
                    $purchasePaymentsSum += self::calculatePaymentAmount($payment, $paymentConversionRate);
                }
            }
        }
        return [
            'licensePaymentsSum' => $licensePaymentsSum,
            'leasePaymentsSum' => $leasePaymentsSum,
            'purchasePaymentsSum' => $purchasePaymentsSum
        ];
    }

    public static function calculatePaymentAmount(Payment $payment, float $conversionRate): float
    {
        if($payment->lease_id){
            return self::calculateLeasePaymentAmount($payment, $payment->lease, $conversionRate);
        }else{
            return self::calculateLicensePaymentAmount($payment, $conversionRate);
        }
    }

    public static function calculateLicensePaymentsSum(License $license, float $conversionRate): array
    {
        $paymentSum = 0;
        $remainingPayments = $license->payments->where('status', Constants::PAYMENT_STATUS['not_paid']);
        $remainingPaymentsCount = count($remainingPayments);
        foreach ($remainingPayments as $payment) {
            $paymentSum += round($payment->payment_amount / $conversionRate / 100, 2);
        }
        return [
            'paymentSum' => $paymentSum,
            'count' => $remainingPaymentsCount
        ];
    }

    public static function calculateLicenseTotalPrice(License $license, float $conversionRate): float
    {
        return self::calculateLicenseMonthlyPrice($license, $conversionRate) * $license->duration;
    }

    public static function calculateLicenseMonthlyPrice(License $license, float $conversionRate): float
    {
        return round($license->price / $conversionRate / 100, 2);
    }

    public static function calculateLicensePaymentAmount(Payment $payment, float $conversionRate): float
    {
        return round($payment->payment_amount / $conversionRate / 100, 2);
    }

    public static function calculateLicenseDeposit(License $license, float $conversionRate): float
    {
        return round($license->deposit_amount / $conversionRate / 100, 2);
    }

    public static function calculateLeaseTotalPrice(Lease $lease, float $conversionRate): float
    {
        return round($lease->monthly_installment / $conversionRate / 100, 2) * $lease->machine_quantity * $lease->duration;
    }

    public static function calculateLeaseMonthlyPrice(Lease $lease, float $conversionRate): float
    {
        return round($lease->monthly_installment / $conversionRate / 100, 2) * $lease->machine_quantity;
    }

    public static function calculateLeasePrice(Lease $lease, float $conversionRate): float
    {
        return round($lease->monthly_installment / $conversionRate / 100, 2);
    }

    public static function calculateLeaseDeposit(Lease $lease, float $conversionRate): float
    {
        return round($lease->deposit_amount / $conversionRate / 100, 2);
    }

    public static function calculateLeasePaymentsSum(Lease $lease, float $conversionRate): array
    {
        $paymentSum = 0;
        $remainingPayments = $lease->payments()->where('status', Constants::PAYMENT_STATUS['not_paid'])->get();
        $remainingPaymentsCount = count($remainingPayments);
        foreach ($remainingPayments as $payment) {
            $paymentSum += self::calculateLeasePaymentAmount($payment, $lease, $conversionRate);
        }
        return [
            'paymentSum' => $paymentSum,
            'count' => $remainingPaymentsCount
        ];
    }


    public static function calculateLeasePaymentAmount(Payment $payment, Lease $lease, $conversionRate): float
    {
        return round($payment->payment_amount / $lease->machine_quantity / $conversionRate / 100, 2) * $lease->machine_quantity;
    }
}
