@if (count($licenses) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="studio.name" class="sortable-list-header" data-sort="studio.name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>

        <div id="type" class="sortable-list-header" data-sort="type">{{ __('Type') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="zip" class="sortable-list-header" data-sort="type">{{ __('Zip') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="price" class="sortable-list-header" data-sort="price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        {{-- <div id="remaining" class="sortable-list-header" data-sort="type">{{ __('Remaining') }}
            <div class="sort-icon asc"></div>
        </div> --}}
        <div>{{ __('Expires On') }}</div>
        <div id="is_active" class="sortable-list-header" data-sort="is_active">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            @foreach ($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach ($licenses as $license)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <p class="my-0 hide-transp">{{ $license->sequential_id }}</p>
                <a href="{{ route('studio.location.show', ['customer' => $customer, 'license' => $license, 'tab' => 'profile']) }}"
                    style="text-decoration: none;" class="my-0 d-flex align-items-center gap-1 medium">
                    <!-- {{ $license->studio->zip }}/{{ $license->studio->city }}, {{ $license->studio->name }} #{{ $license->sequential_id }}
                        {{-- ({{ $license->note_count }}) --}} -->
                    {{ $license->studio->name }}
                    @if ($license->type == 'exclusivity')
                        <img src="{{ asset('/exclusivity-icon.svg') }}" alt="exclusivity" class="ms-1">
                    @endif
                </a>
                <p class="my-0 text-secondary">{{ ucfirst($license->type) }}</p>
                <p class="my-0"> {{ $license->studio->zip }} </p>
                {{-- <p class="my-0">{{$license->studio->zip}}</p> --}}
                <div class="table-div">
                    <p class="my-0 text-success">
                        {{-- {{$admin_currency_symbol}}{{number_format($license->price, 2)}} --}}
                        {{ $currentCurrency->symbol ?? "$" }}{{ number_format($license->price, 2) }}
                    </p>
                    <p class="my-0">/year</p>
                </div>
                <p class="my-0">{{ \Carbon\Carbon::parse($license->expires)->format('m/d/Y') }}</p>
                <div class="status-div">
                    @include('partials.status-badge', [
                        'status' => $license->is_active ? 'active' : 'inactive',
                    ])
                </div>
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-content" id="licences-dropdown-content">
                        <a
                            href="{{ route('studio.location.edit', ['customer' => $customer, 'license' => $license]) }}">{{ __('Edit') }}</a>
                        <a href="" class="cancel-license text-danger" data-bs-toggle="modal"
                            data-bs-target="#cancelModal{{ 'License' }}{{ $license->id }}">{{ __('Cancel') }}</a>
                    </div>
                </div>
            </div>
            @include('partials.modals.cancel', [
                'type' => 'License',
                'id' => $license->id,
                'route' => route('admin.licenses.change-status', [
                    'customer' => $customer,
                    'license' => $license,
                    'is_active' => '0',
                ]),
                'title' => 'License',
            ])
        @endforeach
        <div id="paginate paginate-licences">
            @if ($licenses)
                <div class="">
                    {!! $licenses->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
    <p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    function clearLicenseActiveTab() {
        localStorage.removeItem('licenseActiveTab');
    }

    const licensesCount = @json($licenses).total;
    const descriptiveLabel = licensesCount === 1 ? ' Item' : ' Items';
    $('#licensesCount').text(licensesCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.cancel-license').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
