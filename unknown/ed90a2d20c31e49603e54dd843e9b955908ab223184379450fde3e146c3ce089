<?php

namespace App\Services\Admin\License;

use App\Models\Customer;
use App\Models\License;
use App\Models\Machine;
use Illuminate\Pagination\LengthAwarePaginator;

interface ILicenseService
{
    public function store(array $data, Customer $customer);
    public function update(array $data, License $license, Customer $customer);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator;
    public function delete(License $license);
}
