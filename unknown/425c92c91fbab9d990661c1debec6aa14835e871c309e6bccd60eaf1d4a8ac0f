<?php

namespace App\Helpers;

use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\Payment;
use App\Models\Purchase;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\View\View;
class InvoiceHelper
{
    public static function generateInvoicePDF(Invoice $invoice): \Barryvdh\DomPDF\PDF
    // public static function generateInvoicePDF(Invoice $invoice)
    {
        $payment = $invoice->payment;
        if ($payment->license_id) {
            $company = $payment->license->company;
            $studio = $payment->license->studio;
            $duration = Payment::where('license_id', $payment->license->id)->where('payment_date', '>', $payment->payment_date)->count();

            $conversionRate = $payment->license->conversionRate()->where('currency_id', $payment->license->initial_currency_id)->first()->rate;
            $currencySymbol = $payment->license->initialCurrency->symbol;
            $paymentAmount = CurrencyConversionHelper::calculateLicensePaymentAmount($payment, $conversionRate);

            $pdf = Pdf::loadView('pdfs.license-invoice', compact('company','studio', 'invoice', 'payment', 'duration', 'conversionRate', 'currencySymbol', 'paymentAmount'));
        } else if ($payment->lease_id) {
            $company = $payment->lease->company;
            $studio = $payment->lease->studio;
            $duration = Payment::where('lease_id', $payment->lease->id)->where('payment_date', '>', $payment->payment_date)->count();

            $conversionRate = $payment->lease->conversionRate()->where('currency_id', $payment->lease->initial_currency_id)->first()->rate;
            $currencySymbol = $payment->lease->initialCurrency->symbol;
            $leaseMonthlyPrice = CurrencyConversionHelper::calculateLeaseMonthlyPrice($payment->lease, $conversionRate);
            $leasePrice = CurrencyConversionHelper::calculateLeasePrice($payment->lease, $conversionRate);

            $pdf = Pdf::loadView('pdfs.lease-invoice', compact('company','studio', 'invoice', 'duration', 'payment', 'conversionRate', 'currencySymbol', 'leaseMonthlyPrice', 'leasePrice'));
        }else if ($payment->purchase_id) {
            $purchase = Purchase::find($payment->purchase_id);
            $company = $purchase->company;
            $studio = $purchase->studio;
            $duration = Payment::where('purchase_id', $payment->purchase_id)->where('payment_date', '>', $payment->payment_date)->count();
            // $company = $payment->license->company;
            // $studio = $payment->license->studio;
            // $duration = Payment::where('license_id', $payment->license->id)
            //     ->where('payment_date', '>', $payment->payment_date)->count();

            $conversionRate = $purchase->conversionRate()->where('currency_id', $purchase->initial_currency_id)->first()->rate;
            $currencySymbol = $purchase->initialCurrency->symbol;
            $paymentAmount = CurrencyConversionHelper::calculateLicensePaymentAmount($payment, $conversionRate);

            $pdf = Pdf::loadView('pdfs.purchase-invoice', compact('company','studio', 'invoice', 'payment', 'purchase', 'duration', 'conversionRate', 'currencySymbol', 'paymentAmount'));
        }

        return $pdf;
    }

    public static function generateInvoiceProductPDF(InvoiceProduct $invoiceProduct): \Barryvdh\DomPDF\PDF
    {
        $invoiceProduct->load('items.product', 'customer.owner', 'company.image');
        return Pdf::loadView('pdfs.invoice-product', compact('invoiceProduct'));
    }
    public static function viewInvoiceProductPDF(InvoiceProduct $invoiceProduct): \Barryvdh\DomPDF\PDF
    {
        $invoiceProduct->load('items.product', 'customer.owner', 'company.image');
        return Pdf::loadView('pdfs.invoice-product', compact('invoiceProduct'));
    }
}
