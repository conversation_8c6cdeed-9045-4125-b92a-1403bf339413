<?php

namespace App\Http\Requests\Admin\License;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class StoreLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'type'                    => 'required|in:license,exclusivity',
            'location'                => 'required|in:USA,International',
            'price'                   => 'required|numeric',
            'duration'                => 'required|integer',
            'package'                 => 'required|integer',
            'starting_date'           => 'required|date',
            'deposit_amount'          => 'nullable|numeric',
            'deposit_date'            => 'nullable|date',
            'company_id'              => 'required|exists:companies,id',
            'studio_id'               => 'nullable|exists:studios,id',
            'description'             => 'nullable|string',
            'studio.name'             => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.owner_first_name' => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.owner_last_name'  => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.email'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.phone'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.address'          => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.city'             => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.state_id'         => ['nullable', 'integer', 'exists:states,id'],
            'studio.country_id'       => ['nullable', 'integer', 'exists:countries,id'],
            'studio.zip'              => [Rule::requiredIf(is_null(request()->input('studio_id')))],
        ];


    }

    public function attributes()
    {
        return [
            'studio.name' => 'name',
            'studio.owner_first_name' => 'owner first name',
            'studio.owner_last_name' => 'owner last name',
            'studio.email' => 'email',
            'studio.phone' => 'phone',
            'studio.address' => 'address',
            'studio.city' => 'city',
            'studio.state_id' => 'state',
            'studio.zip' => 'zip',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()) {
            // dd($validator->errors());
            toastr()->addError('Please check all the fields');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }

}
