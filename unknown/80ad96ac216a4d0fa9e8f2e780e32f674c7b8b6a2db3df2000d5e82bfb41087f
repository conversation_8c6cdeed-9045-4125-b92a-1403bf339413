<?php

namespace App\Http\Requests\Admin\InvoiceProduct;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;

class StoreInvoiceProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            'company_id' => 'required|exists:companies,id',
            'customer_id' => 'required|exists:customers,id',
            'send_date' => 'required|date',
//            'sent_date' => 'required|date|after_or_equal:send_date',
            'shipping_fee' => 'nullable|numeric|min:0',
            'handling_fee' => 'nullable|numeric|min:0',
            'show_tax' => 'required|numeric|min:0',
            'paid' => 'numeric',
            'note' => 'nullable|string',
            'items' => 'required|array',
            'items.*' => 'required_with:invoice_product_item|array',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.price' => 'required|numeric|min:0',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.deposit' => 'nullable|numeric|min:0',
            'items.*.discount' => 'nullable|numeric|min:0',
            'items.*.discount_type' => 'required|string',
            'items.*.product_name' => 'string',
        ];
    }

    // This method runs before validation to clean the request data
    protected function prepareForValidation()
    {
        // dd($this); // Dumps and stops execution

        // Clean fields that should not contain the "$" symbol
        if ($this->has('shipping_fee')) {
            $this->merge([
                'shipping_fee' => $this->cleanCurrency($this->input('shipping_fee'))
            ]);
        }

        if ($this->has('handling_fee')) {
            $this->merge([
                'handling_fee' => $this->cleanCurrency($this->input('handling_fee'))
            ]);
        }

        // Clean all items' price and discount fields
        if ($this->has('items')) {
            $items = $this->input('items');
            foreach ($items as $index => $item) {
                if (isset($item['price'])) {
                    $items[$index]['price'] = $this->cleanCurrency($item['price']);
                }
                if (isset($item['discount'])) {
                    $items[$index]['discount'] = $this->cleanCurrency($item['discount']);
                }
                if (isset($item['deposit'])) {
                    $items[$index]['deposit'] = $this->cleanCurrency($item['deposit']);
                }
                if (isset($item['custom_product']) AND $item['custom_product'] == 1) {
                    if (isset($item['product_id']) AND $item['product_id'] > 0) {
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $edit_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'],
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];

                        DB::table('products')->where('id', $item['product_id'])->update($edit_custom_product);
                        // dd($items); // Dumps and stops execution
                    }else{
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $new_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'],
                            'category' => 'product',
                            'custom_product' => 1,
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                        DB::table('products')->insert($new_custom_product);
                        $items[$index]['product_id'] = DB::getPdo()->lastInsertId();
                    }
                }
            }
            $this->merge([
                'items' => $items
            ]);
        }
    }

    // Helper function to clean currency values by removing non-numeric characters
    private function cleanCurrency($value)
    {
        if ($value === '') {
            return null;
        }
        // Remove everything except numbers, dots, and negative sign
        return preg_replace('/[^0-9.-]+/', '', $value);
    }

    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()->has('items')) {
            toastr()->addError('The items field is required.');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
