<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Settings\UpdateAdminSettingsRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class SettingsController extends Controller
{
    public function index(): View
    {
        $user     = auth()->user();
        $settings = $user->adminSettings;

        return view('admin.settings.index', compact('settings', 'user'));
    }

    public function update(UpdateAdminSettingsRequest $request)
    {
        try {
            $user = auth()->user();
            $user->adminSettings()->update([
                'timezone'        => $request->get('timezone'),
                'date_format'     => $request->get('date_format'),
                'time_format'     => $request->get('time_format'),
                'week_start'      => $request->get('week_start'),
                'currency_id'     => $request->get('currency'),
                'seo_title'       => $request->get('seo_title'),
                'seo_description' => $request->get('seo_description'),
                'smtp_from_name'  => $request->get('smtp_from_name'),
                'smtp_from_email' => $request->get('smtp_from_email'),
            ]);

            $user->update([
                'email' => $request->get('email'),
            ]);

            if ($request->has('password') && $request->get('password') !== null) {
                $user->update(['password' => Hash::make($request->get('password'))]);
            }

            if ($request->has('photo')) {
                $this->uploadImage($request, 'photo');
            }

            if (filter_var($request->get('photo_deleted'), FILTER_VALIDATE_BOOLEAN)) {
                $this->deleteImage($user->photo, 'photo');
            }

            if ($request->has('logo')) {
                $this->uploadImage($request, 'logo');
            }

            if (filter_var($request->get('logo_deleted'), FILTER_VALIDATE_BOOLEAN)) {
                $this->deleteImage($user->adminSettings->logo, 'logo');
            }

            toastr()->addSuccess('', 'Successfully updated!');
            return redirect()->back();

        } catch (\Exception $exception) {
            return redirect()->back()->withErrors([$exception->getMessage()]);
        }
    }

    public function uploadImage(Request $request, string $imageType)
    {
        $user      = auth()->user();
        $file      = $request->file($imageType);
        $imagePath = $user->$imageType;
        if ($imagePath) {
            $this->deleteImage($imagePath, $imageType);
        }
        $path = $file->storeAs('public/instructors/' . $user->id . '-' . $file->hashName());
        if ($imageType === 'photo') {
            $user->update(['photo' => str_replace('public/', '', $path)]);
        } else {
            $user->adminSettings()->update(['logo' => str_replace('public/', '', $path)]);
        }
    }

    public function deleteImage(string|null $imagePath, string $imageType): void
    {
        if ($imagePath) {
            $user     = auth()->user();
            $filePath = storage_path('app/public/' . $imagePath);
            if (File::exists($filePath)) {
                File::delete($filePath);
            }

            if ($imageType === 'photo') {
                $user->update(['photo' => null]);
            } else {
                $user->adminSettings()->update(['logo' => null]);
            }
        }
    }
}
