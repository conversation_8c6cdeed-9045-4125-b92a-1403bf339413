@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="page-title">
            <div class="title-left">
                <h3>{{ __('new license Package') }}</h3>
                <a href="{{ route('admin.licenseSettings.index') }}" class="back-link">← Back</a>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.licenseSettings.store') }}" id="license-settings-form">
            @csrf

            <h5 class="form-section-title mt-0" style="border: none">{{ __('License Info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'NAME *',
                'field_type' => 'text',
                'field_value' => old('name', NULL),
            ])

            @include('partials.forms.select', [
                'field_name' => 'package_id',
                'field_label' => 'Package *',
                'values' => \App\Helpers\Constants::LICENSE_PACKAGES,
                'field' => 'package_id',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('package_id', NULL),
                'include_empty' => true,
            ])      

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'PRICE *',
               'field_type' => 'text',
               'currency' => true,
               'field_value' => old('price', NULL),
               'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'deposit',
                'field_label' => 'DEPOSIT *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('deposit', NULL),
                'input_field_class' => 'decimal-field',
            ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.licenseSettings.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection