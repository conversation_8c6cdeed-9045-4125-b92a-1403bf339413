@import "variables"
@import "mixins"

.dashboard-side
    background-color: $primary
    color: $white
    height: 100vh !important
    z-index: 10
    position: fixed
    +breakpoint(lg)
        position: relative

    .hamburger
        top: 10px
        right: 10px

    .dashboard-wrap
        height: 100%

    .logo
        width: 240px

    .menu, .location-wrap
        border-color: #333333 !important

    .menu-item
        letter-spacing: .6px
        font-weight: 600
        color: $white
        text-wrap: nowrap
        overflow: hidden
        text-transform: uppercase
        text-decoration: none
        &.active
            opacity: .7

    .menu-item-has-children
        overflow: visible
        cursor: pointer

        svg
            +sc-transition

        &:not(.collapsed)
            svg
                transform: rotate(180deg)

    #sidebar-submenu .menu-item
        text-transform: none
        font-weight: 400

.profile-thumbnail
    width: 80px
    height: 80px
    overflow: hidden
    border-radius: 50px
    &-big
        width: 160px
        height: 160px
        border-radius: 90px
    img
        object-fit: cover
        width: 100%
        height: 100%

#dashboard-icons
    .dashboard-wrap
        width: 60px
        text-align: center
    .menu-item
        padding: 12px 0
        border-top: 1px solid #333333
        &:last-of-type
            border-bottom: 1px solid #333333
        &.active > *
            opacity: .5

    #sidebar-submenu-mini
        position: absolute
        top: 0
        right: 0
        transform: translateX(100%)

    .sidebar-submenu-mini-inner
        gap: 20px
        text-align: left
        background-color: #fff
        border: 1px solid $light
        width: 200px

        .menu-item
            border: none
            color: $primary
            padding: 0
            text-transform: none
            font-weight: 400
            font-size: 11px

            &:last-of-type
                border-bottom: none

.main-content
    height: 100vh
    overflow: auto

.dash-stats
    display: grid
    gap: 20px
    grid-template-columns: 1fr 1fr
    +breakpoint(md)
        gap: 40px
    +breakpoint(lg)
        grid-template-columns: 1fr 1fr 1fr 1fr

    .stat
        height: 0
        padding-bottom: 70%
        border: 1px solid $light
        position: relative
        &-class
            padding-bottom: 70%
        .stat-status
            bottom: 20px
            position: absolute
            left: 0
            right: 0
            text-align: center
        .stat-content
            position: absolute
            top: 50%
            left: 50%
            transform: translate(-50%, -50%)
            text-align: center
            width: 100%

        .arrow-up,
        .arrow-down
            border-left: 4px solid transparent
            border-right: 4px solid transparent
            display: inline-block
            padding: 0
            margin: 0 6px 2px 0
        .arrow-up
            border-bottom: 5px solid $success
        .arrow-down
            border-top: 5px solid $danger

.dashboard-tables-wrap
    gap: 64px

// Review score on some entities
.entity-grade
    background-color: #E8AF44
    color: #fff
    width: 40px
    height: 40px
    border-radius: 100px

// Store location
.location-label
    border-radius: 100px
    background-color: #e5f6e6
    color: $success
    padding: 6px 14px

.fa-star
    color: $warning

// Internal notes
.internal-notes
    .note
        font-size: 14px

// Instructor payouts
.payouts
    tr
        display: flex
        flex-direction: column
        &:last-of-type td
            @extend .border-bottom
            @extend .border-light

    th
        @extend .border-top
        @extend .border-bottom
        @extend .border-light
        @extend .py-4
        text-transform: uppercase
        font-weight: 400

    td
        display: flex
        justify-content: space-between
        align-items: center
        @extend .px-4
        @extend .py-5

    .price
        font-size: 14px
        margin-bottom: 6px

    .classes
        margin-bottom: 0
        @extend .text-secondary

    .status-label
        border-radius: 100px
        padding: 6px 14px

// List styles
.list-group-item
    border-left: none
    border-right: none
    border-top: none

    .progress
        max-width: 100px

    .list-thumbnail
        width: 60px
        height: 60px
        // overflow: hidden
        img
            width: inherit
            height: inherit
            object-fit: cover

    .btn
        min-width: 126px

// END List styles

// Table filter select fields
.table-filter-select
    padding-right: 33px
    border: none

    + .select2-container--default
        max-width: fit-content

        .select2-selection--single
            background-color: transparent
            border: none

        .select2-selection__rendered
            @extend .text-secondary
            padding: 10px 26px 10px 0 !important

        .select2-selection--single .select2-selection__arrow
            right: 0
            top: 50%
            transform: translateY(-50%)
            width: 12px
            height: 8px

            b
                top: 0
                left: 0
                margin: 0
// Table filter select field focus
.table-filter-select:focus
    border-color: transparent
    outline: none
    box-shadow: none
// Form group select fields
.form-group
    .select2-container
        .select2-selection--single
            display: flex
            align-items: center
            .select2-selection__rendered
                min-height: 50px
                display: flex
                align-items: center
            .select2-selection__arrow
                top: unset

.spots
    input[type="checkbox"], input[type="radio"]
        position: absolute
        opacity: 0
        cursor: pointer
        height: 0
        width: 0

    .spot
        width: 38px
        height: 38px
        background-color: $white
        border-radius: 35px
        border: 1px solid  $primary
        text-align: center
        font-size: 16px
        line-height: 40px
        cursor: pointer
        +sc-transition
        +breakpoint(md)
            width: 46px
            height: 46px
            line-height: 46px
        +breakpoint(lg)
            width: 60px
            height: 60px
            line-height: 60px

        &.checked
            background-color: $primary
            color: $white

        &.taken
            background-color: $light
            color: #c9c9c9
            border-color: $light
            cursor: not-allowed

.calendar-wrap
    .arrow
        position: absolute
        top: 50%
        transform: translateY(-50%)
        z-index: 1
        cursor: pointer

        &.next-arrow
            right: 0

        &.prev-arrow
            left: 0

// Client page specifics
#clientCourseModal, #clientInstructorModal
    .list-thumbnail
        width: 70px
        height: 70px

// Search
.lg-search
    position: relative
    padding-left: 20px
    .form-control
        border-radius: 20px
        border: 1px solid #eaeaea
        padding: 0
        width: 40px
        min-height: 40px
        height: 40px
        transition: all 0.3s ease
        &::placeholder
            transition: all 0.3s ease
            opacity: 0
        &.lg-search-expanded
            width: 330px
            padding-left: 20px
            &::placeholder
                opacity: 1
    .lg-search-ico
        position: absolute
        top: 2px
        right: 2px
        &:hover
            cursor: pointer


.admin-stats
  height: 0
  padding-bottom: 70%
  border: 1px solid $light
  position: relative
  &-class
    padding-bottom: 70%
  .stat-status
    bottom: 20px
    position: absolute
    left: 0
    right: 0
    text-align: center
  .stat-content
    position: absolute
    top: 50%
    left: 50%
    transform: translate(-50%, -50%)
    text-align: center
    width: 100%

  .arrow-up,
  .arrow-down
    border-left: 4px solid transparent
    border-right: 4px solid transparent
    display: inline-block
    padding: 0
    margin: 0 6px 2px 0
  .arrow-up
    border-bottom: 5px solid $success
  .arrow-down
    border-top: 5px solid $danger

.wrapper-card-dashboard
  padding: 50px
  border: 1px solid #F0F0F0
  //max-width: 95%
  //margin-top: 70px
  .card-dashboard-header
    .title-card
      margin-bottom: 50px
      font-size: 14px
      color: #000000
      text-transform: uppercase
      display: block
      font-weight: 600
  .card-dashboard-content
    display: flex
    align-items: center
    flex-direction: column
    #item-card-dashboard-plans-2
      margin-bottom: 60px
    .item-card-dashboard
      margin-bottom: 20px
      display: flex
      justify-content: space-between
      width: 100%
      .label-card
        font-size: 14px
        color: #000000

.mt-70
  margin-top: 70px

.star-value-container
  min-width: 35px
