@extends('layouts.app')

@section('content')
    <div class="page-title ttl-dropdown">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ __('Profile') }}</h3>
            </div>
        </div>
    </div>
    <div class="main-subtitle no-border-btm">
        <h5>{{ __('basic info') }}</h5>
    </div>
    <div>
        <div class="profile-info-div">
            <div class="instructor-basic-info">
                <div class="text-secondary">Customer Name:</div>
                <div class="">
                    {{ $customer->name }}
                </div>
            </div>
            <div class="instructor-basic-info">
                <div class="text-secondary">Licensee name:</div>
                <div class="">
                    {{ $customer->owner->first_name }} {{ $customer->owner->last_name }}
                </div>
            </div>
            <div class="instructor-basic-info">
                <div class="text-secondary">Phone #:</div>
                <div class="">
                    {{ $customer->owner->phone }}
                </div>
            </div>
            <div class="instructor-basic-info">
                <div class="text-secondary">Email (default):</div>
                <div class="">
                    {{ $customer->owner->email }}
                </div>
            </div>
            @if ($customer->owner->email2 != '')
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{ $customer->owner->email2 }}
                    </div>
                </div>
            @endif
        </div>
    </div>
    <div class="main-subtitle no-border-btm">
        <h5>BILLING ADDRESS</h5>
    </div>
    <div>
        <div class="profile-info-div">
            <div class="instructor-basic-info">
                <div class="text-secondary">Address:</div>
                <div class="">
                    {{ $customer?->address }}
                </div>
            </div>
            <div class="instructor-basic-info">
                <div class="text-secondary">City:</div>
                <div class="">
                    {{ $customer?->city }}
                </div>
            </div>
            @if ($customer->location === \App\Helpers\Constants::LOCATION_TYPE[0])
                <div class="instructor-basic-info">
                    <div class="text-secondary">State:</div>
                    <div class="">
                        {{ $customer?->state?->name }}
                    </div>
                </div>
            @else
                <div class="instructor-basic-info">
                    <div class="text-secondary">Country:</div>
                    <div class="">
                        {{ $customer?->country?->name }}
                    </div>
                </div>
            @endif
            <div class="instructor-basic-info">
                <div class="text-secondary">ZIP Code:</div>
                <div class="">
                    {{ $customer?->zip }}
                </div>
            </div>
        </div>
    </div>
    <div
        class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
        <a type="button" href="{{ route('studio.profile.edit', ['customer' => $customer]) }}">
            <button type="submit" class="btn btn-primary fw-normal">{{ __('Edit') }}</button>
        </a>
    </div>

@endsection
