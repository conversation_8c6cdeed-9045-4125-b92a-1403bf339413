@if(count($licenseSettingss) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id">
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        {{-- <div id="id" class="sortable-list-header p-0 {{ $licenseSettingss[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div> --}}
        <div id="name" class="sortable-list-header {{ $licenseSettingss[0]->orderParam == 'name' ? 'active' : '' }}" data-sort="name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="price" style="" class="justify-content-end sortable-list-header {{ $licenseSettingss[0]->orderParam == 'price' ? 'active' : '' }}" data-sort="price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="deposit" style="" class="justify-content-end sortable-list-header {{ $licenseSettingss[0]->orderParam == 'deposit' ? 'active' : '' }}" data-sort="deposit">{{ __('Deposit') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

{{--    <form id="searchForm" method="post">--}}
{{--        @csrf--}}
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button" onclick="document.querySelectorAll('.open-multi-actions:checked').length > 1 ? document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete these licenseSettings?') }}' : document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete this licenseSettings?') }}'">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1" id="multi-delete-title">{{ __('Are you sure you want to delete these licenseSettings?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.licenseSettings.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{{--    </form>--}}

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach($licenseSettingss as $licenseSettings)
        <div class="list-group-item list-group-item-action d-grid align-items-center" onclick="window.location='{{ route('admin.licenseSettings.show', $licenseSettings) }}'">
            <div class="form-check mb-0">
                <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                        value="{{ $licenseSettings->id }}"
                        id="flexCheckDefault_{{$licenseSettings->id}}">
                <label class="form-check-label d-none" for="flexCheckDefault_{{$licenseSettings->id}}"></label>
            </div>
            {{-- <p class="my-0 hide-transp p-0">{{$licenseSettings->sequential_id}}</p> --}}
            <p class="table-name-col"><a href="{{ route('admin.licenseSettings.show', $licenseSettings) }}" style="text-decoration: none;">{{$licenseSettings->name}}</a></p>
            <p class="my-0 text-end" style="color: #969696">${{ number_format($licenseSettings->price ?? '0', 2) }}</p>
            <p class="my-0 text-end" style="color: #969696">${{ number_format($licenseSettings->deposit ?? '0', 2) }}</p>
            <div class="round-button-dropdown">
                <button class="dropbtn">
                    <i class="fa fa-ellipsis-h"></i>
                </button>
                <div class="dropdown-content" id="licenseSettings-dropdown-content">
                    <a href="{{ route('admin.licenseSettings.edit', $licenseSettings) }}">{{ __('Edit') }}</a>
                    <a href="" class="delete text-danger" data-bs-toggle="modal"
                        data-bs-target="#deleteModal{{'Supplier'}}{{$licenseSettings->id}}">{{ __('Delete') }}</a>
                </div>
            </div>
        </div>
        @include('partials.modals.delete', [
            'type' => 'Supplier',
            'id' => $licenseSettings->id,
            'route' => route('admin.licenseSettings.destroy', ['licenseSettings' => $licenseSettings]),
            'title' => $licenseSettings->name,
        ])
        @endforeach
        <div id="paginate paginate-licenseSettings">
            @if($licenseSettingss)
                <div class="">
                    {!! $licenseSettingss->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const licenseSettingsCount = @json($licenseSettingss).total;
    const descriptiveLabel = licenseSettingsCount === 1 ? ' Item' : ' Items';
    $('#licenseSettingsCount').text(licenseSettingsCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.licenseSettings.delete-multiple') }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.delete').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

</script>
