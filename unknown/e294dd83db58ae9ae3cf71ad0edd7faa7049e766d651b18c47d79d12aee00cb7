@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('New Lease') }}</h3>
            <a href="{{ route('studio.lease', $customer) }}" class="back-link">← Back</a>
        </div>
    </div>
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.leases.store', $customer) }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('Lagree Company') }}</h5>

            @include('partials.forms.select', [
                'field_name' => 'company_id',
                'field_label' => null,
                'values' => $companies,
                'field' => 'state',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('company_id'),
                'field_label_class' => 'd-none',
                'include_empty' => true,
            ])

            <h5 class="form-section-title">{{ __('Select Location') }}</h5>

            @include('partials.forms.select', [
                'field_name' => 'studio_id',
                'field_label' => null,
                'values' => $studios,
                'field' => 'state',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('studio_id', null),
                'field_label_class' => 'd-none',
                'include_empty' => true,
                'field_id' => 'lease_id',
            ])

            <h5 class="form-section-title">{{ __('LEASE info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Select',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', null),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field',
            ])

            @include('partials.forms.select', [
                'field_name' => 'machine_id',
                'field_label' => 'MACHINE *',
                'values' => $machines,
                'field' => 'machine',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('machine_id', null),
                'include_empty' => true,
            ])

            <div class="parent_machines_field" @if (old('configuration_id') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'configuration_id',
                    'field_label' => 'CONFIGURATION *',
                    'values' => null,
                    'field' => 'CONFIGURATION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('configuration', null),
                    'include_empty' => true,
                ])
            </div>

            <div class="condition_field" @if (old('condition') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'condition',
                    'field_label' => 'CONDITION *',
                    'values' => ['new' => 'New', 'used' => 'Used', 'restored' => 'Restored'],
                    'field' => 'CONDITION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('condition', null),
                    'include_empty' => true,
                ])
            </div>

            @include('partials.forms.input', [
                'field_name' => 'machine_price',
                'field_label' => 'UNIT PRICE *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'currentCurrency' => $currentCurrency->symbol ?? "$",
                'field_value' => old('machine_price', null),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'monthly_installment',
                'field_label' => 'MONTHLY INSTALLMENT *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'currentCurrency' => $currentCurrency->symbol ?? "$",
                'field_value' => old('monthly_installment', null),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'machine_quantity',
                'field_label' => '# OF MACHINES *',
                'placeholder' => '0',
                'field_type' => 'text',
                'field_right_mark' => 'qty',
                'field_value' => old('machine_quantity', null),
                'input_field_class' => 'integer-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'duration',
                'field_label' => 'DURATION *',
                'field_type' => 'text',
                'placeholder' => '0',
                'field_right_mark' => 'months',
                'field_value' => old('duration', null),
                'input_field_class' => 'integer-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'deposit_amount',
                'field_label' => 'DEPOSIT (PER UNIT) *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'currentCurrency' => $currentCurrency->symbol ?? "$",
                'field_value' => old('deposit_amount', null),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'buy_out',
                'field_label' => 'BUY-OUT AMOUNT *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'currentCurrency' => $currentCurrency->symbol ?? "$",
                'field_value' => old('buy_out', null),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', null),
            ])

            @include('partials.forms.input', [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d'),
            ])

            @include('partials.forms.input', [
                'field_name' => 'status',
                'field_type' => 'hidden',
                'field_value' => 0,
            ])

            <div class="w100 border-top mt-7">
                <p class="lh-1 mt-7 ">Monthly installment: <span class="monthly_installment_val">$0</span></p>
                <p class="lh-1 deposit-wrap">Deposit: <span class="deposit_val">$0</span></p>
            </div>

            <div
                class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{ route('admin.leases.create', $customer) }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>

        </form>
    </div>
@endsection
