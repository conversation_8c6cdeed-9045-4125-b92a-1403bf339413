<div class="main-subtitle border-bottom-0">
    <h5>{{ __('upcoming payments') }}</h5>
</div>

<div id="upcoming-payments-table" class="entity-table bg--loading-black"></div>

<script type="module">
    const searchInput = $('#search');
    const searchIcon = $('.lg-search-ico');
    const sortIcon = $('.sort-icon');
    let searchData = '';
    let orderParam = '';
    let orderType = 'asc';
    let tab = 'upcoming-payments';
    const upcomingPaymentsTable = $('#upcoming-payments-table');

    @php
        $route = route('admin.licenses.upcoming-payments', ['customer' => $customer, 'license' => $license, 'tab' => 'upcoming-payments']);
    @endphp

    function fetchData(url) {
        let perPage = $('.pagination-select').val() ?? 10;
        let selectedValue = $('#statusSelect').val();
        $.ajax({
            url: url,
            data: {
                status: selectedValue,
                search_data: searchData,
                order_param: orderParam,
                order_type: orderType,
                per_page: perPage,
                tab: tab,
            },
            success: function (data) {
                setTimeout(function () {
                    upcomingPaymentsTable.removeClass('bg--loading-black').html(data);
                    // upcomingPaymentsTable.trigger('resultLoaded');
                    setSortIcon();
                    $('.pagination-select').val(perPage);
                }, 300);
            },
            error: function () {
                flasherJS.error('', 'Error occurred while loading data.');
            }
        });
    }


    $('body').on('change', '.pagination-select', function (e) {
        fetchData("{{ $route }}");
    });

    $('body').on('click', '.pagination a', function (e) {
        e.preventDefault();
        const url = $(this).attr('href');
        fetchData(url);
    });


    $('body').on('click', '.sortable-list-header', function () {
        const newOrderParam = $(this).data('sort');
        if (orderParam === newOrderParam) {
            // Toggle sorting direction if the same column is clicked
            orderType = orderType === 'asc' ? 'desc' : 'asc';
        } else {
            // Set new sort column and default direction to ascending
            orderParam = newOrderParam;
            orderType = 'asc';
        }
        fetchData("{{ $route }}");
    });

    function debounce(func, wait, immediate) {
        let timeout;
        return function () {
            let context = this, args = arguments;
            let later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            let callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    function setSortIcon() {
        sortIcon.removeClass('asc desc'); // Remove existing sort classes
        $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(orderType); // Add active sort class
    }


    // Initial load
    fetchData("{{ $route }}");
</script>

