<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            $table->decimal('price')->nullable();
            $table->unsignedBigInteger('parent_machine_id')->nullable();
            $table->foreign('parent_machine_id', 'FK_parent_machine_id_machines')
                ->references('id')
                ->on('machines')
                ->onDelete('cascade');
            $table->integer('parent_show')->nullable();
            $table->decimal('monthly_installment')->nullable();
            $table->decimal('lease_deposit')->nullable();
            $table->integer('lease_duration')->nullable();
            $table->decimal('purchase_deposit')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('machines', function (Blueprint $table) {
            //
        });
    }
};
