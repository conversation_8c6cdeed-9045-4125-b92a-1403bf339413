@extends('layouts.app')
@section('content')
    <div class="page-title ttl-dropdown border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ $lease->studio->name }}</h3>
            </div>
            <a href="{{ route('studio.lease') }}" class="back-link">← Back</a>
        </div>
    </div>
    <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="0">
        <div class="main-subtitle no-border-btm">
            <h5>{{ __('basic info') }}</h5>
        </div>
        <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Studio Name:</div>
                    <div class="">
                        {{ $lease->studio->name }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Licensee:</div>
                    <div class="">
                        {{ $lease->studio->owner_first_name }} {{ $lease->studio->owner_last_name }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Phone #:</div>
                    <div class="">
                        {{ $lease->studio->phone }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{ $lease->studio->email }}
                    </div>
                </div>
            </div>
        </div>

        <div class="main-subtitle no-border-btm">
            <h5>{{ __('Address info') }}</h5>
        </div>
        <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Address:</div>
                    <div class="">
                        {{ $lease->studio->address }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">City:</div>
                    <div class="">
                        {{ $lease->studio->city }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">State:</div>
                    <div class="">
                        {{ $lease->studio->state->name }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Zip Code:</div>
                    <div class="">
                        {{ $lease->studio->zip }}
                    </div>
                </div>
            </div>
        </div>

        <h5 class="fs-14px mb-45 pt-45 form-section-title custom-fw-500 text-uppercase">{{ __('lease info') }}</h5>
        <div>
            <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Machine:</div>
                    <div class="">
                        {{ ucfirst($lease->machine->name) }} x {{ $lease->machine_quantity }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Price:</div>
                    <div class="">
                        <span class="text-success">{{ $currentCurrency->symbol ?? "$" }}{{ number_format($leaseTotal, 2) }}</span>
                        &nbsp;
                        <span>({{ $currentCurrency->symbol ?? "$" }}{{ number_format($leaseMonthly, 2) }}/month)</span>
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Deposit:</div>
                    <div class="">
                        {{ $currentCurrency->symbol ?? "$" }}{{ number_format($leaseDeposit, 2) ?? 'No Deposit' }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Remaining:</div>
                    <div class="">
                        <span
                            class="text-danger">{{ $currentCurrency->symbol ?? "$" }}{{ number_format($leaseRemaining['paymentSum'], 2) }}</span>
                        &nbsp;
                        
                        <span>({{ $leaseRemaining['count'] }}/{{ $lease->duration }} months) </span>
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Start date:</div>
                    <div class="">
                        {{ \Carbon\Carbon::parse($lease->starting_date)->format('m/d/Y') }}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Ends on:</div>
                    <div class="">
                        {{ \Carbon\Carbon::parse($lease->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y') }}
                    </div>
                </div>
            </div>
        </div>
        <div
            class="d-flex justify-content-between align-items-stretch align-items-sm-center pb-10 mt-6 mb-6 flex-column flex-md-row mx-n15">
            <a type="button" href="{{ route('studio.lease.edit', ['customer' => $customer, 'lease' => $lease]) }}">
                <button type="submit" class="btn btn-primary">{{ __('Edit') }}</button>
            </a>
        </div>
    </div>
@endsection
