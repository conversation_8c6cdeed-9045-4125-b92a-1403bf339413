<div class="form-group lh-1 @if ($errors->has(str_replace(['[', ']'], ['.', ''], $field_name))) has-danger @endif {{ $field_class ?? '' }}"
    id="{{ $field_id ?? '' }}">
    <label class="form-label mb-2 mb-sm-3 {{ $field_label_class ?? '' }}" for="{{ $field_name }}">{{ $field_label }}</label>
    <select
        class="form-select @error(str_replace(['[', ']'], ['.', ''], $field_name)) is-invalid @enderror {{ $select_class ?? '' }}"
        name="{{ $field_name }}" id="{{ $field_name . (isset($field) ? '-' . $field : '') }}"
        data-field="{{ $field }}" {{ $multiple ?? '' }} {{ $readonly ?? '' }} {{ $disabled ?? '' }}
        value="{{ $field_value ?? 0 }}"
        @if (isset($placeholder))
        data-placeholder="{{ $placeholder }}"
        @endif
        >

        @if (isset($include_empty))
            <option></option>
        @endif

        @if (isset($group_values))
            @foreach ($group_values as $titles => $products)
                @if ($titles == 'bundle')
                    <optgroup label="{{ $titles }}">
                        @foreach ($products as $product)
                            <option value="{{ $product->id ?? $product }}" @selected($field_value == $product->id)
                                data-type="bundle"
                                data-name="{{ $product->name }}"
                                data-id="{{ $product->id }}"
                                data-value="{{ $field_value }}"
                                data-price="{{ $product->price }}">
                                {{ $product->name }}
                            </option>
                        @endforeach
                    </optgroup>
                @endif
            @endforeach
            @foreach ($group_values as $titles => $products)
                @if ($titles == 'fee')
                    <optgroup label="{{ $titles }}">
                        @foreach ($products as $product)
                            <option value="{{ $product->id ?? $product }}" @selected($field_value == $product->id)
                                data-type="fee"
                                data-name="{{ $product->name }}"
                                data-id="{{ $product->id }}" 
                                data-value="{{ $field_value }}"
                                data-price="{{ $product->price }}">
                                {{ $product->name }}
                            </option>
                        @endforeach
                    </optgroup>
                @endif
            @endforeach
            @foreach ($group_values as $titles => $products)
                @if ($titles == 'products')
                    <optgroup label="{{ $titles }}">
                        @foreach ($products as $product)
                            <option value="{{ $product->id ?? $product }}" @selected($field_value == $product->id)
                                data-type="{{ $product->category }}" 
                                data-name="{{ $product->name }}"
                                data-id="{{ $product->id }}" 
                                data-value="{{ $field_value }}"
                                data-supplier="{{ $product->supplier_id ?? '' }}" data-price="{{ $product->price }}">
                                {{ $product->name }}
                            </option>
                        @endforeach
                    </optgroup>
                @endif
            @endforeach
        @else
            @if (is_array($values))
                @foreach ($values as $key => $value)
                    <option value="{{ $value }}"
                        @if (isset($value->supplier_id)) data-supplier="{{ $field == 'product' ? $key->supplier_id : '' }}" @endif
                        @selected(old(str_replace(['[', ']'], ['.', ''], $field_name), $field_value) == $value)>
                        {{ $field == 'types' ? $value : ucfirst(str_replace('_', ' ', TRIM($key))) }}
                    </option>
                @endforeach
            @else
                @if (isset($values) AND $values !== NULL)
                    @foreach ($values as $value)
                        @if (isset($value->group))
                            <optgroup label="{{ $value->id ?? $value }}">
                                @foreach ($value->group as $group)
                                    <option value="{{ $group->id ?? $group }}"
                                        @if (isset($value->supplier_id)) data-supplier="{{ $group->supplier_id ?? '' }}" @endif
                                        @selected(old(str_replace(['[', ']'], ['.', ''], $field_name), $field_value) == $group->$option_key)>
                                        {{ $group->$option_label }}
                                    </option>
                                @endforeach
                            </optgroup>
                        @else
                            <option value="{{ $field=='locations'? $value->name : ($value->id ?? $value) }}"
                                @if (isset($value->supplier_id)) data-supplier="{{ $value->supplier_id ?? '' }}" @endif
                                @selected(old(str_replace(['[', ']'], ['.', ''], $field_name), $field_value) == $value->$option_key)>
                                {{ $value->$option_label }}
                            </option>
                        @endif
                    @endforeach
                @endif
            @endif
        @endif
    </select>

    @if ($errors->has(str_replace(['[', ']'], ['.', ''], $field_name)))
        <span
            class="text-danger input-error">*{{ $errors->first(str_replace(['[', ']'], ['.', ''], $field_name)) }}</span>
    @endif
</div>
@if (isset($index))
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectElementSelector = `select[name="${'{{ $field_name }}'}"]`;
            $(document).on('change', selectElementSelector, function(e) {
                const productId = document.querySelector(`[name="items[${'{{ $index }}'}][product_id]"]`).value;
                const value = `input[name="product-price[${productId}]"]`;
                const inputField = document.querySelector(`input[name="items[${'{{ $index }}'}][price]"]`);
                if (inputField && value) {
                    inputField.value = document.querySelector(value).value;
                }
            });
        });
    </script>
@endif
