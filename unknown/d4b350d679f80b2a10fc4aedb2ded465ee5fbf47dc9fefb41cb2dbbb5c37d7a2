@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('Edit Fee') }}</h3>
            <a href="{{ route('admin.productfees.index') }}" class="back-link">← Back</a>
        </div>
        <!--<a href="{{ route('admin.productfees.index') }}">
            <button type="button"
                    class="btn btn-ghost-light-bigger back-btn">{{ __('Back') }}</button>
        </a>-->
    </div>
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.productfees.update', $fee) }}" id="studio-location-form">
            @csrf
            @method('PUT')
            <div class="mb-6 mb-sm-6 items-base" style="display:flex; align-items:baseline;">
                <h5 class="form-section-title first-title">{{ __('Fee info') }}</h5>
            </div>
            @include('partials.forms.input', [
               'field_name' => 'name',
               'field_label' => 'NAME *',
               'field_type' => 'text',
               'field_value' => $fee->name,
            ])

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'PRICE *',
               'field_type' => 'text',
               'field_value' => $fee->price,
               'placeholder' => '$'
            ])
            <div class="mt-7 text-xs text-[#f59e0b]">
                <span style="color:red;">Warning: Retail or supplier prices will be automatically updated on all Lagree platforms.</span>
            </div>
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('SAVE') }}</button>
                <a type="button" href="{{route('admin.productfees.edit', $fee)}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'fees'}}{{$fee->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>
@endsection
@include('partials.modals.delete', [
        'type' => 'fees',
        'id' => $fee->id,
        'route' => route('admin.productfees.destroy', ['fee' => $fee]),
        'title' => $fee->name,
])
