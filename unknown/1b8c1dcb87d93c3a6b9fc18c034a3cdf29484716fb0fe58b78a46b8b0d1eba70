<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AdminSettings;

class SetWeekStart
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // if (Auth::check() && Auth::user()->hasRole('super-admin')) {
        if (Auth::check() && Auth::user()->hasAnyRole(explode('|', \App\Helpers\Constants::ROLES))) {
            // $settings = \auth()->user()->adminSettings;
            // $week_start = ($settings->week_start === 'monday') ? 1 : 0;
            //  Asking - Customer own week_start settings.
            $week_start = (AdminSettings::first()->week_start === 'monday') ? 1 : 0;
            view()->share('admin_week_start', $week_start);
        }

        return $next($request);
    }
}
