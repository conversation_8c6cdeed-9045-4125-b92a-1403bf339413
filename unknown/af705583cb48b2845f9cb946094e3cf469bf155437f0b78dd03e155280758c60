<?php

namespace App\Services\Admin\Payment;

use App\Helpers\Constants;
use App\Models\Customer;
use App\Models\Machine;
use App\Models\Payment;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;

class PaymentService implements IPaymentService
{
    public function store(array $data)
    {
        Machine::create([
            'name' => $data['name'],
        ]);
    }

    public function update(array $data, Machine $machine)
    {
        $machine->update($data);
    }

    public function searchLicense(string $searchData, string $orderParam, string $orderType, int $perPage, string $tab, int $status, int $licenseId): LengthAwarePaginator
    {
        $query = Payment::with('license.studio')->where('license_id', $licenseId);

        if ($tab === 'payment-history') {
            $query->where(function ($q) {
                $q->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->orWhere('status', Constants::PAYMENT_STATUS['declined'])
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('status', Constants::PAYMENT_STATUS['not_paid'])
                            ->where('payment_date', '<', Carbon::now());
                    })
                    ->orWhere('payment_date', '<', Carbon::now());
            });
        } else {
            $query->where('payment_date', '>', Carbon::now())->where('status', Constants::PAYMENT_STATUS['not_paid']);
        }

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 4, function ($query) use ($status) {
            $query->where('status', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->whereHas('license.studio', function ($query) use ($searchData) {
                $query->where('name', 'like', '%' . $searchData . '%');
            });
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function searchLease(string $searchData, string $orderParam, string $orderType, int $perPage, string $tab, int $status, int $leaseId): LengthAwarePaginator
    {
        $query = Payment::with('lease.studio')->where('lease_id', $leaseId);

        if ($tab === 'payment-history') {
            $query->where(function ($q) {
                $q->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->orWhere('status', Constants::PAYMENT_STATUS['declined'])
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('status', Constants::PAYMENT_STATUS['not_paid'])
                            ->where('payment_date', '<', Carbon::now());
                    })
                    ->orWhere('payment_date', '<', Carbon::now());
            });
        } else {
            $query->where('payment_date', '>', Carbon::now())->where('status', Constants::PAYMENT_STATUS['not_paid']);
        }

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 4, function ($query) use ($status) {
            $query->where('status', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->whereHas('lease.studio', function ($query) use ($searchData) {
                $query->where('name', 'like', '%' . $searchData . '%');
            });
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }
}
