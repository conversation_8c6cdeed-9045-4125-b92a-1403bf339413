@import 'variables'
@import 'mixins'
@import 'fonts'
@import 'bootstrap_overwrites/bootstrap'
@import 'dashboard'
@import 'input'
@import 'select2'
@import 'auth'
@import 'list-grid-styles'
@import "@flasher/flasher/dist/flasher.min.css"

// Typography fix
body
    letter-spacing: 0.6px

// Header
.hamburger
    background: transparent
    border: none
    padding: 0

.account-ctrl
    border: none
    border-radius: 100%
    background-color: $primary
    color: $white
    width: 30px
    height: 30px

.acc-no-active
    display: block
    height: 10px
    width: 10px
    background-color: $danger
    border-radius: 50%
    position: absolute
    z-index: 5
    top: 7px
    right: -3px

.acc-notification
    border: none
    background-color: $white
    height: 30px
    position: relative
    z-index: 1

// END Header

// Tables
.table-action
    display: flex
    align-items: center
    justify-content: center
    // border: 1px solid $light
    padding: 0
    border-radius: 100%
    width: 30px
    height: 30px
    background: transparent
    text-decoration: none

// END Tables

// Modals
.modal.welcome-modal
    --bs-modal-width: 600px

.welcome-modal-content
    width: 600px
    height: 100vh
    +breakpoint(md)
        height: auto
.welcome-img
    max-width: 255px
.modal-body
    h4
        font-weight: 600

    img
        position: relative
        z-index: 10

    .circle-background
        border-radius: 50%
        width: 200px
        height: 200px
        position: absolute
        left: 0
        right: 0
        top: 80px
        z-index: 1
        +breakpoint(md)
            top: -10px


.modal-dots
    height: 10px !important
    width: 10px
    border-radius: 10px


// END Modals

// Subtle gray button
.btn-ghost-light
    font-weight: 500
    border: 1px solid $light
    color: $info
    padding: 10px 15px
    line-height: 1
    display: inline-block
    letter-spacing: .6px
    text-transform: capitalize
    +sc-transition
    &:hover, &:active, &:focus
        color: $primary
        background-color: $white

.btn-ghost-light-bigger
  font-weight: 500
  border: 1px solid $border
  color: $info
  padding: 14px 20px
  line-height: 1
  display: inline-block
  letter-spacing: 1.2px
  text-transform: uppercase
  +sc-transition
  &:hover, &:active, &:focus
    color: $primary
    background-color: $white

// Button with a small text below
.btn-with-label
    padding-top: .55rem
    padding-bottom: .55rem
    line-height: 1
    .sub
        font-weight: normal
        line-height: 1
        margin-top: 2px
        font-size: 8px

// Narrow container on client checkout page
.container-narrow
    max-width: 840px

// Cart item mini label
.cart-item-num
    width: 15px
    height: 15px
    line-height: 16px
    top: -2px
    right: -2px

.cart-item-num-big
    width: 20px
    height: 20px

// Display 'none' without !important
.d-none-soft
    display: none

// Buttons that change the date in query parameters
.go-to-date
    cursor: pointer
//Calendar embed page
.img-embed-page
    width: 100%
    max-width: 450px

// Studio Billing
.studio-package-ico
    width: 70px
    height: 70px

.cursor-pointer
    cursor: pointer

%sc-scroll-bar
    &::-webkit-scrollbar
        width: 3px
    &::-webkit-scrollbar-track
        +sc-box-shadow(inset -1px 0 $secondary)
    &::-webkit-scrollbar-thumb
        background-color: $primary

.f-10 
    font-size: 10px !important

.f-12 
    font-size: 12px !important

.f-14 
    font-size: 14px !important

.f-16 
    font-size: 16px !important

.f-18 
    font-size: 18px !important

.f-20 
    font-size: 20px !important

.f-24 
    font-size: 24px !important

.f-30 
    font-size: 30px !important

.f-36 
    font-size: 36px !important

.f-40 
    font-size: 40px !important

.f-48 
    font-size: 48px !important

.f-64 
    font-size: 64px !important

.light
    font-weight: 300 !important

.normal
    font-weight: 400 !important

.medium
    font-weight: 500 !important
    
.semibold
    font-weight: 600 !important

.bold
    font-weight: 700 !important

h1, h2, h3, h4, h5, h6 
    font-weight: 600 !important

input, select, textarea
    font-family: $font-family-sans-serif
