<?php

namespace App\Services\Admin\Order;

use App\Enum\Order\Status;
use App\Helpers\OrderHelper;
use App\Mail\SendInvoiceReminder;
use App\Mail\SendOrderToSupplier;
use App\Models\Order;
use App\Models\OrderItem;
use App\Services\Admin\Order\IOrderService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Mail;

class OrderService implements IOrderService
{
    public function store(array $data)
    {
        $order = Order::create($data);
        $order->items()->createMany($data['items']);
    }

    public function update(array $data, Order $order)
    {
        $order->update($data);
        $ids = [];
        foreach ($data['items'] as $item) {
            if (isset($item['id'])) {
                $ids[] = $item['id'];
                unset($item['product_name']);
                $order->items()->where('id', $item['id'])->update($item);
            } else {
                unset($item['product_name']);
                $newItem = $order->items()->create($item);
                $ids[] = $newItem->id;
            }
        }
        OrderItem::where('order_id', $order->id)->whereNotIn('id', $ids)->delete();
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, int|null $supplierId): LengthAwarePaginator
    {
        $query = Order::with('company', 'supplier');

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($supplierId, function ($query) use ($status, $supplierId) {
            $query->where('supplier_id', $supplierId);
        });

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('status', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('id', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('created_at', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('updated_at', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('sent_at', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhereHas('company', function ($q) use ($searchData) {
                      $q->where('name', 'LIKE', '%' . $searchData . '%');
                  })
                  ->orWhereHas('supplier', function ($q) use ($searchData) {
                      $q->where('name', 'LIKE', '%' . $searchData . '%');
                  });
            });
        });

       $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function trimNumber(string $searchData): string
    {
        return ltrim($searchData, '0');
    }

    public function delete(Order $order)
    {
        $order->items()->delete();
        $order->delete();
    }

    public function deleteMultiple(array $ids)
    {
        foreach ($ids as $id) {
            $order = Order::find($id);
            $this->delete($order);
        }
    }

    public function sendToSupplier(Order $order)
    {
        $pdf = OrderHelper::generatePDF($order);
        Mail::to($order->supplier)->send(new SendOrderToSupplier($pdf, $order));
        if($order->supplier->email2 !== null) {
            Mail::to($order->supplier->email2)->send(new SendOrderToSupplier($pdf, $order));            
        }
        $order->update([
            'sent_at' => now(),
            'status' => Status::SENT
        ]);
    }
}
