<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('purchase', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->foreign('company_id')->references('id')->on('companies');
            $table->unsignedBigInteger('studio_id');
            $table->foreign('studio_id')->references('id')->on('studios');
            $table->unsignedBigInteger('initial_currency_id')->nullable();
            $table->foreign('initial_currency_id')->references('id')->on('currencies');
            $table->unsignedBigInteger('customer_id');
            $table->foreign('customer_id')->references('id')->on('customers');
            $table->unsignedBigInteger('machine_id');
            $table->foreign('machine_id')->references('id')->on('machines');
            $table->integer('machine_price');
            $table->integer('machine_quantity');            
            $table->unsignedBigInteger('configuration_id')->nullable();
            $table->foreign('configuration_id', 'FK_purchase_configuration_id_machines')
                ->references('id')
                ->on('machines')
                ->onDelete('cascade');
            $table->integer('duration')->nullable();
            $table->date('starting_date');
            $table->integer('deposit_amount')->nullable();
            $table->date('deposit_date')->nullable();
            $table->boolean('is_active')->default(true)->nullable();
            $table->integer('status')->nullable();
            $table->string('condition')->nullable();
            $table->decimal('buy_out')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('purchase');
    }
};
