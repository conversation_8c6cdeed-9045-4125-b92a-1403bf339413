<?php

namespace App\Providers;

use App\Models\Lease;
use App\Models\License;
use App\Models\Purchase;
use App\Observers\LeaseObserver;
use App\Observers\LicenseObserver;
use App\Observers\PurchaseObserver;
use App\Services\Admin\Agreement\AgreementService;
use App\Services\Admin\Agreement\IAgreementService;
use App\Services\Admin\CustomerAgreement\CustomerAgreementService;
use App\Services\Admin\CustomerAgreement\ICustomerAgreementService;
use App\Services\Admin\Company\CompanyService;
use App\Services\Admin\Company\ICompanyService;
use App\Services\Admin\Customer\CustomerService;
use App\Services\Admin\Customer\ICustomerService;
use App\Services\Admin\Dashboard\DashboardService;
use App\Services\Admin\Dashboard\IDashboardService;
use App\Services\Admin\EmailTemplates\EmailTemplateService;
use App\Services\Admin\EmailTemplates\IEmailTemplateService;
use App\Services\Admin\Invoice\IInvoiceService;
use App\Services\Admin\Invoice\InvoiceService;
use App\Services\Admin\InvoiceProduct\IInvoiceProductService;
use App\Services\Admin\InvoiceProduct\InvoiceProductService;
use App\Services\Admin\InvoiceTemplate\IInvoiceTemplateService;
use App\Services\Admin\InvoiceTemplate\InvoiceTemplateService;
use App\Services\Admin\Lease\ILeaseService;
use App\Services\Admin\Lease\LeaseService;
use App\Services\Admin\License\ILicenseService;
use App\Services\Admin\License\LicenseService;
use App\Services\Admin\Machine\IMachineService;
use App\Services\Admin\Machine\MachineService;
use App\Services\Admin\Products\IProductsService;
use App\Services\Admin\Products\ProductsService;
use App\Services\Admin\Bundle\IBundleService;
use App\Services\Admin\Bundle\BundleService;
use App\Services\Admin\Notification\INotificationService;
use App\Services\Admin\Notification\NotificationService;
use App\Services\Admin\Order\IOrderService;
use App\Services\Admin\Order\OrderService;
use App\Services\Admin\Payment\IPaymentService;
use App\Services\Admin\Payment\PaymentService;
use App\Services\Admin\PaymentHistory\IPaymentHistoryService;
use App\Services\Admin\PaymentHistory\PaymentHistoryService;
use App\Services\Admin\Purchase\IPurchaseService;
use App\Services\Admin\Purchase\PurchaseService;
use App\Services\Admin\Woocommerce\IWoocommerceService;
use App\Services\Admin\Woocommerce\WoocommerceService;
use App\Services\Admin\Supplier\ISupplierService;
use App\Services\Admin\Supplier\SupplierService;
use App\Services\Admin\LicenseSettings\ILicenseSettingsService;
use App\Services\Admin\LicenseSettings\LicenseSettingsService;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(IWoocommerceService::class, function (): WoocommerceService {
            return new WoocommerceService();
        });

        $this->app->singleton(ISupplierService::class, function (): SupplierService {
            return new SupplierService();
        });

        $this->app->singleton(ILicenseSettingsService::class, function (): LicenseSettingsService {
            return new LicenseSettingsService();
        });

        $this->app->singleton(ICustomerService::class, function (): CustomerService {
            return new CustomerService();
        });

        $this->app->singleton(IMachineService::class, function (): MachineService {
            return new MachineService();
        });

        $this->app->singleton(IProductsService::class, function (): ProductsService {
            return new ProductsService();
        });

        $this->app->singleton(IBundleService::class, function (): BundleService {
            return new BundleService();
        });

        $this->app->singleton(ICompanyService::class, function (): CompanyService {
            return new CompanyService();
        });

        $this->app->singleton(ILicenseService::class, function (): LicenseService {
            return new LicenseService();
        });

        $this->app->singleton(IPaymentService::class, function (): PaymentService {
            return new PaymentService();
        });

        $this->app->singleton(ILeaseService::class, function (): LeaseService {
            return new LeaseService();
        });

        $this->app->singleton(IPurchaseService::class, function (): PurchaseService {
            return new PurchaseService();
        });

        $this->app->singleton(IInvoiceService::class, function (): InvoiceService {
            return new InvoiceService();
        });

        $this->app->singleton(IPaymentHistoryService::class, function (): PaymentHistoryService {
            return new PaymentHistoryService();
        });

        $this->app->singleton(IOrderService::class, function (): OrderService {
            return new OrderService();
        });

        $this->app->singleton(IDashboardService::class, function (): DashboardService {
            return new DashboardService();
        });

        $this->app->singleton(IAgreementService::class, function (): AgreementService {
            return new AgreementService();
        });

        $this->app->singleton(INotificationService::class, function (): NotificationService {
            return new NotificationService();
        });

        $this->app->singleton(IEmailTemplateService::class, function (): EmailTemplateService {
            return new EmailTemplateService();
        });

        $this->app->singleton(IInvoiceProductService::class, function (): InvoiceProductService {
            return new InvoiceProductService();
        });
        $this->app->singleton(IInvoiceTemplateService::class, function (): InvoiceTemplateService {
            return new InvoiceTemplateService();
        });
        $this->app->singleton(ICustomerAgreementService::class, function (): CustomerAgreementService {
            return new CustomerAgreementService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        License::observe(LicenseObserver::class);
        Lease::observe(LeaseObserver::class);
        Purchase::observe(PurchaseObserver::class);
        
        View::composer('*', function ($view) {
            $user = auth()->user();
            if ($user) {
                $stickyNotifications = $user->unreadNotifications;
                $view->with('stickyNotification', $stickyNotifications);
            }
        });
    }
}
