<?php

namespace App\Repositories\Infrastructure;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;

interface BaseRepository
{
    /**
     * Find a resource by id.
     *
     * @param string $id
     * @throws ModelNotFoundException
     */
    public function findOneById(string $id);

    /**
     * Find a resource by key value criteria.
     *
     * @param array $criteria
     * @param array $relationships
     * @throws ModelNotFoundException
     */
    public function findOneBy(
        array $criteria = [],
        array $relationships = []
    );

    /**
     * Search All resources by query builder.
     *
     * @param string $orderParam
     * @param string $orderType
     * @param array $criteria
     * @param array $relationships
     */
    public function findByFilters(
        string $orderParam = 'created_at',
        string $orderType = 'desc',
        array $criteria = [],
        array $relationships = []
    );

    /**
     * Search All resources by query builder paginator.
     *
     * @param string $orderParam
     * @param string $orderType
     * @param array $criteria
     * @param array $relationships
     */
    public function findByFiltersPaginate(
        string $orderParam = 'created_at',
        string $orderType = 'desc',
        array $criteria = [],
        array $relationships = []
    );

    /**
     * @param string $field
     * @param array $data
     * @return mixed
     */
    public function findIn(string $field, array $data);

    /**
     * @param string $relationship
     * @param array $data
     * @param array $whereCriteria
     * @param string $method
     */
    public function findByHasRelationship(
        string $relationship,
        array $data,
        array $whereCriteria = [],
        string $method = 'get'
    );


    /**
     * @param string $relationship
     * @param array $data
     * @param array $orWhereData
     * @param array $whereCriteria
     */
    public function findByHasOrWhereRelationship(
        string $relationship,
        array $data,
        array $orWhereData,
        array $whereCriteria = []
    );

    /**
     * @param array $criterias
     */
    public function find(array $criterias);

    /**
     * @param array $criteria
     * @param array $data
     */
    public function findOrCreate(array $criteria, array $data = []);

    /**
     * Save a resource.
     *
     * @param array $data
     */
    public function store(array $data);

    /**
     * Insert a resource.
     *
     * @param array $data
     */
    public function insert(array $data);

    /**
     * Update a resource.
     *
     * @param Model $model
     * @param array $data
     */
    public function update(Model $model, array $data);

    /**
     * Update a resource.
     *
     * @param array $criteria
     * @param array $data
     * @param bool $trash
     */
    public function updateWhere(array $criteria, array $data, bool $trash = false);

    /**
     * @param string $relationship
     * @param array $whereHasCriteria
     * @param array $data
     * @param array|null $whereCriteria
     * @param bool $trash
     */
    public function updateWhereHas(
        string $relationship,
        array $whereHasCriteria,
        array $data,
        ?array $whereCriteria = [],
        bool $trash = false
    );

    /**
     * @param string $relationship
     * @param array $whereHasCriteria
     * @param array $data
     * @param array|null $whereCriteria
     * @param array|null $orWhereCriteria
     * @param bool $trash
     */
    public function updateWhereNotIn(
        string $relationship,
        array $whereHasCriteria,
        array $data,
        string $columnName,
        ?array $whereCriteria = [],
        bool $trash = false
    );

    /**
     * @param string $field
     * @param array $criteria
     * @param array $data
     * @param array $whereCriteria
     * @return mixed
     */
    public function updateWhereIn(string $field, array $criteria, array $data, array $whereCriteria = []);

    /**
     * Create or update a record matching the attributes, and fill it with values.
     *
     * @param array $attributes
     * @param array $data
     */
    public function updateOrCreate(array $attributes, array $data = []);

    /**
     * Attach relationship data to model
     *
     * @param Model $model
     * @param string $relationship
     * @param array $data
     */
    public function attach(Model $model, string $relationship, array $data);

    /**
     * Detach relationship data to model
     *
     * @param Model $model
     * @param string $relationship
     * @param array $data
     */
    public function detach(Model $model, string $relationship, array $data);

    /**
     * Sync relationship data to model
     *
     * @param Model $model
     * @param string $relationship
     * @param array $data
     */
    public function sync(Model $model, string $relationship, array $data);

    /**
     * @param Model $model
     * @param string $relationship
     * @param array $data
     * @param string $pivotField
     * @param string $pivotData
     */
    public function syncWhere(Model $model, string $relationship, array $data, string $pivotField, string $pivotData);

    /**
     * Sync without detaching relationship data to model
     *
     * @param Model $model
     * @param string $relationship
     * @param array $data
     */
    public function syncWithoutDetaching(Model $model, string $relationship, array $data);

    /**
     * @param Model $model
     * @param string $relationship
     * @param Model $associate
     */
    public function associate(Model $model, string $relationship, Model $associate);

    /**
     * @param Model $model
     * @param string $relationship
     * @param Model $associate
     */
    public function dissociate(Model $model, string $relationship, Model $associate);

    /**
     * Delete a resource.
     *
     * @param Model $model
     */
    public function delete(Model $model);

    /**
     * Delete a resource.
     *
     * @param array $ids
     * @param null|string $field
     */
    public function deleteAll(array $ids, string $field = 'id');

    /**
     * Delete a resources.
     *
     * @param string $field
     * @param $value
     */
    public function deleteWhere(string $field, $value);

    /**
     * @param array $criteria
     * @return mixed
     */
    public function deleteWhereCriteria(array $criteria);

    /**
     * @param string $relationship
     * @param array $whereHasCriteria
     * @param array|null $whereCriteria
     */
    public function deleteWhereHas(
        string $relationship,
        array $whereHasCriteria,
        ?array $whereCriteria = []
    );

    /**
     * Delete a resource.
     *
     * @param int $id
     */
    public function deleteOneById(int $id);

    /**
     * Get all with or without trashed.
     *
     * @param array $relationships
     * @param bool $trashed
     */
    public function getAll(array $relationships = [], bool $trashed = false);

    /**
     * Force delete
     *
     * @param Model $model
     */
    public function forceDelete(Model $model);

    /**
     * Force delete many by ids
     *
     * @param array $ids
     * @return mixed
     */
    public function forceDeleteMany(array $ids);

    /**
     * @param string $column
     * @param string $value
     */
    public function getResultByLikeFilter(string $column, string $value);

    /**
     * Get N last data with or without trashed.
     * @param int $count
     * @param array $relationships
     * @param bool $trashed
     */
    public function getNumberOfdData(int $count, array $relationships = [], bool $trashed = false);

    /**
     * Reorder model records
     *
     * @param array $data
     */
    public function reorderSortable(array $data);

    /**
     * Store resource and increment sortable field
     *
     * @param array $data
     * @param bool $asc
     */
    public function storeWithSortable(array $data, bool $asc = true);
}
