@extends('layouts.login')

@section('content')
    <div class="auth-card login-wrap">
        <div class="login-above">
            <div class="mb-7 align-items-center d-flex justify-content-between">
                <h1 class="fs-4 mb-0 text-uppercase fw-semibold align-items-center d-flex">
                    {{ __('Create an account') }}
                </h1>
                <a href="/new-account" style="display: flex; align-items: center; text-decoration: none; color: #969696;">
                    ← &nbsp;<span>Go Back</span>
                </a>
            </div>
            {{-- <form method="POST" action="{{ route('login') }}" class="my-auto"> --}}
            <form method="POST" action="{{ route('signup.store') }}" class="my-auto">
                @csrf
                <div class="input-placeholder border-bottom border-light">
                    <div class="form-group discount_type-field">
                        <select name="account-type" class="form-control dynamic-select" id="account-type">
                            <option value="studio" {{ $selection == 'studio' ? 'selected' : '' }}>Studio</option>
                            <option value="customer" {{ $selection == 'customer' ? 'selected' : '' }}>B2C Customer</option>
                            <option value="trainer" {{ $selection == 'trainer' ? 'selected' : '' }}>Trainer</option>
                            <option value="master-trainer" {{ $selection == 'master-trainer' ? 'selected' : '' }}>Master
                                Trainer</option>
                        </select>
                    </div>
                </div>
                {{-- @if ($selection == 'studio') --}}
                <div class="input-placeholder mt-5" id="input-placeholder-studio-name">
                    <input id="studio-name" type="text"
                        class="line-style mb-3  @error('studio-name') is-invalid @enderror" name="studio-name"
                        value="{{ old('studio-name') }}" required autofocus placeholder="Studio name">
                    @error('studio-name')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                </div>
                {{-- @else --}}
                <div class="input-placeholder mt-5" id="input-placeholder-user-email">
                    <input id="user-email" type="email" class="line-style mb-3 @error('user-email') is-invalid @enderror"
                        name="user-email" value="{{ old('user-email') }}" required autofocus placeholder="Email address">
                    @error('user-email')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                    <div class="w-full d-flex justify-content-center">
                        <span id="email-alert" class="mb-3" style="color: red">Please use an email address registered at
                            Lagree Academy.</span>
                    </div>
                </div>
                {{-- @endif --}}
                <div class="input-placeholder">
                    <input id="first-name" type="text" class="line-style mb-3 @error('first-name') is-invalid @enderror"
                        name="first-name" value="{{ old('first-name') }}" required autofocus
                        placeholder="{{ $selection == 'studio' ? 'License first name' : 'First name' }}">
                    {{-- @error('first-name') --}}
                    <span class="invalid-feedback"
                        role="alert">{{ 'Please use an email address registered at lagree shop' }}</span>
                    {{-- @enderror --}}
                </div>
                <div class="input-placeholder" id="input-placeholder-last-name">
                    <input id="last-name" type="text" class="line-style mb-3  @error('last-name') is-invalid @enderror"
                        name="last-name" value="{{ old('last-name') }}" required autofocus
                        placeholder="{{ $selection == 'studio' ? 'License last name' : 'Last name' }}">
                    @error('last-name')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                </div>

                <div class="input-placeholder border-bottom border-light" id="studio-user-email">
                    <input id="user-email" type="email"
                        class="line-style mb-5  @error('user-email') mb-3 is-invalid @enderror" name="user-email"
                        value="{{ old('user-email') }}" required autofocus placeholder="Email address">
                    @error('user-email')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                </div>
                <div class="position-relative mt-5">
                    <div class="input-placeholder">
                        <input id="user-password" type="password"
                            class="line-style @error('user-password') is-invalid @enderror" name="user-password" required
                            placeholder="Password">
                        @error('user-password')
                            <span class="invalid-feedback" role="alert">{{ $message }}</span>
                        @enderror
                        <div class="password-eye">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                <path id="Path_5662" data-name="Path 5662"
                                    d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z"
                                    transform="translate(-1 -4.5)" fill="#DFDFDF" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="position-relative">
                    <div class="input-placeholder">
                        <input id="user-password_confirmation" type="password"
                            class="line-style @error('user-password_confirmation') is-invalid @enderror"
                            name="user-password_confirmation" required placeholder="Confirm password">
                        @error('user-password_confirmation')
                            <span class="invalid-feedback" role="alert">{{ $message }}</span>
                        @enderror
                        <div class="password-eye">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227"
                                viewBox="0 0 15 10.227">
                                <path id="Path_5662" data-name="Path 5662"
                                    d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z"
                                    transform="translate(-1 -4.5)" fill="#DFDFDF" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div
                    class="login-submit d-flex flex-column flex-md-row justify-content-between align-items-stretch align-items-md-center gap-4">
                    <button type="submit" class="btn btn-primary ms-0 ls-1.2px fw-normal">{{ __('Submit') }}</button>
                    <span>
                        If you need help, <a href="/contact">contact us</a>
                    </span>
                </div>
            </form>
        </div>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                swapValue("{{ $selection }}");
                const accountType = document.getElementById('account-type');
                $('#account-type').select2({
                    placeholder: "Select Option",
                    minimumResultsForSearch: Infinity
                });
                $('#account-type').on('change', function() {
                    let selectedValue = $(this).val();
                    swapValue(selectedValue);
                });

                function swapValue(selectedValue) {
                    if (selectedValue == "studio") {
                        $("#input-placeholder-studio-name").show();
                        $("#studio-user-email").show().find('input').prop('disabled', false);
                        $("#input-placeholder-user-email").hide().find('input').prop('disabled', true);

                        $("#first-name").attr("placeholder", "License first name");
                        $("#last-name").attr("placeholder", "License last name");

                    } else {
                        $("#input-placeholder-studio-name").hide();
                        $("#studio-user-email").hide().find('input').prop('disabled', true);
                        $("#input-placeholder-user-email").show().find('input').prop('disabled', false);

                        $("#first-name").attr("placeholder", "First name");
                        $("#last-name").attr("placeholder", "Last name");

                        let alertLabel = "Please use an email address registered at ";
                        let shuffix = selectedValue == "customer" ? "Lagree Shop" : "Lagree Academy";
                        $("#email-alert").text(alertLabel + shuffix);
                    }
                }
            });
        </script>
    @endsection
