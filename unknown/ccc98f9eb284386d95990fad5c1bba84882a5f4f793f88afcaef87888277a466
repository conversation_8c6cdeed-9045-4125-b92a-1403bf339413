@if(count($machines) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id">
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="name" class="sortable-list-header {{ $machines[0]->orderParam == 'name' ? 'active' : '' }}" data-sort="name">
            {{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="price" class="sortable-list-header {{ $machines[0]->orderParam == 'price' ? 'active' : '' }}" data-sort="price" style="text-decoration: none;color: #969696;">
            {{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="configuration" class="" style="text-decoration: none;color: #969696;">
            {{-- {{ __('Configuration') }} --}}
        </div>
        <div class="round-button-dropdown"></div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value="" id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these machines?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.machines.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">
            @foreach($machines as $machine)
                <div class="list-group-item list-group-item-action d-grid align-items-center py-4 px-4">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                               value="{{ $machine->id }}"
                               id="flexCheckDefault_{{$machine->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$machine->id}}"></label>
                    </div>
                    <p class="my-0 table-name-col">
                        <a href="{{ route('admin.machines.edit', $machine) }}" style="text-decoration: none;color: #000;">
                            {{$machine->name}} 
                        </a>
                    </p>
                    <p class="my-0 text-secondary" style="text-decoration: none;color: #969696;">${{ number_format($machine->price, 2) }}</p>
                    <p class="my-0 text-secondary" style="text-decoration: none;color: #969696;">{{ $machine->description }}</p>
                    <p class="my-0 pe-0 text-end">
                        @if ($machine->parent_machine_id != NULL)
                            <span class="d-inline-block rounded-pill lh-1 f-10 fw-medium bg-light" style="color: #777 !important;">Configuration</span>                            
                        @endif
                    </p>
                    <div class="round-button-dropdown">
                        <button class="dropbtn" type="button">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="customers-dropdown-content">
                            <a href="{{ route('admin.machines.edit', $machine) }}">{{ __('Edit') }}</a>
                            <a href="" class="terminate" data-bs-toggle="modal"
                               data-bs-target="#deleteModal{{'Machines'}}{{$machine->id}}">{{ __('Delete') }}</a>
                        </div>
                    </div>
                </div>
                @include('partials.modals.delete', [
                    'type' => 'Machine',
                    'id' => $machine->id,
                    'route' => route('admin.machines.destroy', ['machine' => $machine]),
                    'title' => $machine->name,
                ])
  
            @endforeach
            <div id="paginate paginate-machines">
                @if($machines)
                    <div class="">
                        {!! $machines->links() !!}
                    </div>
                @endif
            </div>
        </div>
    </form>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const machinesCount = @json($machines).total;
    const descriptiveLabel = machinesCount === 1 ? ' Item' : ' Items';
    $('#machinesCount').text(machinesCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    
    $('.dropbtn').on('click', function (event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('.dropdown-content').on('click', function(event) {
        event.stopPropagation();
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        tableWrapper.find('.list-group input[type="checkbox"]').change(function(){
            let allChecked = true;
            let allNonChecked = false;
            tableWrapper.find('.list-group input[type="checkbox"]').each(function () {
                allChecked &= $(this).prop('checked');
                allNonChecked ||= $(this).prop('checked');
            })
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (allNonChecked === false) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });
</script>
