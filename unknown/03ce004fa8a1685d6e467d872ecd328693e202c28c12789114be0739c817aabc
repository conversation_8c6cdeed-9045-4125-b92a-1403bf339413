@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center py-5 py-md-9 border-bottom border-light">
                <h3 class="page-title mb-0 text-uppercase">{{ __('Agreement list') }}</h3>
                <a class="btn btn-primary mt-4" href="{{ route('get-templates') }}">
                   {{ __('Create agreement') }}
                </a>
            </div>
            <div class="py-8">
                <table class="table w-100">
                    <thead>
                    <tr>
                        <th scope="col">Customer Name</th>
                        <th scope="col">Agreement signed date</th>
                        <th scope="col">Show Agreement</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($agreements as $agreement)
                        <tr>
                            <td>{{$agreement->customer->name ?? '-'}}</td>
                            <td>{{$agreement->updated_at}}</td>
                            <td><a href="{{ route('show-pdf', ['filename' => $agreement->agreementTemplate->name]) }}" target="_blank">View PDF</a></td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
