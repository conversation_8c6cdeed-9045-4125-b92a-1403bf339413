@extends('layouts.login')

@section('content')
    <div class="d-flex align-items-center justify-content-center min-vh-100 py-3">
        <div class="auth-card px-3 px-sm-6 px-md-10 py-5 py-md-9">
            <a class="auth-close" href="{{ url('/') }}">
                <button type="button" class="btn-close" aria-label="Close"></button>
            </a>

            <h1 class="fs-4 mb-7 text-uppercase">Lagree ERP</h1>

            <form method="POST" action="{{ route('register') }}">
                @csrf

                <input id="first_name" type="text"
                       class="@error('first_name') is-invalid @enderror" name="first_name"
                       value="{{ old('first_name') }}" required autocomplete="first_name" autofocus
                       placeholder="First name">
                @error('first_name')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror

                <input id="last_name" type="text"
                       class="@error('last_name') is-invalid @enderror" name="last_name"
                       value="{{ old('last_name') }}" required autocomplete="last_name" autofocus
                       placeholder="First name">
                @error('last_name')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror

                <input id="email" type="email"
                       class="@error('email') is-invalid @enderror"
                       name="email" value="{{ old('email') }}" required autocomplete="email"
                       placeholder="Email">
                @error('email')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror

                <div class="position-relative">
                    <input id="password" type="password"
                           class="@error('password') is-invalid @enderror"
                           name="password" required autocomplete="new-password" placeholder="Password">
                    <i class="fa fa-eye bi-eye-slash" onclick="togglePasswordEye()"></i>
                </div>
                @error('password')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror

                <input id="password-confirm" type="password"
                       name="password_confirmation" required autocomplete="new-password"
                       placeholder="Password confirmation">

                <button type="submit" class="btn btn-primary mt-4">
                    {{ __('Register') }}
                </button>
            </form>
        </div>
    </div>
@endsection
