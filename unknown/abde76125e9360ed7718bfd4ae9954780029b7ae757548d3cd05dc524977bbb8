<?php

namespace App\Http\Requests\Admin\Bundle;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class UpdateBundleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            'title' => 'required',
            'items' => 'required|array',
            'items.*' => 'required_with:bundle_item|array',
            'items.*.id' => 'nullable|integer|exists:bundle_item,id',
            'items.*.product_id' => 'required|integer|exists:products,id',
            'items.*.bundle_id' => 'required|integer|exists:bundle,id',
            'items.*.quantity' => 'required|integer',
            'items.*.product_name' => 'string',
        ];
    }

        protected function prepareForValidation()
    {
        // Clean all items' price and discount fields
        if ($this->has('items')) {
            $items = $this->input('items');
            foreach ($items as $index => $item) {
                if (!isset($item['product_id']) OR !isset($item['bundle_id'])) {
                    unset($items[$index]);
                }
                // if (isset($item['custom_product']) AND $item['custom_product'] == 1) {
                //     if (isset($item['product_id']) AND $item['product_id'] > 0) {
                //         $item['price'] = $this->cleanCurrency($item['price']);
                //         $edit_custom_product = [
                //             'name' => $item['product_name'],
                //             'price' => $item['price'],
                //             'updated_at' => date('Y-m-d H:i:s'),
                //         ];

                //         DB::table('products')->where('id', $item['product_id'])->update($edit_custom_product);
                //         // dd($items); // Dumps and stops execution
                //     }else{
                //         $item['price'] = $this->cleanCurrency($item['price']);
                //         $new_custom_product = [
                //             'name' => $item['product_name'],
                //             'price' => $item['price'],
                //             'category' => 'product',
                //             'custom_product' => 1,
                //             'created_at' => date('Y-m-d H:i:s'),
                //             'updated_at' => date('Y-m-d H:i:s'),
                //         ];
                //         // dd($new_custom_product); // Dumps and stops execution

                //         DB::table('products')->insert($new_custom_product);
                //         $items[$index]['product_id'] = DB::getPdo()->lastInsertId();
                //         // dd($items); // Dumps and stops execution
                //     }
                // }
            }
            // $this->merge([
            //     'items' => $items
            // ]);
        }
    }

    // Helper function to clean currency values by removing non-numeric characters
    private function cleanCurrency($value)
    {
        if ($value === '') {
            return null;
        }
        // Remove everything except numbers, dots, and negative sign
        return preg_replace('/[^0-9.-]+/', '', $value);
    }

    protected function failedValidation(Validator $validator)
    {
        // dd($validator);
        if ($validator->errors()->has('items')) {
            toastr()->addError('The items field is required.');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
