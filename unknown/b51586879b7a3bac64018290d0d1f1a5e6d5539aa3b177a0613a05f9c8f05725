@extends('layouts.login')
@section('content')
    <div class="auth-card px-3 px-sm-6 px-md-10 py-9 pt-md-9 pb-md-9 d-flex align-items-center d-md-block">
        <a class="btn btn-link text-decoration-none text-info d-block close-btn-mobile" href="{{ url('login') }}">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
                <path id="Union_1" data-name="Union 1"
                      d="M19482,14626.723l-8.277,8.277-1.723-1.723,8.277-8.277-8.277-8.277,1.723-1.723,8.277,8.277,8.277-8.277,1.723,1.723-8.277,8.277,8.277,8.277-1.723,1.723Z"
                      transform="translate(-19472 -14615)" fill="#f0f0f0"/>
            </svg>
        </a>
        <h1 class="mb-7 custom-fs-16 d-none d-md-block ls-1.8px fw-semibold text-uppercase">{{ __('New Password') }}</h1>
        <h1 class="mb-7 custom-fs-18 d-md-none ls-1.8px fw-semibold text-uppercase">{{ __('New Password') }}</h1>

        <form method="POST" action="{{ route('password.store') }}" class="my-auto w-100">
            @csrf

            <div class="position-relative">
                @error('email')
                <span class="invalid-feedback" role="alert">{{ $message }}</span>
                @enderror
                <input type="hidden" name="email" value="{{request()->query('email')}}">
                <input type="hidden" name="token" value="{{request()->segment(2)}}">
            </div>
            <div class="input-placeholder">
                <input id="password" type="password"
                       class="line-style ls-0.8px @error('password') is-invalid @enderror"
                       name="password" required autocomplete="new-password" placeholder="Password">
                <span class="placeholder-shown">Password</span>
                @error('password')
                <span class="invalid-feedback mb-3" role="alert">{{ $message }}</span>
                @enderror
                <div class="password-eye">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                        <path id="Path_5662" data-name="Path 5662"
                              d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z"
                              transform="translate(-1 -4.5)" fill="#f0f0f0"/>
                    </svg>
                </div>
            </div>
            <div class="input-placeholder">
                <input id="password_confirmation" type="password"
                       class="line-style ls-0.8px @error('password_confirmation') is-invalid @enderror"
                       name="password_confirmation" required autocomplete="new-password"
                       placeholder="Password confirmation">
                <span class="placeholder-shown">Password confirmation</span>
                @error('password_confirmation')
                <span class="invalid-feedback mb-3" role="alert">{{ $message }}</span>
                @enderror
                <div class="password-eye" data-pass-target="password_confirmation">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                        <path id="Path_5662" data-name="Path 5662"
                              d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z"
                              transform="translate(-1 -4.5)" fill="#f0f0f0"/>
                    </svg>
                </div>
            </div>

            <div
                class="d-flex flex-column flex-md-row align-items-stretch align-items-md-center justify-content-between mt-0 mt-sm-5">
                <button type="submit" class="btn btn-primary ms-0 ls-1.2px">
                    {{ __('Save') }}
                </button>
{{--                <a class="btn btn-link text-decoration-none text-info d-none d-md-block" href="{{ url('login') }}">--}}
{{--                    {{ __('← Go Back') }}--}}
{{--                </a>--}}
            </div>
        </form>
    </div>
@endsection

