<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Lease;
use App\Models\License;
use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\License>
 */
class LicenseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'company_id' => $this->faker->randomElement([1,2,3]),
            'type' => $this->faker->randomElement(['license', 'exclusivity']),
            'location' => 'Nis',
            'studio_id' => 1,
            'price' => 5000,
            'duration' => 10,
            'starting_date' => now(),
            'deposit_amount' => 1000,
            'deposit_date' => now(),
            'is_active' => true,
            'initial_currency_id' => 1
        ];
    }
}
