<?php

namespace App\Services\Admin\Payment;

use App\Models\Customer;
use App\Models\Machine;
use Illuminate\Pagination\LengthAwarePaginator;

interface IPaymentService
{
    public function store(array $data);
    public function update(array $data, Machine $machine);
    public function searchLicense(string $searchData, string $orderParam, string $orderType, int $perPage, string $tab, int $status, int $licenseId): LengthAwarePaginator;
    public function searchLease(string $searchData, string $orderParam, string $orderType, int $perPage, string $tab, int $status, int $leaseId): LengthAwarePaginator;
}
