<?php

namespace App\Http\Controllers\Studio;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use App\Http\Requests\Admin\License\StoreLicenseRequest;
use App\Http\Requests\Admin\License\UpdateStudioLicenseRequest;
use App\Http\Requests\Admin\Lease\UpdateStudioLeaseRequest;
use App\Http\Requests\Admin\Customer\UpdateCustomerRequest;
use App\Http\Requests\Admin\Customer\UpdateSettingRequest;
use App\Models\Customer;
use App\Models\Payment;
use App\Models\License;
use App\Models\Lease;
use App\Models\AdminSettings;
use App\Models\User;
use App\Models\Invoice;
use App\Models\InvoiceProduct;
use App\Models\Purchase;
use App\Models\Machine;
use App\Models\Studio;
use App\Models\Company;
use App\Models\Countries;
use App\Models\State;
use App\Models\LicenseNotes;

use App\Services\Admin\License\ILicenseService;
use App\Services\Admin\Lease\ILeaseService;
use App\Services\Admin\PaymentHistory\IPaymentHistoryService;
use App\Services\Admin\InvoiceProduct\IInvoiceProductService;
use App\Services\Admin\CustomerAgreement\ICustomerAgreementService;
use App\Services\Admin\Customer\ICustomerService;

use App\Helpers\CurrencyConversionHelper;
use App\Helpers\Constants;
use App\Helpers\InvoiceHelper;

use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class StudioController extends Controller
{
    private ILicenseService $licenseService;
    private ILeaseService $leaseService;
    private IPaymentHistoryService $paymentHistoryService;
    private IInvoiceProductService $invoiceProductService;
    private ICustomerAgreementService $customerAgreementService;
    private ICustomerService $customerService;

    public function __construct(
        ILicenseService $licenseService,
        ILeaseService $leaseService,
        IPaymentHistoryService $paymentHistoryService,
        IInvoiceProductService $invoiceProductService,
        ICustomerAgreementService $customerAgreementService,
        ICustomerService $customerService,
    ) {
        $this->licenseService = $licenseService;
        $this->leaseService = $leaseService;
        $this->paymentHistoryService = $paymentHistoryService;
        $this->invoiceProductService = $invoiceProductService;
        $this->customerAgreementService = $customerAgreementService;
        $this->customerService = $customerService;
    }

    public function index(Request $request)
    {
        $customer = $this->getCustomer($request);
        $currentCurrency = AdminSettings::first()->currency;
        $customerPayments = Payment::with('license.conversionRate', 'lease.conversionRate')
            ->where('customer_id', $customer->id)
            ->where('license_id', '!=', null)
            ->orWhere('lease_id', '!=', null)
            ->orderBy('updated_at', 'desc')->get();

        $firstDayOfMonth = Carbon::now()->startOfMonth()->toDateString();
        $remainingPayments = $customerPayments->where('status', Constants::PAYMENT_STATUS['not_paid'])->where('payment_date', $firstDayOfMonth);
        
        $remainingSubscriptionsSum = CurrencyConversionHelper::calculatePaymentsSum($remainingPayments, $currentCurrency);
        $licenses = License::where('customer_id', $customer->id)->where('is_active', true)->get();
        $activeLicenses = $licenses->where('type', Constants::LICENSE_TYPES[0])->count();
        $activeExclusivities = $licenses->where('type', Constants::LICENSE_TYPES[1])->count();

        $leases = Lease::where('customer_id', $customer->id)->where('is_active', true)->get();
        $activeLeases = $leases->count();
        $machines = $leases->sum('machine_quantity');
        $purchasedMachines = Purchase::where('customer_id', $customer->id)->where('is_active', true)->count();
        $payments = $customerPayments->where('status', Constants::PAYMENT_STATUS['paid'])->sortByDesc('updated_at')->take(10);
        $sequential_id = 1;
        foreach ($payments as $payment) {
            $payment['sequential_id'] = $sequential_id++;
            $conversionRate = ($payment->license_id)
                ? $payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate
                : $payment->lease->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);
        }
        return view(
            'studio.dashboard',
            compact('payments', 'remainingSubscriptionsSum', 'activeLicenses', 'activeExclusivities', 'activeLeases', 'machines', 'purchasedMachines', 'customer', 'currentCurrency')
        )->render();
    }

    public function downloadInvoice(Customer $customer, Invoice $invoice)
    {
        $pdf = InvoiceHelper::generateInvoicePDF($invoice);
        return $pdf->download('Invoice-' . $invoice->formatted_number  . '.pdf');
    }

    public function location(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.location.index', compact('customer'))->render();
    }

    public function lease(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.lease.index', compact('customer'))->render();
    }
    public function invoice(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.invoice.index', compact('customer'))->render();
    }

    public function agreement(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.agreement.index', compact('customer'))->render();
    }

    public function profile(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.profile.index', compact('customer'))->render();
    }

    public function setting()
    {
        $userId = auth()->id();
        $customer = auth()->user()->customer;
        $settings = AdminSettings::where('admin_id', $userId)->first();
        return view('studio.setting.index', compact('settings', 'customer'));
    }

    public function tickets(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.support.tickets', compact('customer'))->render();
    }


    public function location_search(Customer $customer, Request $request): JsonResponse
    {

        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';
        $licenses = $this->licenseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );
        $currentCurrency = AdminSettings::first()->currency;
        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }
        foreach ($licenses as $license) {
            $license['sequential_id'] = $sequential_id++;
            $conversionRate             = $license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            $license['remaining']       = CurrencyConversionHelper::calculateLicensePaymentsSum($license, $conversionRate);
            $license['expires']         = Carbon::parse($license->starting_date)->addYear()->format('m/d/Y');
            $license['conversion_rate'] = $conversionRate;
            $license['price']           = CurrencyConversionHelper::calculateLicenseMonthlyPrice($license, $conversionRate);
            $license['note_count'] = $license->license_notes_count;
        }
        $viewContent = view('studio.location.search', compact('licenses', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function ticket_search(Customer $customer, Request $request): JsonResponse
    {

        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';
        $licenses = $this->licenseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );
        $currentCurrency = AdminSettings::first()->currency;
        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }
        foreach ($licenses as $license) {
            $license['sequential_id'] = $sequential_id++;
            $conversionRate             = $license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            $license['remaining']       = CurrencyConversionHelper::calculateLicensePaymentsSum($license, $conversionRate);
            $license['expires']         = Carbon::parse($license->starting_date)->addYear()->format('m/d/Y');
            $license['conversion_rate'] = $conversionRate;
            $license['price']           = CurrencyConversionHelper::calculateLicenseMonthlyPrice($license, $conversionRate);
            $license['note_count'] = $license->license_notes_count;
        }
        $viewContent = view('studio.support.search-ticket', compact('licenses', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function lease_search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';
        $status     = $request->get('status') ?? 'all';
        $leases = $this->leaseService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer,
            $status,
        );
        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }
        foreach ($leases as $lease) {
            $lease['sequential_id'] = $sequential_id++;
            $conversionRate           = $lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            $lease['remaining']       = CurrencyConversionHelper::calculateLeasePaymentsSum($lease, $conversionRate);
            $lease['conversion_rate'] = $conversionRate;
            $lease['expires']         = Carbon::parse($lease->payments->sortByDesc('payment_date')->take(1)->first()->payment_date)->format('m/d/Y');
            $lease['total']           = CurrencyConversionHelper::calculateLeaseTotalPrice($lease, $conversionRate);
            $lease['monthly']         = CurrencyConversionHelper::calculateLeaseMonthlyPrice($lease, $conversionRate);
            $lease['note_count'] = $lease->lease_notes_count;
        }
        $viewContent = view('studio.lease.search', compact('leases', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function payment_history(Request $request)
    {
        $customer = $this->getCustomer($request);
        return view('studio.payment-history.index', compact('customer'))->render();
    }

    public function payment_history_search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'id';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';
        $payments = $this->paymentHistoryService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $customer->id
        );
        $currentCurrency = AdminSettings::first()->currency;
        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }
        foreach ($payments as $payment) {
            $payment['sequential_id'] = $sequential_id++;
            if ($payment->license_id) {
                $payment['name'] = $payment->license->studio->name;
            } else if ($payment->lease_id) {
                $payment['name'] = $payment->lease->machine->name;
            } else if ($payment->purchase_id) {
                $purchase = Purchase::find($payment->purchase_id);
                $payment['name'] = $purchase->machine->name;
            }
            if ($payment->license_id) {
                $payment['type'] = ucfirst($payment->license->type);
            } else if ($payment->lease_id) {
                $payment['type'] = 'Lease';
            } else if ($payment->purchase_id) {
                $payment['type'] = 'Purchase';
            }
            if ($payment->license_id) {
                $payment['duration'] = $payment->license->duration;
            } else if ($payment->lease_id) {
                $payment['duration'] = $payment->lease->duration;
            } else if ($payment->purchase_id) {
                $payment['duration'] = 1;
            }
            $payment['invoice_number'] = $payment->invoice?->formatted_number;
            if ($payment->license_id) {
                $conversionRate = $payment->license->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            } else if ($payment->lease_id) {
                $conversionRate = $payment->lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            } else if ($payment->purchase_id) {
                $purchase = Purchase::findOrFail($payment->purchase_id);
                $conversionRate = $purchase->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            }
            $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);
        }

        $viewContent = view('studio.payment-history.search', compact('payments', 'customer'))->render();

        return response()->json($viewContent);
    }
    
    public function invoice_search(Request $request): JsonResponse
    {
        $customer = $this->getCustomer($request);
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $invoices = $this->invoiceProductService->search($searchData, $orderParam, $orderType, $perPage, $status, $customer->id);

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = ($page - 1) * $perPage + 1;
        }

        foreach ($invoices as $invoiceProduct) {
            $invoiceProduct['sequential_id'] = $sequential_id++;
            $invoiceProduct['orderParam'] = $orderParam;
            $invoiceProduct['orderType'] = $orderType;
            $product_items = $invoiceProduct->load('items.product', 'customer');
            $invoiceProduct['amount'] = 0;
            $subTotal = 0;
            $totalDiscount = 0;

            if (count($product_items->items) > 0) {
                foreach ($product_items->items as $key => $item) {
                    $discount_val = 0;
                    if ($item->discount_type == "$") {
                        $discount_val = $item->discount;
                    } else {
                        $discount_val = $item->price * $item->quantity * ($item->discount / 100);
                    }

                    $subTotal += $item->price * $item->quantity;
                    $totalDiscount += $discount_val;
                }
            }

            $tax = $invoiceProduct->show_tax ? ($subTotal - $totalDiscount) * 0.095 : 0;
            $tax = $tax < 0 ? 0 : $tax; // Ensure tax is not negative

            $invoiceProduct['amount'] = $subTotal - $totalDiscount + $tax + $invoiceProduct->shipping_fee + $invoiceProduct->handling_fee;
        }

        $viewContent = view('studio.invoice.search', compact('invoices'))->render();

        return response()->json($viewContent);
    }

    public function agreements_search(Request $request): JsonResponse
    {
        $customer = $this->getCustomer($request);
        // dd($customer);
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType  = $request->get('order_type') ?? 'desc';

        $agreements = $this->customerAgreementService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $customer
        );

        foreach ($agreements as $k => &$agreement) {
            $studio = Studio::where('id', $agreement->location)->first();
            $agreement->studio_name = $studio?->name;
            if ($agreement->type == 'license') {
                if (isset($agreement->custom) and $agreement->custom == 'custom') {
                    $agreement->name = $agreement->file_name;
                } else {
                    $name = License::where('id', $agreement->license_id)->first();
                    if (isset($name->package) and $name->package != NULL and (int)$name->package > 0) {
                        $agreement->name = Constants::LICENSE_PACKAGES_KEYS[$name->package];
                    } else {
                        $agreement->name = 'No package selected';
                    }
                }
            } else if ($agreement->type == 'lease') {
                if (isset($agreement->custom) and $agreement->custom == 'custom') {
                    $agreement->name = $agreement->file_name;
                } else {
                    $name = Lease::where('id', $agreement->lease_id)->first();
                    if (isset($name->configuration_id) and $name->configuration_id != NULL) {
                        $machine = Machine::where('id', $name->configuration_id)->first();
                        $agreement->name = $machine->name;
                    } else if (isset($name->machine_id) and $name->machine_id != NULL) {
                        $machine = Machine::where('id', $name->configuration_id)->first();
                        $agreement->name = $machine->name;
                    } else {
                        $agreement->name = $agreement->file_name;
                    }
                }
            } else if ($agreement->type == 'purchase') {
                $name = Purchase::where('id', $agreement->purchase_id)->first();
                if (isset($name->configuration_id) and $name->configuration_id != NULL) {
                    $machine = Machine::where('id', $name->configuration_id)->first();
                    $agreement->name = $machine->name;
                } else if (isset($name->machine_id) and $name->machine_id != NULL) {
                    $machine = Machine::where('id', $name->configuration_id)->first();
                    $agreement->name = $machine->name;
                } else {
                    $agreement->name = $agreement->file_name;
                }
            } else {
                $agreement->name = 'No lease ID no license ID';
            }
        }
        $viewContent = view('studio.agreement.search', compact('agreements', 'customer'))->render();
        return response()->json($viewContent);
    }

    public function location_show(Customer $customer, License $license, Request $request)
    {
        $tab              = $request->query('tab', 'profile');
        $currentCurrency  = AdminSettings::first()->currency;
        $conversionRate   = $license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;

        $licenseRemaining = CurrencyConversionHelper::calculateLicensePaymentsSum($license, $conversionRate);
        $licenseMonthly   = CurrencyConversionHelper::calculateLicenseMonthlyPrice($license, $conversionRate);
        $licenseTotal     = CurrencyConversionHelper::calculateLicenseTotalPrice($license, $conversionRate);
        $licenseDeposit   = CurrencyConversionHelper::calculateLicenseDeposit($license, $conversionRate);
        $license_notes = $license->notes()->get();
        $license_files_count = count($license->files()->get());
        return view('studio.location.show', compact('customer', 'license', 'tab', 'licenseRemaining', 'conversionRate', 'licenseTotal', 'licenseMonthly', 'licenseDeposit', 'license_notes', 'license_files_count'));
    }

    public function location_create(Customer $customer): View
    {
        $companies = Company::select('id', 'name')->get();
        $studios   = Studio::select('id', 'name')->get();
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        return view('studio.location.create', compact('customer', 'studios', 'states_countries', 'companies'));
    }

    public function location_store(Customer $customer, StoreLicenseRequest $request)
    {
        try {
            $this->licenseService->store($request->validated(), $customer);
            toastr()->addSuccess('', 'License created successfully.');
            return redirect()->route('studio.location', ['customer' => $customer]);
        } catch (\Exception $e) {
            dd($e->getMessage());
            toastr()->addError('Location creation failed');
            return redirect()->back()->withInput();
        }
    }

    public function location_edit(Customer $customer, License $license): View
    {
        $companies = Company::select('id', 'name')->get();
        $studios   = Studio::select('id', 'name')->get();
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        $currentCurrency            = AdminSettings::first()->currency;
        $conversionRate             = $license->conversionRate()->where('currency_id', $currentCurrency->id)->first();
        $customer->description      = LicenseNotes::where('license_id', $license->id)->first()->body ?? NULL;
        $license->converted_price   = $license->price / 100 / $conversionRate->rate;
        $license->converted_deposit = ($license->deposit_amount) ? $license->deposit_amount / 100 / $conversionRate->rate : null;
        return view('studio.location.edit', compact('license', 'customer', 'studios', 'states_countries', 'companies'));
    }

    public function location_update(Customer $customer, License $license, UpdateStudioLicenseRequest $request)
    {
        try {
            $companies = Company::select('id', 'name')->get();
            $states_countries['states'] = State::all();
            $states_countries['countries'] = Countries::all();

            $this->licenseService->update($request->validated(), $license, $customer);
            toastr()->addSuccess('', 'License updated successfully.');
            return redirect()->route('studio.location.edit', ['customer' => $customer, 'license' => $license, 'states_countries' => $states_countries, 'companies' => $companies]);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('License update failed');
            return redirect()->back();
        }
    }

    public function location_destroy(Customer $customer, License $license)
    {
        try {
            $this->licenseService->delete($license);
            toastr()->addSuccess('', 'Location deleted successfully.');
            return view('studio.location.index', compact('customer'))->render();
        } catch (\Exception $e) {
            toastr()->addError('Location delete failed');
            return redirect()->back();
        }
    }

    public function lease_create(Customer $customer): View
    {
        $companies = Company::select('id', 'name')->get();
        $locations = License::select(DB::raw('GROUP_CONCAT(studio_id) as ids'))->where('customer_id', $customer->id)->get();
        if (!empty($locations[0]['ids'])) {
            $studios = Studio::select('id', 'name')->whereIn('id', explode(',', (string)$locations[0]['ids']))->get();
        } else {
            $studios = NULL;
        }
        $machines  = Machine::select('id', 'name')->where('id', '!=', '2')->whereNull('parent_machine_id')->orderBy('name', 'desc')->get();

        return view('studio.lease.create', compact('customer', 'studios', 'machines', 'companies'));
    }

    public function lease_show(Customer $customer, Lease $lease, Request $request): View
    {
        $currentCurrency = AdminSettings::first()->currency;
        $conversionRate  = $lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
        $leaseRemaining  = CurrencyConversionHelper::calculateLeasePaymentsSum($lease, $conversionRate);
        $leaseTotal      = CurrencyConversionHelper::calculateLeaseTotalPrice($lease, $conversionRate);
        $leaseMonthly    = CurrencyConversionHelper::calculateLeaseMonthlyPrice($lease, $conversionRate);
        $leaseDeposit    = CurrencyConversionHelper::calculateLeaseDeposit($lease, $conversionRate);

        $lease_notes = $lease->notes()->get();
        $lease_files_count = count($lease->files()->get());
        return view('studio.lease.show', compact('customer', 'lease', 'leaseRemaining', 'leaseTotal', 'leaseMonthly', 'leaseDeposit', 'lease_notes', 'lease_files_count'));
    }

    public function lease_edit(Customer $customer, Lease $lease): View
    {
        $states_countries['states']             = State::all();
        $states_countries['countries']          = Countries::all();
        return view('studio.lease.edit', compact('lease', 'customer', 'states_countries'));
    }

    public function lease_update(Customer $customer, Lease $lease, UpdateStudioLeaseRequest $request)
    {
        try {
            $states_countries['states']             = State::all();
            $states_countries['countries']          = Countries::all();
            $this->leaseService->update($request->validated(), $lease, $customer);
            toastr()->addSuccess('', 'Lease updated successfully.');
            return redirect()->route('studio.lease.edit', ['customer' => $customer, 'lease' => $lease, 'states_countries' => $states_countries]);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Lease update failed');
            return redirect()->back();
        }
    }

    public function lease_destroy(Customer $customer, Lease $lease)
    {
        try {
            $this->leaseService->delete($lease);
            toastr()->addSuccess('', 'Lease deleted successfully.');
            return view('studio.lease.index', compact('customer'))->render();
        } catch (\Exception $e) {
            toastr()->addError('Lease delete failed');
            return redirect()->back();
        }
    }

    public function invoice_destroy(InvoiceProduct $invoiceProduct): RedirectResponse
    {
        try {
            $this->invoiceProductService->delete($invoiceProduct);
            toastr()->addSuccess('', 'Invoice deleted successfully.');
            return redirect(route('studio.invoice'));
        } catch (\Exception $e) {
            toastr()->addError('Invoice delete failed');
            return redirect()->back();
        }
    }

    public function profile_edit(Customer $customer): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();
        $customer = $customer->load('owner');

        return view('studio.profile.edit', compact('customer', 'states_countries'));
    }

    public function profile_update(Customer $customer, UpdateCustomerRequest $request)
    {
        try {
            if ($request->hasFile('avatar')) {
                // dd("has avatar");
                $file = $request->file('avatar');
                $extension = $file->getClientOriginalExtension();
                $randomName = \Illuminate\Support\Str::random(40) . '.' . $extension;
                $avatarPath = 'instructors/' . $randomName;
                $file->storeAs('public/instructors', $randomName);

                $user = User::find($customer->owner->id);
                if ($user) {
                    $user->avatar = $avatarPath;
                    $user->save();
                }
            }
            // dd($request->validated());
            $this->customerService->update($request->validated(), $customer);
            toastr()->addSuccess('', 'Customer updated successfully.');
            return redirect()->route('studio.profile.edit', $customer);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Customer update failed');
            return redirect()->back();
        }
    }

    public function setting_update(Customer $customer, UpdateSettingRequest $request)
    {
        try {
            $this->customerService->update_setting($request->validated(), $customer);
            toastr()->addSuccess('', 'Customer setting updated successfully.');

            $userId = auth()->id();
            $settings = AdminSettings::where('admin_id', $userId)->first();
            return view('studio.setting.index', compact('settings', 'customer'));
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Customer update failed');
            return redirect()->back();
        }
    }

    public function getCustomer(Request $request)
    {
        $userId = $request->user()->id;
        $user = User::with('customer')->find($userId);
        return $user->customer;
    }

    public function hash_file_name($org_file_name)
    {
        $hashed_name = \Illuminate\Support\Str::random(40) . '.' . $org_file_name;
        return $hashed_name;
    }
}
