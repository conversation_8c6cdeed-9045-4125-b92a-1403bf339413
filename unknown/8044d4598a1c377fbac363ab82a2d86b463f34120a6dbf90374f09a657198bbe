<?php

namespace App\Services\NotificationService;

use App\Models\AdminSettings;
use Illuminate\Support\Facades\Config;

class SmtpService
{
    /**
     * @param object $smtpSettings
     * @return array|string[]
     */
    public function setSmtp(object $smtpSettings)
    {
        if ($smtpSettings->smtp_host && $smtpSettings->smtp_port && $smtpSettings->smtp_username && $smtpSettings->smtp_password) {
            Config::set([
                'mail.mailers.custom-smtp.transport'    => 'smtp',
                'mail.mailers.custom-smtp.url'          => env('MAIL_URL'),
                'mail.mailers.custom-smtp.encryption'   => env('MAIL_ENCRYPTION', 'tls'),
                'mail.mailers.custom-smtp.timeout'      => null,
                'mail.mailers.custom-smtp.local_domain' => env('MAIL_EHLO_DOMAIN'),
                'mail.mailers.custom-smtp.host'         => $smtpSettings->smtp_host,
                'mail.mailers.custom-smtp.port'         => $smtpSettings->smtp_port,
                'mail.mailers.custom-smtp.username'     => $smtpSettings->smtp_username,
                'mail.mailers.custom-smtp.password'     => $smtpSettings->smtp_password,
            ]);
        }
        if ($smtpSettings->smtp_from_email && $smtpSettings->smtp_from_name) {
            return ['from_email' => $smtpSettings->smtp_from_email, 'from_name' => $smtpSettings->smtp_from_name];
        } else {
            return $this->setAdminSmtp(AdminSettings::first());
        }
    }

    public function setAdminSmtp(object|null $smtpSettings): array
    {
        if ($smtpSettings?->smtp_from_email && $smtpSettings?->smtp_from_name) {
            return ['from_email' => $smtpSettings->smtp_from_email, 'from_name' => $smtpSettings->smtp_from_name];
        }
        return ['from_email' => \config('mail.from.address'), 'from_name' => \config('mail.from.name')];
    }
}
