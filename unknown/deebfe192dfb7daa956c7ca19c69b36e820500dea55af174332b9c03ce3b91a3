<?php

return [
    'base_url' => env('ADOBE_SIGN_URLS_BASE_URL', 'https://api.na3.adobesign.com:443/api/rest/v6/baseUris'),
    'access_token' => env('ADOBE_SIGN_ACCESS_TOKEN', '3AAABLblqZhCof_uq_fAcLQM984oBc41loLOgEhfL1UgIQn9dyq4cF3pyZl-1lSEByGKJzk9YwDIaKszxL25PsQ9djLIeJInT'),
    'templates' => 'api/rest/v6/libraryDocuments',
    'agreements' => 'api/rest/v6/agreements',
    'webhook_url' => 'api/rest/v6/webhooks',
    'app_webhook_url' => env('ADOBE_APP_WEBHOOK_URL'),
    'approver_email' => env('ADOBE_SIGN_EMAIL'),
    'approver_name' => env('ADOBE_SIGN_NAME'),
];
