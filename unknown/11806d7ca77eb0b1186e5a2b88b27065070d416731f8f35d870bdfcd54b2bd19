<ul class="list-group checkbox-group @if($errors->has($field_name)) has-danger @endif">
    @foreach($values as $value)
        <li class="list-group-item" >
            <span></span>
            <input class="form-check-input me-3" id="{{$field_name}}_{{$value->id}}" name="{{$field_name}}" placeholder="{{$field_label}}"
                   value="{{$value->id}}"
                   type="checkbox"
                @readonly(isset($readonly) && $readonly == 'readonly')
                @disabled(isset($disabled) && $disabled == 'disabled')
                @checked(in_array($value->id, $selected))
                @required(isset($required) && $required == 'required')
            >
            <label class="form-check-label stretched-link fs-14px ls-0.7px" for="{{$field_name}}">{{ $value->$field }}</label>
        </li>
    @endforeach


    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</ul>
