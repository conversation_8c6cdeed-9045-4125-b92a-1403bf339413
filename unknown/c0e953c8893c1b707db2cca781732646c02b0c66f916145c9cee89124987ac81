<?php

namespace App\Services\Admin\Notification;

use App\Models\AdminNotification;
use Illuminate\Pagination\LengthAwarePaginator;

class NotificationService implements INotificationService
{
    public function store(array $data)
    {
        AdminNotification::create($data);
    }

    public function update(array $data, AdminNotification $notification)
    {
        $notification->update($data);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator
    {
        $query = AdminNotification::query();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where('title', 'LIKE', '%' . $searchData . '%');
        });

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function delete(AdminNotification $notification)
    {
        $notification->delete();
    }
}
