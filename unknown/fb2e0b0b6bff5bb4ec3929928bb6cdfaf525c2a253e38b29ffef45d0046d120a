<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View|RedirectResponse
    {
        if (\auth()->user()) {
            return redirect(RouteServiceProvider::HOME);
        }
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    // public function store(LoginRequest $request): RedirectResponse
    // {
    // // if (\auth()->user()) {
    // //     return redirect(RouteServiceProvider::HOME);
    // // }
    // // $request->authenticate();

    // // $request->session()->regenerate();

    // // return redirect()->route('admin.dashboard.index');


    // $request->authenticate();
    // // dd($request->authenticate());
    // $request->session()->regenerate();
    // $user = auth()->user();
    // $isSuperAdmin = DB::table('admin_settings')
    //     ->where('admin_id', $user->id)
    //     ->exists();

    // // dd($isSuperAdmin);
    // if ($isSuperAdmin) {
    //     return redirect()->route('admin.dashboard.index');
    // }

    // return redirect()->route('customer.home');


    // }
    public function store(LoginRequest $request): RedirectResponse
    {
        if (\auth()->user()) {
            return redirect(RouteServiceProvider::HOME);
        }
        $request->authenticate();

        $request->session()->regenerate();

        return redirect()->route('admin.dashboard.index');
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
