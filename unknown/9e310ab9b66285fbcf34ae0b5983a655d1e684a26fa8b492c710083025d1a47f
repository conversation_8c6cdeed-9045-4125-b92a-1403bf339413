<?php

namespace Database\Factories;

use App\Models\Address;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Address>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'street_1' => fake()->address,
            'street_2' => fake()->address,
            'city' => fake()->city,
            'zip' => fake()->name,
            'county' => fake()->country,
            'state' => fake()->name,
            'country' => fake()->country,
        ];
    }
}
