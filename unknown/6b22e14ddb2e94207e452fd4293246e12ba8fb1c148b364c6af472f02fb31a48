<?php

namespace App\Observers;

use App\Helpers\Constants;
use App\Http\Controllers\StripePaymentController;
use App\Models\License;
use App\Models\Payment;
use App\Models\Customer;
use App\Models\User;
use Carbon\Carbon;
use App\Mail\PaymentLinkEmail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class LicenseObserver
{
    public function created(License $license)
    {
        Log::info("License created: {$license->id}");
        $depositDate = Carbon::parse($license->starting_date);
        $startDate = Carbon::parse($license->starting_date)->addMonths(6);
        // $nextDate = $startDate->addYear();

        if(isset($license->deposit_amount) AND $license->deposit_amount != '' AND $license->deposit_amount != NULL AND ($license->deposit_amount != $license->price)){
            $deposit_payment = Payment::create([
                'license_id' => $license->id,
                'customer_id' => $license->customer_id,
                'payment_number' => 1,
                'payment_amount' => $license->deposit_amount,
                'payment_date' => $depositDate,
                'description' => 'Deposit',
            ]);
            Log::info("License Deposit Payment created: {$deposit_payment->id}");

            $payment = Payment::create([
                'license_id' => $license->id,
                'customer_id' => $license->customer_id,
                'payment_number' => 1,
                'payment_amount' => $license->price - $license->deposit_amount,
                'payment_date' => $startDate,
                'description' => 'Rest of the payment',
            ]);
            Log::info("License Rest of Payment created: {$payment->id}");

            $deposit_invoice = $deposit_payment->invoice()->create(['customer_id' => $license->customer_id]);
            Log::info("License Deposit Invoice created: {$deposit_invoice->id}");

            $invoice = $payment->invoice()->create(['customer_id' => $license->customer_id]);
            Log::info("License Invoice created: {$invoice->id}");
        }else{
            $payment = Payment::create([
                'license_id' => $license->id,
                'customer_id' => $license->customer_id,
                'payment_number' => 1,
                'payment_amount' => ($license->price != $license->deposit_amount) ? $license->price - $license->deposit_amount : $license->price,
                'payment_date' => $startDate,
                'description' => 'Full license payment',
            ]);
            Log::info("License Full amount license Payment created: {$payment->id}");

            $invoice = $payment->invoice()->create(['customer_id' => $license->customer_id]);
            Log::info("License Invoice created: {$invoice->id}");
        }
    }

    public function updated(License $license)
    {
        if ($license->wasChanged('price')) {
            $license->payments()
                ->where('payment_date', '>', Carbon::now()->endOfMonth())
                ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                ->update(['payment_amount' => $license->price]);
        }

        if ($license->wasChanged('duration')) {
            $oldDuration = $license->getOriginal('duration');
            $newDuration = $license->duration;

            if ($oldDuration > $newDuration) {
                $paymentsToDelete = $license->payments()
                    ->where('payment_number', '>', $newDuration)
                    ->where('payment_date', '>', Carbon::now())
                    ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                foreach ($paymentsToDelete as $payment) {
                    $payment->invoice->delete();
                    $payment->forceDelete();
                }
            } elseif ($oldDuration < $newDuration) {
                $numberOfPaymentsToBeAdded = $newDuration - $oldDuration;
                $dateOfCurrentLastPayment = $license->payments->where('payment_number', $oldDuration)->first()->payment_date;
                for ($i = 1; $i <= $numberOfPaymentsToBeAdded; $i++) {
                    $payment = $license->payments()->create([
                        'customer_id'    => $license->customer_id,
                        'payment_number' => $oldDuration + $i,
                        'payment_amount' => $license->price,
                        'payment_date'   => Carbon::parse($dateOfCurrentLastPayment)->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
                    ]);

                    $payment->invoice()->create(['customer_id' => $license->customer_id]);
                }
            }
        }

        if ($license->wasChanged('starting_date')) {
            $paymentsToDelete = $license->payments;
            foreach ($paymentsToDelete as $payment) {
                $payment->invoice->delete();
                $payment->forceDelete();
            }
            for ($i = 1; $i <= $license->duration; $i++) {
                $payment = $license->payments()->create([
                    'customer_id' => $license->customer_id,
                    'payment_number' => $i,
                    'payment_amount' => $license->price,
                    'payment_date' => Carbon::parse($license->starting_date)->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
                ]);

                $payment->invoice()->create(['customer_id' => $license->customer_id]);
            }
        }
    }
}
