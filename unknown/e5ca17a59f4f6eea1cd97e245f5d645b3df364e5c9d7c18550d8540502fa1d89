@if(count($payments) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Name') }}</div>
        <div>{{ __('Type') }}</div>
        <div>{{ __('Payment') }}</div>
        <div id="payment_amount" class="sortable-list-header" data-sort="payment_amount">{{ __('Amount') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Invoice') }}</div>
        <div id="payment_date" class="sortable-list-header" data-sort="payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">

        @foreach($payments as $payment)
            <div class="list-group-item list-group-item-action d-grid align-items-center" data-payment_id="{{ $payment->id }}">
                <p class="my-0 hide-transp">{{$payment->sequential_id}}</p>
                <p class="my-0">{{$payment->name}}</p>
                <p class="my-0 text-secondary">{{$payment->type}}</p>
                <p class="my-0">{{$payment->payment_number}} of {{$payment->duration}}</p>
                @if($payment->status == \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                    <p class="my-0 text-success">{{$admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                @else
                    <p class="my-0 text-danger">{{$admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                @endif
                <p class="my-0 text-secondary">{{ $payment->invoice_number ? 'Invoice #' . $payment->invoice_number : 'No invoice found' }}</p>
                <p class="my-0">{{\Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y')}}</p>

                <div class="status-div">
                    @if($payment->status === \App\Helpers\Constants::PAYMENT_STATUS['paid'])
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-normal bg-success text-success">
                            {{ __('Paid') }}
                        </div>
                    @elseif($payment->status === \App\Helpers\Constants::PAYMENT_STATUS['not_paid'])
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-normal bg-secondary text-secondary">
                            {{ __('Not Paid') }}
                        </div>
                    @else
                        <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-normal bg-danger text-danger">
                            {{ __('Declined') }}
                        </div>
                    @endif
                </div>

                @if ($payment->invoice)
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-content" id="payments-history-dropdown-content">
                        <a href="{{route('admin.download-invoice', ['customer' => $customer, 'invoice' => $payment->invoice])}}">Download Invoice</a>
                        <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$payment->invoice->id}}">{{ __('Email Invoice') }}</a>
                    </div>
                </div>
                @endif
            </div>
            @if ($payment->invoice)
            @include('partials.modals.email-invoice', [
                'id' => $payment->invoice->id,
                'route' => route('admin.email-invoice', ['customer' => $customer, 'invoice' => $payment->invoice]),
                'invoice' => $payment->invoice
            ])
            @endif
        @endforeach
        <div id="paginate paginate-payments-history">
            @if($payments)
                <div class="">
                    {!! $payments->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">

    const paymentsCount = @json($payments).total;
    const descriptiveLabel = paymentsCount === 1 ? ' Item' : ' Items';
    $('#paymentsCount').text(paymentsCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
