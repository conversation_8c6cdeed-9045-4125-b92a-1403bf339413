<div class="main-subtitle">
<h5>{{ __('Internal notes') }}{{ ' (' . $notes->count() . ')' }}</h5>
</div>

<form action="{{ route('admin.notes.store', $customer) }}" method="POST">
    @csrf
    <textarea class="form-text form-control py-4" style="max-width: none;min-height: 130px;max-height: 130px; margin-top:25px;" name="body" id="note" rows="10" placeholder="Add a note"></textarea>
    <button type="submit" class="btn btn-primary mt-5 fw-normal">{{ __('Save') }}</button>
</form>

<div class="internal-notes">
    @foreach($notes as $note)
        <div class="int-note-box">
            <p class="text-secondary mb-2">{{ $note->updated_at->format('m/d/Y') }}</p>
            <p class="note">{{ $note->body }}</p>
            <span class="text-secondary text-decoration-underline">
                <a href="javascript:;" class="delete-note px-0" data-bs-toggle="modal" data-bs-target="#deleteModal{{'Note'}}{{$note->id}}">
                    <button type="button" class="btn btn-small btn-ghost-light-bigger fw-normal ">{{ __('Delete') }}</button>
                </a>
            </span>
        </div>
        @include('partials.modals.delete', [
              'type' => 'Note',
              'id' => $note->id,
              'route' => route('admin.notes.destroy', ['customer' => $customer, 'note' => $note]),
              'title' => 'Note',
        ])
    @endforeach
</div>