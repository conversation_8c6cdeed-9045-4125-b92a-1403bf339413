<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class BundleItem extends Model
{
    use HasFactory, SoftDeletes;

    public $table = 'bundle_item';

    protected $fillable = [
        'bundle_id',
        'product_id',
        'quantity',
    ];

    protected $appends = [
        'price'
    ];

    public function getPriceAttribute(): string
    {
        $product = $this->product;
        return $product->price ?? '0';
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class, 'product_id');
    }

    public function bundle(): BelongsTo
    {
        return $this->belongsTo(Bundle::class, 'bundle_id');
    }

}
