<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Models\AdminSettings;
use App\Models\ConversionRate;
use App\Models\Customer;
use App\Models\Lease;
use App\Models\License;
use App\Models\Payment;
use App\Services\Admin\Payment\IPaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    private IPaymentService $paymentService;

    public function __construct(IPaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    public function searchLicensePayments(Customer $customer, License $license, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'payment_date';
        $orderType  = $request->get('order_type') ?? 'desc';
        $tab        = $request->get('tab') ?? 'payment-history';
        $status     = (int) $request->get('status') ?? '';

        $payments = $this->paymentService->searchLicense(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $tab,
            $status,
            $license->id
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($payments as $payment) {
            $conversionRate = $payment->license->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            $payment['payment_amount'] = CurrencyConversionHelper::calculateLicensePaymentAmount($payment, $conversionRate);
            $payment['conversion_rate'] = $conversionRate;
            $payment['sequential_id'] = $sequential_id++;
        }

        if ($tab === 'payment-history') {
            $viewContent = view('partials.forms.licenses.payment-history-search', compact('payments', 'customer', 'license'))->render();
        } else {
            $viewContent = view('partials.forms.licenses.upcoming-payments-search', compact('payments', 'customer', 'license'))->render();
        }

        return response()->json($viewContent);
    }

    public function searchLeasePayments(Customer $customer, Lease $lease, Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'payment_date';
        $orderType  = $request->get('order_type') ?? 'desc';
        $tab        = $request->get('tab') ?? 'payment-history';
        $status     = (int) $request->get('status') ?? '';

        $payments = $this->paymentService->searchLease(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $tab,
            $status,
            $lease->id
        );

        $currentCurrency = AdminSettings::first()->currency;
        $sequential_id = 1;
        foreach ($payments as $payment) {
            $conversionRate = $payment->lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            $payment['payment_amount'] = CurrencyConversionHelper::calculateLeasePaymentAmount($payment, $lease, $conversionRate);
            $payment['conversion_rate'] = $conversionRate;
            $payment['sequential_id'] = $sequential_id++;
        }

        if ($tab === 'payment-history') {
            $viewContent = view('partials.forms.leases.payment-history-search', compact('payments', 'customer', 'lease'))->render();
        } else {
            $viewContent = view('partials.forms.leases.upcoming-payments-search', compact('payments', 'customer', 'lease'))->render();
        }

        return response()->json($viewContent);
    }

    public function changeStatus(Payment $payment, Request $request)
    {
        try {
            $payment->update(['status' => $request->get('status')]);
            $paymentType = ($payment->license_id) ? 'license' : 'lease';

            if ($paymentType === 'lease') {
                if (Payment::where('lease_id', $payment->lease_id)->count()
                    === Payment::where('lease_id', $payment->lease_id)->where('status', Constants::PAYMENT_STATUS['paid'])->count())
                {
                    $payment->lease()->update(['is_active' => false]);
                } else {
                    $payment->lease()->update(['is_active' => true]);
                }
            } else {
                if (Payment::where('license_id', $payment->license_id)->count()
                    === Payment::where('license_id', $payment->license_id)->where('status', Constants::PAYMENT_STATUS['paid'])->count())
                {
                    $payment->license()->update(['is_active' => false]);
                } else {
                    $payment->license()->update(['is_active' => true]);
                }
            }

            toastr()->addSuccess('', 'Status changed successfully.');
            return redirect()->route('admin.' . $paymentType . 's' . '.show', [
                'customer' => $payment->customer,
                $paymentType => $payment->$paymentType
            ]);
        } catch (\Exception $e) {
            toastr()->addError('Status change failed');
            return redirect()->back();
        }
    }
}
