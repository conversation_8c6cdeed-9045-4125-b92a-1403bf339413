<?php

namespace App\Http\Services\Adobe;

use App\Models\Agreement;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Storage;

class AdobeTemplates
{
    /**
     * @throws GuzzleException
     */
    public function getTemplates($accessToken): array
    {
        //TODO: extract endpoints to configurable location - possibly a dedicated config file
        $url = env("ADOBE_SIGN_BASE_URL") . 'api/rest/v6/libraryDocuments?showHiddenLibraryDocuments=true';
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);
        //TODO: test if response->status == 200
        $responseData = json_decode($response->getBody(), true);
        $templates = [];

        foreach ($responseData['libraryDocumentList'] as $template)
        {
            if($template['sharingMode'] !== "GLOBAL")
            $templates[] = $template;
        }

        return $templates;
    }

    /**
     * @throws GuzzleException
     */
    public function getTemplateDetails($templateId, $accessToken): array
    {
        //TODO: extract endpoints to configurable location
        //TODO: make a template endpoint and replace $templateId with str_replace
        $url = env("ADOBE_SIGN_BASE_URL") . 'api/rest/v6/libraryDocuments/' . $templateId . '/formFields';
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);
        //TODO: test if response->status == 200
        $responseData = json_decode($response->getBody(), true);
        $templates = [];

        foreach ($responseData['fields'] as $template)
        {
            if($template['inputType'] === "TEXT_FIELD" && $template["contentType"] === "DATA")
                $templates[] = $template["name"];
        }

        return $templates;
    }

    /**
     * @param $accessToken
     * @param $agreementId
     * @param $agreementName
     * @return string
     * @throws GuzzleException
     */
    public function storeAgreement($accessToken, $agreementId, $agreementName): string
    {
        $url = env("ADOBE_SIGN_BASE_URL") . 'api/rest/v6/agreements/' . $agreementId . '/combinedDocument/url';
        $client = new Client();

        $headers = [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/json',
        ];
        $data = [
            'headers' => $headers
        ];

        $response = $client->get($url, $data);
        $pdfUrl = json_decode($response->getBody(), true);
        Storage::disk('local')->put($agreementName, file_get_contents($pdfUrl['url']));

        return Storage::path($agreementName);
    }
}
