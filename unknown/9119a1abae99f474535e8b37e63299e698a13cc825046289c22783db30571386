<?php

namespace App\Console\Commands;

use App\Helpers\Constants;
use App\Helpers\InvoiceHelper;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class SendInvoiceReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-invoice-reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send invoice reminder to customer';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Send invoice reminder to customer started');
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        $currentMonthPayments = Payment::whereBetween('payment_date', [$startDate, $endDate])
            ->where('payment_date', '>', Carbon::now()->subDays(8))
            ->where('status', Constants::PAYMENT_STATUS['not_paid'])
            ->where('payment_reminder', false)
            ->get();

        foreach ($currentMonthPayments as $payment) {
            $pdf = InvoiceHelper::generateInvoicePDF($payment->invoice);

            Mail::to($payment->customer->owner->email)->send(new \App\Mail\SendInvoiceReminder($payment->invoice, $pdf));
            $payment->update([
                'payment_reminder' => true
            ]);
        }

        $this->info('Send invoice reminder to customer finished');
    }
}
