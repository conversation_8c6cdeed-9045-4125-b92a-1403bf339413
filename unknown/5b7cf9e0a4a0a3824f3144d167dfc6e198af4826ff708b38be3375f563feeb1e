<?php

namespace App\Services\Admin\EmailTemplates;

use App\Models\Customer;
use App\Models\EmailTemplate;
use App\Models\Invoice;
use App\Models\Payment;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Pagination\LengthAwarePaginator;

class EmailTemplateService implements IEmailTemplateService
{
    public function renderTemplateKeywords(string $templateBody, array $keywords): string
    {
        foreach ($keywords as $key => $value) {
            $templateBody = str_replace("{{" . $key . "}}", $value, $templateBody);
        }
        return $templateBody;
    }

    public function search(
        string $searchData,
        string $orderParam,
        string $orderType,
        string $type,
        int $perPage = 10,
    ): LengthAwarePaginator
    {
        $query = EmailTemplate::query();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where('name', 'LIKE', '%' . $searchData . '%');
        });

        $query->when($type !== '', function ($query) use ($type) {
            $query->where('type', $type);
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }
}
