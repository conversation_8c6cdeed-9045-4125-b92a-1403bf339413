@import "variables"
@import "mixins"

input[type="text"].line-style,
input[type="email"].line-style,
input[type="url"].line-style,
input[type="password"].line-style,
input[type="search"].line-style,
input[type="number"].line-style,
input[type="tel"].line-style,
input[type="range"].line-style,
input[type="date"].line-style,
input[type="month"].line-style,
input[type="week"].line-style,
input[type="time"].line-style,
input[type="datetime"].line-style,
input[type="datetime-local"].line-style,
input[type="color"].line-style,
textarea.line-style
    width: 100%
    padding: 1.25rem 0
    margin-bottom: 1.25rem
    border: 1px solid $light
    border-radius: 0
    background: transparent
    color: $primary
    font-size: 14px
    line-height: 14px
    +sc-transition
    +sc-placeholder
        color: $info
        font-weight: 400
        text-transform: capitalize
    +breakpoint(md)
        border: none
        border-bottom: 1px solid $light


    &:focus
        border-color: $primary
        outline: none
        box-shadow: none

// Password eye icon
.password-eye
    position: absolute
    right: 15px
    top: 24px
    display: flex
    cursor: pointer
    z-index: 200
    font-size: 1rem
    color: $info

// END Password eye icon
// Second input style
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea
    +sc-transition
    +sc-placeholder
        font-weight: 400
        text-transform: capitalize

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button
    -webkit-appearance: none
    margin: 0

/* Hide arrows in Firefox */
input[type="number"]
    -moz-appearance: textfield

/* Optional: remove the default input styling (optional) */
input[type="number"]
    appearance: none

.form-control, .form-select
    max-width: 500px

.form-control
    min-height: 55px

.form-section-title
    font-weight: 600

.form-input
    input
        display: none

.form-input
    position: relative
    label
        display: block
        width: 80px
        height: 80px
        text-align: center
        // background: $primary
        color: $white
        border-radius: 50%
        cursor: pointer
        overflow: hidden
    img
        top: 0
        left: 0
        width: 100%
        height: 100%
        object-fit: cover

.form-switch
    display: flex
    align-items: center
    gap: 20px
    .form-check-input
        padding-top: 16px
        padding-bottom: 16px
        border-radius: 100px
        border: none
        box-shadow: none
        background-color: $light

        &:checked
            background-color: #e5f6e6

// Special date selector that doesn't look like an input field
.date-selector-wrap
    position: relative
    min-width: 200px
    max-width: 100%
    text-align: center

.date-selector
    border: none
    cursor: pointer
    padding: 0
    width: 20ch
    line-height: 3
    outline: none

.form-check *
    cursor: pointer

// Custom 'select none' action
.form-select-none
    cursor: pointer

// Rating stars
.star-rating
    .fa-star
        opacity: .2
        filter: grayscale(1)
        &.full
            opacity: 1
            filter: none
        &:before
            display: block
            width: 18px
            height: 17px

// Checkbox list fix
.checkbox-group .list-group-item
    border: none
    display: flex
    align-items: center
    padding-left: 0

.form-check-input
    &[type="radio"]
        width: 25px
        height: 25px

.checkbox-group-extended .list-group-item
    display: block

    .form-group
        margin-bottom: 0.6249894rem !important

// Colorpicker styles
.colorpicker-wrap
    position: relative

    input[type='color']
        position: absolute
        width: 0
        height: 0
        bottom: 0
        right: 0
        visibility: hidden
        appearance: none

    label
        display: flex
        justify-content: center
        align-items: center
        cursor: pointer
        height: 50px
        width: 50px
        border-radius: 200px
        +sc-transition

    img
        width: 20px
        height: 20px
        pointer-events: none
