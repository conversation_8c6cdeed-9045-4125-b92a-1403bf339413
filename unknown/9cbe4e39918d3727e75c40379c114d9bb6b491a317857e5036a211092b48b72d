<?php

namespace App\Services\Admin\Company;

use App\Models\Company;
use Illuminate\Pagination\LengthAwarePaginator;

interface ICompanyService
{
    public function store(array $data);
    public function update(array $data, Company $company);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator;
    public function delete(Company $company);

}
