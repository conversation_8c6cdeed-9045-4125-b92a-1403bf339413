@extends('layouts.app')
@php
    $title = '';
    $redirect = '';
    $form_address = '';
    $type = $customer->type;
    if ($customer->type === 'b2c') {
        $title = 'B2c customer';
        $redirect = 'admin.customers.b2c.index';
        $form_address = 'admin.customers.b2c.update';
    } elseif ($customer->type === 'studio') {
        $title = 'studio';
        $redirect = 'admin.customers.studio.index';
        $form_address = 'admin.customers.studio.update';
    } elseif ($customer->type === 'master-trainer') {
        $title = 'master tainer';
    } else {
        $title = 'trainer';
    }
@endphp
@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('EDIT Profile') }}</h3>
            <a href="{{ url()->previous() }}" class="back-link">← Back</a>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="mb-6">
        <form method="POST" action="{{ route('studio.profile.update', $customer) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <input type="hidden" name="type" value="studio">
            <h5 class="form-section-title first-title">{{ __('profile photo') }}</h5>
            <div style="display: none">
                @include('partials.forms.radio-group', [
                    'field_name' => 'is_active',
                    'field_label' => 'Radio group status',
                    'values' => [
                        'status_active' => ['text' => 'Active', 'value' => '1'],
                        'status_inactive' => ['text' => 'Inactive', 'value' => '0'],
                    ],
                    'field' => 'status',
                    'required' => 'required',
                    'checked' => $customer->owner->is_active,
                ])
            </div>
            <div class="d-flex align-items-center">
                @php
                    $settings = \App\Models\AdminSettings::first();
                    $user = Auth::user();
                @endphp
                <input type="file" id="avatar" name="avatar" accept="image/*" style="display:none;" />
                <img id="avatarPreview" src="{{ URL::asset('/storage/' . $user->avatar) }}" height="80px" width="80px"
                    alt="" style="border-radius:50%; margin-right:30px; cursor:pointer;" class="img-fluid"
                    onclick="document.getElementById('avatar').click();" />
                <div style="">
                    <p class="my-0 text-secondary">Max. file size is 1MB. Supported formats: PNG/JPG.</p>
                    <p class="my-0 text-secondary">Desirable size: 1080px * 1080px.</p>
                </div>
            </div>

            <h5 class="form-section-title">{{ __('account info') }}</h5>
            @include('partials.forms.input', [
                'field_name' => 'customer_name',
                'field_label' => 'STUDIO NAME *',
                'field_type' => 'text',
                'field_value' => $customer->name,
            ])

            @include('partials.forms.input', [
                'field_name' => 'owner_first_name',
                'field_label' => 'FIRST NAME (LICENSEE) *',
                'field_type' => 'text',
                'field_value' => $customer->owner->first_name,
            ])

            @include('partials.forms.input', [
                'field_name' => 'owner_last_name',
                'field_label' => 'LAST NAME (LICENSEE) *',
                'field_type' => 'text',
                'field_value' => $customer->owner->last_name,
            ])

            @include('partials.forms.input', [
                'field_name' => 'phone',
                'field_label' => 'PHONE #',
                'field_type' => 'text',
                'field_value' => $customer->owner->phone,
            ])

            @include('partials.forms.input', [
                'field_name' => 'email',
                'field_label' => 'EMAIL ADDRESS (USERNAME) *',
                'field_type' => 'email',
                'field_value' => $customer->owner->email,
            ])

            @include('partials.forms.checkbox', [
                'field_name' => 'toggle',
                'field_to_toggle' => 'email2_container',
                'field_label' => 'Another email address',
                'wrap_class_name' => 'mb-5 toggle_bottom',
                'checked' => $customer->owner->email2 != '' ? 'checked' : '',
            ])

            @include('partials.forms.input', [
                'field_name' => 'email2',
                'field_label' => 'EMAIL ADDRESS',
                'field_type' => 'email',
                'field_class' => ($customer->owner->email2 != '' ? '' : 'd-none') . ' email2_container',
                'field_value' => $customer->owner->email2,
            ])

            <h5 class="form-section-title">{{ __('billing address') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'location',
                'field_class' => 'mb-7',
                'field_label' => 'Radio group location',
                'values' => [
                    'location_usa' => ['text' => 'USA', 'value' => 'USA'],
                    'location_international' => ['text' => 'International', 'value' => 'International'],
                ],
                'field' => 'status',
                'required' => 'required',
                'checked' => $customer->location == 'USA' ? 'USA' : 'International',
            ])

            @include('partials.forms.input', [
                'field_name' => 'address',
                'field_label' => 'ADDRESS *',
                'field_type' => 'text',
                'field_value' => $customer->address,
            ])

            @include('partials.forms.input', [
                'field_name' => 'city',
                'field_label' => 'CITY *',
                'field_type' => 'text',
                'field_value' => $customer->city,
            ])

            <div id="location_usa_container"
                style="display: {{ $customer->location == 'International' ? 'none' : 'block' }}">
                @include('partials.forms.select', [
                    'field_name' => 'state_id',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => $customer->state_id,
                    'include_empty' => true,
                ])
            </div>

            @include('partials.forms.input', [
                'field_name' => 'zip',
                'field_label' => 'ZIP CODE',
                'field_type' => 'text',
                'field_value' => $customer->zip,
            ])

            <div id="location_international_container"
                style="display: {{ $customer->location == 'USA' ? 'none' : 'block' }}">
                @include('partials.forms.select', [
                    'field_name' => 'country_id',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'country',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => $customer->country_id,
                    'include_empty' => true,
                ])
            </div>

            <h5 class="form-section-title">{{ 'shipping address' }}</h5>
            @include('partials.forms.checkbox', [
                'field_name' => 'use_billing_address',
                'field_to_toggle' => 'shipping_container',
                'field_label' => 'Use billing address',
                'wrap_class_name' => 'mb-50 toggle_bottom',
                'field_value' => $customer->use_billing_address,
                'checked' =>
                    ($customer->use_billing_address == '' or $customer->use_billing_address == '0')
                        ? ''
                        : 'checked',
            ])
            <div
                class="shipping_container {{ ($customer->use_billing_address == '' or $customer->use_billing_address == '0') ? '' : 'd-none' }}">
                @include('partials.forms.radio-group', [
                    'field_name' => 'shipping_location',
                    'field_class' => 'mb-7',
                    'field_label' => 'Radio group location',
                    'values' => [
                        'shipping_location_usa' => ['text' => 'USA', 'value' => 'USA'],
                        'shipping_location_international' => [
                            'text' => 'International',
                            'value' => 'International',
                        ],
                    ],
                    'field' => 'shipping_location',
                    'required' => 'required',
                    'checked' => $customer->shipping_location == 'USA' ? 'USA' : 'International',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_address',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => $customer->shipping_address,
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_city',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => $customer->shipping_city,
                ])
                @include('partials.forms.input', [
                    'field_name' => 'shipping_zip',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => $customer->shipping_zip,
                ])
                <div id="shipping_location_usa_container"
                    style="display: {{ $customer->shipping_location == 'International' ? 'none' : 'block' }}">
                    @include('partials.forms.select', [
                        'field_name' => 'shipping_state_id',
                        'field_label' => 'STATE',
                        'values' => $states_countries['states'],
                        'field' => 'shipping_state',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => $customer->shipping_state_id,
                        'include_empty' => true,
                    ])
                </div>
                <div id="shipping_location_international_container"
                    style="display: {{ ($customer->shipping_location == 'USA' or $customer->shipping_location == '') ? 'none' : 'block' }}">
                    @include('partials.forms.select', [
                        'field_name' => 'shipping_country_id',
                        'field_label' => 'COUNTRY',
                        'values' => $states_countries['countries'],
                        'field' => 'shipping_country',
                        'option_key' => 'id',
                        'option_label' => 'name',
                        'field_value' => $customer->shipping_country_id,
                        'include_empty' => true,
                    ])
                </div>
            </div>

            <h5 class="form-section-title">{{ __('password management') }}</h5>
            @include('partials.forms.input', [
                'field_name' => 'password',
                'field_label' => 'NEW PASSWORD *',
                'field_type' => 'password',
            ])

            @include('partials.forms.input', [
                'field_name' => 'password_confirmation',
                'field_label' => 'REPEAT NEW PASSWORD *',
                'field_type' => 'password',
            ])

            <div class="cust-pass-text">
                <p>The password field must be at least 8 characters.<br>
                    The password field must contain at least one uppercase and one lowercase letter.</p>
            </div>
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button" href="{{ route($redirect, $customer->id) }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button" data-bs-toggle="modal"
                    data-bs-target="#deleteModal{{ 'Customer' }}{{ $customer->id }}"
                    data-bs-whatever="">{{ __('TERMINATE') }}
                </button>
            </div>
        </form>
    </div>
@endsection
@include('partials.modals.delete', [
    'type' => 'Customer',
    'id' => $customer->id,
    'route' => route('admin.customers.destroy', ['customer' => $customer]),
    'title' => $customer->name,
])
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        function toggleLocationContainers() {
            if ($('#location_international').is(':checked')) {
                $('#location_usa_container').hide();
                $('#location_international_container').show();
            } else if ($('#location_usa').is(':checked')) {
                $('#location_usa_container').show();
                $('#location_international_container').hide();
            }
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        }

        function toggleShippingLocationContainers() {
            if ($('#shipping_location_international').is(':checked')) {
                $('#shipping_location_usa_container').hide();
                $('#shipping_location_international_container').show();
            } else if ($('#shipping_location_usa').is(':checked')) {
                $('#shipping_location_usa_container').show();
                $('#shipping_location_international_container').hide();
            }
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        }

        $('#location_international, #location_usa').on('click', toggleLocationContainers);
        $('#shipping_location_international, #shipping_location_usa').on('click',
            toggleShippingLocationContainers);

        $(document).ready(toggleLocationContainers);
        $(document).ready(toggleLocationContainers);

        document.getElementById('avatar').addEventListener('change', function(event) {
            const [file] = event.target.files;
            if (file) {
                document.getElementById('avatarPreview').src = URL.createObjectURL(file);
            }
        });
    });
</script>
