@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('EDIT notification') }}</h3>
            <a href="{{ route('admin.notifications.index') }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.notifications.update', $notification) }}" id="studio-location-form">
            @csrf
            @method('PUT')

            <div class="">
                <h5 class="form-section-title first-title">{{ __('notification text') }}</h5>

                @include('partials.forms.input', [
                   'field_name' => 'title',
                   'field_label' => 'TITLE *',
                   'field_type' => 'text',
                   'field_value' => $notification->title,
                ])

                <label class="form-label fw-normal mb-3 text-uppercase">{{ __('description *') }}</label>

                @include('partials.forms.textarea', [
                    'field_name' => 'description',
                    'field_label' => 'Enter',
                    'field_type' => 'text',
                    'field_value' => $notification->description,
                    'field_label_class' => 'd-none',
                    'render_max_char' => true,
                    'max_chars' => 200,
                ])
            </div>

            <h5 class="form-section-title">{{ __('notification details') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'link',
                'field_label' => 'NOTIFICATION LINK',
                'field_type' => 'text',
                'field_value' => $notification?->link,
            ])

            @include('partials.forms.input',  [
                'field_name' => 'published_at',
                'field_label' => 'DATE PUBLISHED',
                'field_value' => ($notification->published_at) ? \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $notification->published_at)->format('Y-m-d') : null,
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
            ])


            <h5 class="form-section-title">{{ __('studio') }}</h5>

            @include('partials.forms.select', [
               'field_name' => 'customer_id',
               'field_label' => 'CUSTOMER',
               'values' => $customers,
               'field' => 'studio',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('customer_id', $notification->customer_id),
               'include_empty' => true
           ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.notifications.edit', $notification)}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'Notification'}}{{$notification->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>
    @include('partials.modals.delete', [
       'id' => $notification->id,
       'type' => 'Notification',
       'route' => route('admin.notifications.delete', $notification),
       'title' => 'Notification',
   ])

@endsection
