<?php

namespace App\Http\Controllers;

use App\Services\NotificationService\MarkAsReadNotificationService;
use App\Services\NotificationService\ReadNotificationDataService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    public function markAsReadNotification(
        Request                       $request,
        MarkAsReadNotificationService $notification,
        ReadNotificationDataService   $readNotificationDataService
    ): JsonResponse
    {
        $notification->markAsRead($request->get('notification_id'));
        $stickyNotifications = $readNotificationDataService->readNotificationFromAdmin(auth()->user()->id);

        return response()->json(['message' => 'Notification marked as read']);
    }

    public function markAsReadNewClientsNotification(
        Request                       $request,
        MarkAsReadNotificationService $notification,
        ReadNotificationDataService   $readNotificationDataService
    ): JsonResponse
    {
        $notification->markAsRead($request->get('clientId'));

        $newClient = $readNotificationDataService->readNotificationNewClientRegister(auth()->user()->id);

        $viewContent = view('partials.forms.new-client-register-notifications', compact('newClient'))->render();

        toastr()->addSuccess('', 'Client notifications marked as read.');
        return response()->json($viewContent);
    }

    public function markAsReadAll(MarkAsReadNotificationService $notification)
    {
        $notification->markAsReadAll(auth()->user()->id);
        toastr()->addSuccess('', 'All notifications marked as read.');
        return response()->json(['message' => 'Notifications read']);
    }
}
