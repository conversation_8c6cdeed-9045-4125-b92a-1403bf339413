<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Request</title>
    <style>
        /* Add your custom styling here */
        body {
            font-family: Graphik, sans-serif;
            font-size: 12px;
            margin-top: 10px;
            margin-left: 30px;
            margin-right: 30px;
        }
        .header-table, .items-table, .note-table {
            width: 100%;
            border-collapse: collapse;
        }
        .header-table td, .items-table th, .items-table td, .note-table td {
            padding: 8px;
        }
        .logo-cell {
            width: 20%;
            text-align: left;
        }
        .logo-cell img {
            width: 100px;
            height: 100px;
            border-radius: 50%;
        }
        .items-table {
            margin-top: 35px;
            /*border: 1px solid #ddd;*/
        }
        .items-table tr {
            border-collapse: collapse
        }
        .items-table th, .items-table td {
            border-collapse: collapse
            border-top: 1px solid #F0F0F0;
            border-bottom: 1px solid #F0F0F0;
            padding-bottom: 15px;
            padding-top: 15px;
            text-align: left;
        }

        .items-table th:nth-child(1), .items-table td:nth-child(1) {
            width: 20px; /* Set width for the ID column */
        }

        .items-table th:nth-child(2), .items-table td:nth-child(2) {
            width: auto; /* Let the Item column take up the remaining space */
        }

        .items-table th:nth-child(3), .items-table td:nth-child(3) {
            width: 130px; /* Set width for the Price column */
            text-align: right; /* Align Price column to the right */

        }
        .items-table th:nth-child(4), .items-table td:nth-child(4) {
            width: 50px; /* Set width for the Quantity column */
            text-align: center; /* Align Quantity column to the right */

        }
        .items-table th:nth-child(5), .items-table td:nth-child(5) {
            width: 30px; /* Set width for the Total column */
            text-align: center; /* Align Total column to the right */

        }

        .note-table {
            margin-top: 30px;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .note-table td {
            padding: 10px;
            vertical-align: top;
            overflow-wrap: break-word;
            word-break: break-word;
            max-width: 100%;
            min-height: 50px; /* Adjust as needed */
            overflow: auto;
        }

        .note {
            font-size: 10px;
            white-space: pre-wrap;
        }

        .page-break {
            page-break-after: always;
        }
        .light-line {
            width: 100%;
            border-bottom: 1px solid #F0F0F0;
        }
    </style>
</head>
<body>
<!-- Header Section -->
<table class="header-table">
    <tr>
        <td class="logo-cell">
            <img src="{{$order->company?->image?->path ? public_path('/storage/' . $order->company->image->path) : public_path('/img_placeholder.svg') }}" alt="Company Logo">
            {{-- <img src="{{$order->company?->image?->path ? URL::asset('/storage/' . $order->company->image->path) : URL::asset('/img_placeholder.svg') }}" alt="Company Logo"> --}}
        </td>
        <td class="info-cell">
            @if($order->company)
            <strong style="line-height: 1.6;">{{$order->company->name}}</strong><br>
            <span style="line-height: 1.6;">{{ $order->company->address }}</span><br>
            <span style="line-height: 1.6;">{{ $order->company->phone }}</span>
            @endif
        </td>
        <td style="text-align:right;">
            <strong>PURCHASE ORDER #{{ $order->formatted_order_number }}</strong><br>
            <span style="font-size: 80%">DATE: {{ $order->sent_at }}</span>
        </td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 30px">
    <tr>
        <td></td>
    </tr>
</table>

<table class="company-table">
    <tr>
        <td>
            <strong style="line-height: 1.4;font-size: 16px;">{{ $order->supplier->name }}</strong><br>
            <span style="line-height: 1.4;font-size: 16px;">{{ $order->supplier->address }}</span><br>
            <span style="line-height: 1.4;font-size: 16px;">{{ $order->supplier->phone }}</span>
        </td>
        <td></td>
    </tr>
</table>

<table class="light-line" style="margin-bottom: 35px; margin-top: 30px">
    <tr>
        <td></td>
    </tr>
</table>

<h4>ORDER ITEMS</h4>

<!-- Order Items Section -->
<table class="items-table">
    <thead>
    <tr>
        <th>ID</th>
        <th>Item</th>
        <th>SUPPLIER PRICE</th>
        <th>QTY</th>
        <th>TOTAL</th>
    </tr>
    </thead>
    <tbody>
    @foreach($order->items as $item)
        <tr>
            <td>{{ $loop->iteration }}</td>
            <td>{{ $item->product['name'] }}</td>
            <td>${{ number_format($item->price, 2, '.', ',') }}</td>
            <td>{{ $item->quantity }}</td>
            <td>{{ number_format(($item->price * $item->quantity), 2, '.', ',') }}</td>
        </tr>
        @if($loop->iteration % 9 == 0 && $loop->remaining > 0)
    </tbody>
</table>
<div class="page-break"></div>
<table class="items-table">
    <thead>
        <tr>
            <th>ID</th>
            <th>Item</th>
            <th>SUPPLIER PRICE</th>
            <th>QTY</th>
            <th>TOTAL</th>
        </tr>
    </thead>
    <tbody>
    @endif
    @endforeach
    <tr>
        <td style="text-align: right;font-size: 14px;padding-top: 30px;padding-bottom: 30px" colspan="5"><strong>Total: ${{ $order->total }}</strong></td>
    </tr>
    </tbody>
</table>

@if($order->note)
    <table class="invoice-table" style="margin-top: 50px">
        <tr>
            <td class="header" style="padding-bottom: 10px"><b>NOTE</b></td>
        </tr>
        <tr>
            <td class="terms" style="line-height: 1.3">
                {{$order->note}}
            </td>
        </tr>
    </table>
@endif

</body>
</html>
