<?php

namespace App\Helpers;

use App\Models\File;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileHelper
{
    public static function uploadImage(object $model, string $type, string $path, ?File $fileData = null, ?UploadedFile $file = null, int $position = null): void
    {
        self::deleteImage($fileData);
        if ($file) {
            $hashName = self::hash($file->getClientOriginalName(), $file->getClientOriginalExtension());
            $file->storeAs($path, $hashName, 'public');
            if ($fileData === null) {
                $fileData = new File;
            }
            $fileData->fill([
                'name' => $file->getClientOriginalName(),
                'extension' => $file->getClientOriginalExtension(),
                'type' => $type,
                'hash_name' => $hashName,
                'path' => $path.'/'.$hashName,
            ]);
            $model->$type()->save($fileData);
        }
    }

    public static function hash(string $originalName, string $originalExtension): string
    {
        return uniqid() . '_' . Str::random(10) . '.' . $originalExtension;
    }

    public static function deleteImage(?object $model): void
    {
        if (isset($model)) {
            Storage::disk('public')->delete($model->path);
        }
    }

    public static function deleteFiles(array $ids): void
    {
        foreach ($ids as $id) {
            self::deleteSingleFile($id);
        }
    }

    public static function deleteSingleFile(int $id): void
    {
        $file = File::find($id);
        self::deleteImage($file);
        $file->delete();
    }
}

