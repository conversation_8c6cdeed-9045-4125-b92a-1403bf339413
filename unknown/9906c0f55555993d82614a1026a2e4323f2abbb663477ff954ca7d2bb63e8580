<?php

namespace App\Services\Admin\PaymentHistory;

use App\Helpers\Constants;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;

class PaymentHistoryService implements IPaymentHistoryService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $customer_id): LengthAwarePaginator
    {
        $query = Payment::with('lease.machine', 'license.studio')->where('customer_id', $customer_id);
        // $query = Payment::with('lease.machine', 'license.studio')->where('customer_id', $customer_id)->where('description', '!=', 'Deposit');
        $query->where(function ($q) {
            $q->where('status', Constants::PAYMENT_STATUS['paid'])
              ->orWhere('status', Constants::PAYMENT_STATUS['declined'])
              ->orWhere(function ($subQuery) {
                $subQuery->where('status', Constants::PAYMENT_STATUS['not_paid'])
                         ->where('payment_date', '<', Carbon::now());
              })
              ->orWhere('payment_date', '<', Carbon::now());
        });
        $perPage = ($perPage != 0) ? $perPage : $query->count();
        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('status', $status);
        });
        if ($searchData !== '') {
            $query->where(function ($q) use ($searchData) {
                $q->whereHas('lease.machine', function ($q) use ($searchData) {
                    $q->where('name', 'LIKE', '%' . $searchData . '%');
                })->orWhereHas('license.studio', function ($q) use ($searchData) {
                    $q->where('name', 'LIKE', '%' . $searchData . '%');
                });
            });
        }
        $query->orderBy($orderParam, $orderType);
        return $query->paginate($perPage);
    }
}
