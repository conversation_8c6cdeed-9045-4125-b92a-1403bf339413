<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!--<title>{{ config('app.name', 'ERP') }}</title>-->
    <title>Lagree IMS</title>
    <link rel="icon" type="image/x-icon" href="/favicon.jpg">

    <!-- Scripts -->
    @vite(['resources/sass/app.sass', 'resources/css/custom.css', 'resources/js/app.js'])
    <style id="theme-color-settings">
        .select2-container--default .select2-selection--single .select2-selection__arrow b {
            content: url("/select_arrow.svg");
            width: 8px;
            height: 5px;
        }

        .zIndex-10000.position-relative .select2-container--default .select2-selection--single .select2-selection__arrow b {
            content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='5' viewBox='0 0 8 5'%3E%3Cpath id='Path_7017' data-name='Path 7017' d='M0,0,4,5,8,0Z' transform='translate(0 0)' fill='%23969696'/%3E%3C/svg%3E%0A");
            width: 8px;
            height: 5px;
            margin-top: 2px;
        }


        @media(max-width: 767px) {
            .checkbox-group .list-group-item {
                padding-right: 0;
            }

            .nav-tabs .nav-item .nav-link.active {
                position: relative;
            }

            .nav-tabs .nav-item .nav-link.active::before {
                content: "";
                position: absolute;
                top: 45px;
                height: 2px;
                background: #000;
                width: 100%;
                left: 0;
            }

            button,
            .btn {
                text-transform: uppercase !important;
            }
        }

        @media(max-width: 1024px) {
            .spots>div.d-flex {
                gap: 15px !important;
            }
        }
    </style>
    <script>
        window.markerConfig = {
            project: '67adb184e1e70a6b4b8ee405',
            source: 'snippet'
        };
        ! function(e, r, a) {
            if (!e.__Marker) {
                e.__Marker = {};
                var t = [],
                    n = {
                        __cs: t
                    };
                ["show", "hide", "isVisible", "capture", "cancelCapture", "unload", "reload", "isExtensionInstalled",
                    "setReporter", "clearReporter", "setCustomData", "on", "off"
                ].forEach(function(e) {
                    n[e] = function() {
                        var r = Array.prototype.slice.call(arguments);
                        r.unshift(e), t.push(r)
                    }
                }), e.Marker = n;
                var s = r.createElement("script");
                s.async = 1, s.src = "https://edge.marker.io/latest/shim.js";
                var i = r.getElementsByTagName("script")[0];
                i.parentNode.insertBefore(s, i)
            }
        }(window, document);
    </script>
</head>

<body class="font-sans antialiased">
    <div id="app">
        <main>
            <div class="d-flex">
                @php
                    use Illuminate\Support\Facades\Auth;
                    use Illuminate\Support\Facades\DB;
                    $roles = [];
                    if (Auth::check()) {
                        $roles = DB::table('model_has_roles')
                            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                            ->where('model_has_roles.model_id', Auth::user()->id)
                            ->pluck('roles.name');
                    }
                @endphp
                @if ($roles[0] === 'super-admin')
                    @include('sidebar')
                @else
                    @include('customer-sidebar')
                @endif
                <div class="main-content w-100">
                    @include('header')

                    <div class="container pb-8 mb-8 pb-sm-11 mb-sm-8 pt-9 pt-sm-0">
                        @yield('content')
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // let weekStart = '{{ $admin_week_start ?? NULL }}'
        let weekStart = '{{ $admin_week_start ?? 0 }}'
        document.addEventListener('DOMContentLoaded', () => {
            const currentPath = window.location.pathname;
            //let weekStart = {{$weekStart ?? 0}};
            // Keep dashboardActiveTab only if the path starts with /admin/customers-dashboard
            if (!currentPath.startsWith('/admin/customers-dashboard')) {
                localStorage.removeItem('dashboardActiveTab');
            }
        });
    </script>
    <script src="https://js.stripe.com/basil/stripe.js"></script>

    @stack('scripts')
    @include('partials.js-links')
</body>

</html>
