<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Currency::create([
            'name'   => 'US Dollar',
            'code'   => 'USD',
            'symbol' => '$',
            'rate'   => 1.00,
        ]);

        Currency::create([
            'name'   => 'Euro',
            'code'   => 'EUR',
            'symbol' => '€',
            'rate'   => 0.85,
        ]);
    }
}
