<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class UniqueCustomerEmailPerType implements ValidationRule
{
    protected $ownerId;
    protected $type;

    public function __construct($ownerId, $type)
    {
        $this->ownerId = $ownerId;
        $this->type = $type;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $userId = DB::table('users')
            ->where('email', $value)
            ->pluck('id');
        if (!$userId) {
            return;
        }
        $exists = DB::table('customers')
            ->whereIn('owner_id', $userId)
            ->where('type', $this->type)
            ->exists();
        if ($exists) {
            throw ValidationException::withMessages([
                $attribute => "A user with this email already exists as a '{$this->type}' customer."
            ]);
        }
    }
}
