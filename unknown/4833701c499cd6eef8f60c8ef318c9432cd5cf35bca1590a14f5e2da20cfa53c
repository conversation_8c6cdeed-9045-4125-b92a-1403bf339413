<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use App\Models\OrderItem;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    protected $fillable = [
        'company_id',
        'supplier_id',
        'sent_at',
        'status',
        'note'
    ];

    protected $appends = [
        'total',
    ];

    public function getTotalAttribute(): string
    {
        $items = OrderItem::where('order_id', $this->id)->get();
        // dd($items);

        $total = 0;
        if($items->count() > 0){
            foreach ($items as $item) {
                $total += $item->price * $item->quantity;
            }
        }
        return number_format($total, 2);
    }

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'supplier'   => ['id', 'orders.supplier_id'],
    ];

    public function getPrimaryAndForeignKeys(string $relation): array
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function sortableFields(): array
    {
        return [
            'id',
            'created_at',
            'updated_at',
            'sent_at',
            'supplier.name',
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'id';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function getFormattedOrderNumberAttribute(): string
    {
        return str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }
}
