<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->string('paymentIntent_id')->after('stripe_session_id')->nullable();
            $table->string('stripePayment_id')->after('stripe_session_id')->nullable();
            $table->string('stripeCustomer_id')->after('stripe_session_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('paymentIntent_id');
            $table->dropColumn('stripePayment_id');
            $table->dropColumn('stripeCustomer_id');
        });
    }
};
