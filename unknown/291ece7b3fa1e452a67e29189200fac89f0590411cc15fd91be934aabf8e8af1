<?php

namespace App\Services\NotificationService;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ReadNotificationDataService
{
    /**
     * @param $userId
     * @return Collection
     */
    public function readNotificationFromAdmin($userId): Collection
    {
        return DB::table('notifications')
            ->whereNull('read_at')
            ->where('notifiable_id', $userId)
            ->where('notifiable_type', 'App\Models\User')
            ->where('type', 'App\Notifications\AdminMessage')
            ->get();
    }

    /**
     * @param $userId
     * @return Collection
     */
    public function readNotificationNewClientRegister($userId): Collection
    {
        return DB::table('notifications')
            ->whereNull('read_at')
            ->where('notifiable_id', $userId)
            ->where('notifiable_type', 'App\Models\User')
            ->where('type', 'App\Notifications\NewClientRegister')
            ->get();
    }
}
