<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Products extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'price',
        'category',
        'supplier_price',
        'supplier_id',
        'stock',
        'wp_id'

    ];

    protected $appends = [
        'profit'
    ];

    public function getProfitAttribute(): string
    {
        $price = $this->price ?? 0;
        $supplierPrice = $this->supplier_price ?? 0;
        return str_pad($price - $supplierPrice, 4, '0', STR_PAD_LEFT);
    }

    public function invoiceProduct(): BelongsTo
    {
        return $this->belongsTo(InvoiceProductItem::class);
    }
}
