<?php

namespace App\Mail;

use App\Models\AdminSettings;
use App\Models\Order;
use Barryvdh\DomPDF\PDF;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Attachment;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendOrderToSupplier extends Mailable
{
    use Queueable, SerializesModels;

    private PDF $pdf;
    private Order $order;

    /**
     * Create a new message instance.
     */
    public function __construct(PDF $pdf, Order $order)
    {
        $this->pdf = $pdf;
        $this->order = $order;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $settings = AdminSettings::first();
        return new Envelope(
            from: new Address($settings->smtp_from_email, $settings->smtp_from_name),
            subject: 'Order ' . $this->order->formatted_order_number,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.send-order-to-supplier',
            with: ['order' => $this->order],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [
            Attachment::fromData(fn() => $this->pdf->output(), 'order-' . $this->order->formatted_order_number . '.pdf')
                ->withMime('application/pdf'),
        ];
    }
}
