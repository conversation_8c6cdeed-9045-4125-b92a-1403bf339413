-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jan 27, 2025 at 02:39 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `lagree_erp`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_notifications`
--

CREATE TABLE `admin_notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_notifications`
--

INSERT INTO `admin_notifications` (`id`, `customer_id`, `title`, `description`, `link`, `published_at`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 'First text', 'This is very first notification.', 'http://somelink.com', '2024-12-26 00:00:00', NULL, '2024-12-26 06:12:51', '2024-12-26 06:12:51');

-- --------------------------------------------------------

--
-- Table structure for table `admin_settings`
--

CREATE TABLE `admin_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `admin_id` bigint(20) UNSIGNED DEFAULT NULL,
  `currency_id` bigint(20) UNSIGNED DEFAULT NULL,
  `timezone` varchar(255) DEFAULT NULL,
  `date_format` varchar(255) DEFAULT NULL,
  `time_format` varchar(255) DEFAULT NULL,
  `week_start` varchar(255) DEFAULT NULL,
  `seo_title` varchar(255) DEFAULT NULL,
  `seo_description` varchar(255) DEFAULT NULL,
  `smtp_from_name` varchar(255) DEFAULT NULL,
  `smtp_from_email` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_settings`
--

INSERT INTO `admin_settings` (`id`, `admin_id`, `currency_id`, `timezone`, `date_format`, `time_format`, `week_start`, `seo_title`, `seo_description`, `smtp_from_name`, `smtp_from_email`, `logo`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'America/Los_Angeles', 'F j, Y', 'g:i A', 'sunday', NULL, NULL, 'Lagree Fitness (IMS)', '<EMAIL>', 'instructors/1-03CEqE6uvnl4wxBau44yB2b2NlBxJmfE7qDR9WT8.jpg', NULL, '2024-12-12 11:46:22', '2025-01-16 02:19:16'),
(2, 21, 1, 'America/Los_Angeles', 'F j, Y', 'g:i A', 'sunday', NULL, NULL, 'Lagree Fitness (IMS)', '<EMAIL>', 'instructors/1-i0DNWVXcRGjB9wFoabCScESMpWJxWVE0Wv0nD67o.jpg', NULL, '2024-12-12 11:46:22', '2025-01-08 05:52:51');

-- --------------------------------------------------------

--
-- Table structure for table `agreements`
--

CREATE TABLE `agreements` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `adobe_template_id` varchar(255) NOT NULL,
  `adobe_agreement_id` varchar(255) NOT NULL,
  `send_date` date DEFAULT NULL,
  `singing_date` date DEFAULT NULL,
  `completed_date` date DEFAULT NULL,
  `filepath` varchar(255) DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `companies`
--

CREATE TABLE `companies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `owner_first_name` varchar(255) DEFAULT NULL,
  `owner_last_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `companies`
--

INSERT INTO `companies` (`id`, `state_id`, `name`, `owner_first_name`, `owner_last_name`, `email`, `phone`, `address`, `city`, `zip`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 5, 'Maximum Fitness', NULL, NULL, '<EMAIL>', '(*************', '9340 Eton Ave, Chatsworth, CA 91311', 'Chatsworth', '91311', 1, '2024-12-12 11:46:22', '2024-12-29 23:58:19', NULL),
(2, 5, 'Lagree Fitness', NULL, NULL, NULL, '************', '9340 Eton Ave, Chatsworth, CA 91311', NULL, NULL, 1, '2024-12-12 11:46:22', '2025-01-26 04:49:30', NULL),
(3, 5, 'SPX Fitness', NULL, NULL, NULL, NULL, '9340 Eton Ave, Chatsworth, CA 91311', NULL, NULL, 1, '2024-12-12 11:46:22', '2024-12-12 11:46:22', NULL),
(4, 14, 'Svercer Trade', NULL, NULL, '<EMAIL>', '+****************', NULL, NULL, NULL, 1, '2025-01-03 00:49:21', '2025-01-09 05:51:21', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `contact_people`
--

CREATE TABLE `contact_people` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `contactable_type` varchar(255) NOT NULL,
  `contactable_id` bigint(20) NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `position` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_people`
--

INSERT INTO `contact_people` (`id`, `contactable_type`, `contactable_id`, `first_name`, `last_name`, `position`, `phone`, `email`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\Supplier', 1, 'Vo Cong', 'Hai', 'CEO', '+84 38 4545 777', '<EMAIL>', NULL, '2024-12-23 04:55:30', '2024-12-23 04:55:59'),
(2, 'App\\Models\\Supplier', 2, 'Ivy', 'Barrett', 'Animi dignissimos i', '+****************', '<EMAIL>', NULL, '2025-01-03 00:50:19', '2025-01-26 07:54:07'),
(3, 'App\\Models\\Supplier', 3, 'Quinton', 'Pouros', 'Quis hic fugit exercitationem ipsa fugiat.', '************', '<EMAIL>', NULL, '2025-01-08 11:28:47', '2025-01-08 11:28:47'),
(4, 'App\\Models\\Supplier', 4, 'asdasd', 'asdasd', 'asdasd', '123123', '<EMAIL>', NULL, '2025-01-09 05:47:32', '2025-01-09 05:47:32');

-- --------------------------------------------------------

--
-- Table structure for table `conversion_rates`
--

CREATE TABLE `conversion_rates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `rateable_type` varchar(255) NOT NULL,
  `rateable_id` bigint(20) NOT NULL,
  `currency_id` bigint(20) UNSIGNED NOT NULL,
  `rate` decimal(8,4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `conversion_rates`
--

INSERT INTO `conversion_rates` (`id`, `rateable_type`, `rateable_id`, `currency_id`, `rate`, `created_at`, `updated_at`) VALUES
(3, 'App\\Models\\License', 1, 1, 1.0000, '2024-12-16 10:04:33', '2024-12-16 10:04:33'),
(4, 'App\\Models\\License', 1, 2, 1.1765, '2024-12-16 10:04:33', '2024-12-16 10:04:33'),
(5, 'App\\Models\\License', 2, 1, 1.0000, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(6, 'App\\Models\\License', 2, 2, 1.1765, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(7, 'App\\Models\\Lease', 1, 1, 1.0000, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(8, 'App\\Models\\Lease', 1, 2, 1.1765, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(11, 'App\\Models\\License', 4, 1, 1.0000, '2024-12-30 07:19:03', '2024-12-30 07:19:03'),
(12, 'App\\Models\\License', 4, 2, 1.1765, '2024-12-30 07:19:03', '2024-12-30 07:19:03'),
(13, 'App\\Models\\License', 5, 1, 1.0000, '2024-12-30 08:30:06', '2024-12-30 08:30:06'),
(14, 'App\\Models\\License', 5, 2, 1.1765, '2024-12-30 08:30:06', '2024-12-30 08:30:06'),
(15, 'App\\Models\\License', 3, 1, 1.0000, '2024-12-30 08:30:43', '2024-12-30 08:30:43'),
(16, 'App\\Models\\License', 3, 2, 1.1765, '2024-12-30 08:30:43', '2024-12-30 08:30:43');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Afghanistan', NULL, NULL),
(2, 'Albania', NULL, NULL),
(3, 'Algeria', NULL, NULL),
(4, 'Andorra', NULL, NULL),
(5, 'Angola', NULL, NULL),
(6, 'Antigua and Barbuda', NULL, NULL),
(7, 'Argentina', NULL, NULL),
(8, 'Armenia', NULL, NULL),
(9, 'Australia', NULL, NULL),
(10, 'Austria', NULL, NULL),
(11, 'Azerbaijan', NULL, NULL),
(12, 'Bahamas', NULL, NULL),
(13, 'Bahrain', NULL, NULL),
(14, 'Bangladesh', NULL, NULL),
(15, 'Barbados', NULL, NULL),
(16, 'Belarus', NULL, NULL),
(17, 'Belgium', NULL, NULL),
(18, 'Belize', NULL, NULL),
(19, 'Benin', NULL, NULL),
(20, 'Bhutan', NULL, NULL),
(21, 'Bolivia', NULL, NULL),
(22, 'Bosnia and Herzegovina', NULL, NULL),
(23, 'Botswana', NULL, NULL),
(24, 'Brazil', NULL, NULL),
(25, 'Brunei', NULL, NULL),
(26, 'Bulgaria', NULL, NULL),
(27, 'Burkina Faso', NULL, NULL),
(28, 'Burundi', NULL, NULL),
(29, 'Cambodia', NULL, NULL),
(30, 'Cameroon', NULL, NULL),
(31, 'Canada', NULL, NULL),
(32, 'Central African Republic', NULL, NULL),
(33, 'Chad', NULL, NULL),
(34, 'Chile', NULL, NULL),
(35, 'China', NULL, NULL),
(36, 'Colombia', NULL, NULL),
(37, 'Comoros', NULL, NULL),
(38, 'Congo (Brazzaville)', NULL, NULL),
(39, 'Congo (Kinshasa)', NULL, NULL),
(40, 'Costa Rica', NULL, NULL),
(41, 'Côte d\'Ivoire', NULL, NULL),
(42, 'Croatia', NULL, NULL),
(43, 'Cuba', NULL, NULL),
(44, 'Cyprus', NULL, NULL),
(45, 'Czech Republic', NULL, NULL),
(46, 'Denmark', NULL, NULL),
(47, 'Djibouti', NULL, NULL),
(48, 'Dominica', NULL, NULL),
(49, 'Dominican Republic', NULL, NULL),
(50, 'Ecuador', NULL, NULL),
(51, 'Egypt', NULL, NULL),
(52, 'El Salvador', NULL, NULL),
(53, 'Equatorial Guinea', NULL, NULL),
(54, 'Eritrea', NULL, NULL),
(55, 'Estonia', NULL, NULL),
(56, 'Ethiopia', NULL, NULL),
(57, 'Fiji', NULL, NULL),
(58, 'Finland', NULL, NULL),
(59, 'France', NULL, NULL),
(60, 'Gabon', NULL, NULL),
(61, 'Gambia', NULL, NULL),
(62, 'Georgia', NULL, NULL),
(63, 'Germany', NULL, NULL),
(64, 'Ghana', NULL, NULL),
(65, 'Greece', NULL, NULL),
(66, 'Grenada', NULL, NULL),
(67, 'Guatemala', NULL, NULL),
(68, 'Guinea', NULL, NULL),
(69, 'Guinea-Bissau', NULL, NULL),
(70, 'Guyana', NULL, NULL),
(71, 'Haiti', NULL, NULL),
(72, 'Honduras', NULL, NULL),
(73, 'Hungary', NULL, NULL),
(74, 'Iceland', NULL, NULL),
(75, 'India', NULL, NULL),
(76, 'Indonesia', NULL, NULL),
(77, 'Iran', NULL, NULL),
(78, 'Iraq', NULL, NULL),
(79, 'Ireland', NULL, NULL),
(80, 'Israel', NULL, NULL),
(81, 'Italy', NULL, NULL),
(82, 'Jamaica', NULL, NULL),
(83, 'Japan', NULL, NULL),
(84, 'Jordan', NULL, NULL),
(85, 'Kazakhstan', NULL, NULL),
(86, 'Kenya', NULL, NULL),
(87, 'Kiribati', NULL, NULL),
(88, 'North Korea', NULL, NULL),
(89, 'South Korea', NULL, NULL),
(90, 'Kosovo', NULL, NULL),
(91, 'Kuwait', NULL, NULL),
(92, 'Kyrgyzstan', NULL, NULL),
(93, 'Laos', NULL, NULL),
(94, 'Latvia', NULL, NULL),
(95, 'Lebanon', NULL, NULL),
(96, 'Lesotho', NULL, NULL),
(97, 'Liberia', NULL, NULL),
(98, 'Libya', NULL, NULL),
(99, 'Liechtenstein', NULL, NULL),
(100, 'Lithuania', NULL, NULL),
(101, 'Luxembourg', NULL, NULL),
(102, 'Macedonia', NULL, NULL),
(103, 'Madagascar', NULL, NULL),
(104, 'Malawi', NULL, NULL),
(105, 'Malaysia', NULL, NULL),
(106, 'Maldives', NULL, NULL),
(107, 'Mali', NULL, NULL),
(108, 'Malta', NULL, NULL),
(109, 'Marshall Islands', NULL, NULL),
(110, 'Mauritania', NULL, NULL),
(111, 'Mauritius', NULL, NULL),
(112, 'Mexico', NULL, NULL),
(113, 'Micronesia', NULL, NULL),
(114, 'Moldova', NULL, NULL),
(115, 'Monaco', NULL, NULL),
(116, 'Mongolia', NULL, NULL),
(117, 'Montenegro', NULL, NULL),
(118, 'Morocco', NULL, NULL),
(119, 'Mozambique', NULL, NULL),
(120, 'Myanmar (Burma)', NULL, NULL),
(121, 'Namibia', NULL, NULL),
(122, 'Nauru', NULL, NULL),
(123, 'Nepal', NULL, NULL),
(124, 'Netherlands', NULL, NULL),
(125, 'New Zealand', NULL, NULL),
(126, 'Nicaragua', NULL, NULL),
(127, 'Niger', NULL, NULL),
(128, 'Nigeria', NULL, NULL),
(129, 'Norway', NULL, NULL),
(130, 'Oman', NULL, NULL),
(131, 'Pakistan', NULL, NULL),
(132, 'Palau', NULL, NULL),
(133, 'Panama', NULL, NULL),
(134, 'Papua New Guinea', NULL, NULL),
(135, 'Paraguay', NULL, NULL),
(136, 'Peru', NULL, NULL),
(137, 'Philippines', NULL, NULL),
(138, 'Poland', NULL, NULL),
(139, 'Portugal', NULL, NULL),
(140, 'Qatar', NULL, NULL),
(141, 'Romania', NULL, NULL),
(142, 'Russia', NULL, NULL),
(143, 'Rwanda', NULL, NULL),
(144, 'Saint Kitts and Nevis', NULL, NULL),
(145, 'Saint Lucia', NULL, NULL),
(146, 'Saint Vincent and the Grenadines', NULL, NULL),
(147, 'Samoa', NULL, NULL),
(148, 'San Marino', NULL, NULL),
(149, 'Sao Tome and Principe', NULL, NULL),
(150, 'Saudi Arabia', NULL, NULL),
(151, 'Senegal', NULL, NULL),
(152, 'Serbia', NULL, NULL),
(153, 'Seychelles', NULL, NULL),
(154, 'Sierra Leone', NULL, NULL),
(155, 'Singapore', NULL, NULL),
(156, 'Sint Maarten', NULL, NULL),
(157, 'Slovakia', NULL, NULL),
(158, 'Slovenia', NULL, NULL),
(159, 'Solomon Islands', NULL, NULL),
(160, 'Somalia', NULL, NULL),
(161, 'South Africa', NULL, NULL),
(162, 'South Sudan', NULL, NULL),
(163, 'Spain', NULL, NULL),
(164, 'Sri Lanka', NULL, NULL),
(165, 'Sudan', NULL, NULL),
(166, 'Suriname', NULL, NULL),
(167, 'Swaziland', NULL, NULL),
(168, 'Sweden', NULL, NULL),
(169, 'Switzerland', NULL, NULL),
(170, 'Syria', NULL, NULL),
(171, 'Tajikistan', NULL, NULL),
(172, 'Tanzania', NULL, NULL),
(173, 'Thailand', NULL, NULL),
(174, 'Timor-Leste', NULL, NULL),
(175, 'Togo', NULL, NULL),
(176, 'Tonga', NULL, NULL),
(177, 'Trinidad and Tobago', NULL, NULL),
(178, 'Tunisia', NULL, NULL),
(179, 'Turkey', NULL, NULL),
(180, 'Turkmenistan', NULL, NULL),
(181, 'Tuvalu', NULL, NULL),
(182, 'Uganda', NULL, NULL),
(183, 'Ukraine', NULL, NULL),
(184, 'United Arab Emirates', NULL, NULL),
(185, 'United Kingdom', NULL, NULL),
(186, 'United States', NULL, NULL),
(187, 'Uruguay', NULL, NULL),
(188, 'Uzbekistan', NULL, NULL),
(189, 'Vanuatu', NULL, NULL),
(190, 'Vatican City', NULL, NULL),
(191, 'Venezuela', NULL, NULL),
(192, 'Vietnam', NULL, NULL),
(193, 'Yemen', NULL, NULL),
(194, 'Zambia', NULL, NULL),
(195, 'Zimbabwe', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `code` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `symbol` varchar(255) NOT NULL,
  `rate` decimal(8,4) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`id`, `code`, `name`, `symbol`, `rate`, `created_at`, `updated_at`) VALUES
(1, 'USD', 'US Dollar', '$', 1.0000, '2024-12-12 11:46:22', '2024-12-12 11:46:22'),
(2, 'EUR', 'Euro', '€', 0.8500, '2024-12-12 11:46:22', '2024-12-12 11:46:22');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `owner_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `owner_id`, `name`, `location`, `deleted_at`, `created_at`, `updated_at`, `state_id`, `country_id`, `address`, `city`, `zip`) VALUES
(1, 2, 'Lagree Studio', 'USA', NULL, '2024-12-12 03:51:42', '2024-12-30 06:37:44', 5, 91, '9340 Eaton Ave', 'Chatsworth', '91311'),
(2, 3, 'Megaformer', 'USA', '2024-12-26 06:39:33', '2024-12-18 07:21:01', '2024-12-26 06:39:33', NULL, NULL, '', '', ''),
(3, 4, 'Company?', 'International', '2024-12-26 06:39:33', '2024-12-21 13:38:09', '2024-12-26 06:39:33', NULL, NULL, '', '', ''),
(4, 5, 'Test Customer', 'USA', '2024-12-26 06:39:33', '2024-12-23 06:06:32', '2024-12-26 06:39:33', NULL, NULL, '', '', ''),
(5, 6, 'JD DEVELOPMENT 5', 'International', NULL, '2024-12-26 07:35:41', '2025-01-03 17:25:31', NULL, 59, 'Siren  ***********, 5, rue Claude Monet', 'BOULOGNE BILLANCOURT', '92100'),
(6, 7, 'Honour Wholeness', 'International', NULL, '2024-12-26 11:10:13', '2024-12-31 03:20:55', 5, 140, 'Zone 6 Old Al-Ghanim, street 810 Al-Fardan Center 3rd Floor', 'Doha', 'na'),
(7, 8, 'Caitlyn XX', 'International', NULL, '2024-12-27 10:12:58', '2024-12-30 05:03:51', 5, 152, 'Neka ulica', 'Novi Sad', '91311'),
(8, 9, 'Luka Anđelković', 'USA', NULL, '2024-12-30 05:40:40', '2024-12-30 08:33:41', 4, 152, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000'),
(9, 10, 'Luka Test', 'USA', '2024-12-30 06:07:54', '2024-12-30 06:07:36', '2024-12-30 06:07:54', 6, 152, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000'),
(10, 11, 'test', 'International', '2024-12-30 08:33:46', '2024-12-30 06:08:27', '2024-12-30 08:33:46', 6, 155, 'Bulevar Sv. Cara Konstantina 80, 86 Prilaz 4 br. 21', 'Niš', '18000'),
(11, 12, 'Luka Anđelković', 'International', '2025-01-08 04:24:31', '2024-12-31 05:22:53', '2025-01-08 04:24:31', NULL, 152, 'qweqweqwe', 'Niš', '18000'),
(12, 16, 'Tes', 'USA', '2025-01-08 11:36:44', '2024-12-31 07:40:56', '2025-01-08 11:36:44', 4, NULL, 'tst', 'test', 'test'),
(13, 17, 'TEst', 'International', '2025-01-08 11:36:44', '2024-12-31 07:41:59', '2025-01-08 11:36:44', NULL, 5, 'tea', 'test', 'test'),
(14, 18, 'Test 123', 'USA', NULL, '2024-12-31 07:42:21', '2024-12-31 07:42:21', 5, NULL, 'Neka ulica', 'Los Angeles', '91311'),
(15, 21, 'test kekacbre', 'International', NULL, '2024-12-31 08:42:20', '2025-01-03 10:30:40', 49, 152, '543 Toby Greens', 'West Jordan', '86063'),
(16, 22, 'My Licencee', 'USA', NULL, '2024-12-31 09:24:33', '2024-12-31 09:24:33', NULL, NULL, NULL, NULL, NULL),
(17, 23, 'Top Licensee', 'USA', NULL, '2024-12-31 09:26:55', '2025-01-04 01:44:07', 4, NULL, 'test', 'test', '112233'),
(18, 24, 'CORE', 'USA', NULL, '2025-01-03 13:57:41', '2025-01-03 13:57:41', 30, NULL, NULL, NULL, NULL),
(19, 25, 'Lagree Room', 'International', '2025-01-08 09:20:44', '2025-01-06 10:49:29', '2025-01-08 09:20:44', NULL, 31, '19979 76th Ave. Unit 115', 'Langley, BC', 'V2Y 3J3'),
(20, 26, 'Shania Spinka', 'USA', NULL, '2025-01-08 11:37:10', '2025-01-08 11:37:10', 13, NULL, '365 Shields View', 'North Port', '61128'),
(21, 27, 'Kelly Zulauf', 'International', NULL, '2025-01-08 11:37:18', '2025-01-09 05:42:35', 42, NULL, NULL, NULL, NULL),
(22, 32, 'asdasdasd', 'USA', NULL, '2025-01-09 05:43:05', '2025-01-09 05:43:05', NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `keywords` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`keywords`)),
  `type` varchar(255) NOT NULL,
  `unique_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `failed_jobs`
--

INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(1, '232092ff-06bd-456c-a732-f790c1ba9ec1', 'database', 'default', '{\"uuid\":\"232092ff-06bd-456c-a732-f790c1ba9ec1\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-16 18:02:36'),
(2, '73864200-1ac1-4ba9-b2c5-2473d6c77ab2', 'database', 'default', '{\"uuid\":\"73864200-1ac1-4ba9-b2c5-2473d6c77ab2\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"payments\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-16 18:04:35'),
(3, '8f8b0d6f-15e6-4db0-a8f2-d8d1214f6663', 'database', 'default', '{\"uuid\":\"8f8b0d6f-15e6-4db0-a8f2-d8d1214f6663\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-24 08:17:00'),
(4, '94964a66-8b84-4b19-a0d1-3d01ea6280fa', 'database', 'default', '{\"uuid\":\"94964a66-8b84-4b19-a0d1-3d01ea6280fa\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:14:\\\"lease template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:16:\\\"App\\\\Models\\\\Lease\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-24 08:22:17'),
(5, '44a60269-677f-4e48-ac8e-6c3b41371eea', 'database', 'default', '{\"uuid\":\"44a60269-677f-4e48-ac8e-6c3b41371eea\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 13:49:32');
INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(6, '782c51f3-aa47-43dd-9f45-8efa0301e3d2', 'database', 'default', '{\"uuid\":\"782c51f3-aa47-43dd-9f45-8efa0301e3d2\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:4;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 15:19:04'),
(7, '486a7649-30fd-4ad7-b862-f9cf76d16cec', 'database', 'default', '{\"uuid\":\"486a7649-30fd-4ad7-b862-f9cf76d16cec\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:5;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 16:30:09'),
(8, '8211c0f7-2620-437d-a346-c75916abc754', 'database', 'default', '{\"uuid\":\"8211c0f7-2620-437d-a346-c75916abc754\",\"displayName\":\"App\\\\Jobs\\\\SendAgreement\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\SendAgreement\",\"command\":\"O:22:\\\"App\\\\Jobs\\\\SendAgreement\\\":4:{s:36:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000templateName\\\";s:16:\\\"license template\\\";s:32:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000customer\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:19:\\\"App\\\\Models\\\\Customer\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:29:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000model\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\License\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:1:{i:0;s:8:\\\"payments\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:31:\\\"\\u0000App\\\\Jobs\\\\SendAgreement\\u0000company\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:18:\\\"App\\\\Models\\\\Company\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'GuzzleHttp\\Exception\\ClientException: Client error: `GET https://api.na3.adobesign.com/api/rest/v6/libraryDocuments` resulted in a `403 Forbidden` response:\n{\"code\":\"INVALID_API_ACCESS_POINT\",\"message\":\"Request must be made to correct API access point (e.g. use GET /baseUris). (truncated...)\n in /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php:113\nStack trace:\n#0 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Middleware.php(72): GuzzleHttp\\Exception\\RequestException::create()\n#1 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(209): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()\n#2 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(158): GuzzleHttp\\Promise\\Promise::callHandler()\n#3 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/TaskQueue.php(52): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()\n#4 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(251): GuzzleHttp\\Promise\\TaskQueue->run()\n#5 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(227): GuzzleHttp\\Promise\\Promise->invokeWaitFn()\n#6 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(272): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#7 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(229): GuzzleHttp\\Promise\\Promise->invokeWaitList()\n#8 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/promises/src/Promise.php(69): GuzzleHttp\\Promise\\Promise->waitIfPending()\n#9 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/Client.php(189): GuzzleHttp\\Promise\\Promise->wait()\n#10 /home/<USER>/arznzamezd/public_html/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\\Client->request()\n#11 /home/<USER>/arznzamezd/public_html/app/Services/Admin/AdobeSign/AdobeSignService.php(35): GuzzleHttp\\Client->get()\n#12 /home/<USER>/arznzamezd/public_html/app/Jobs/SendAgreement.php(42): App\\Services\\Admin\\AdobeSign\\AdobeSignService::getTemplate()\n#13 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\SendAgreement->handle()\n#14 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#15 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#16 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#17 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#18 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(128): Illuminate\\Container\\Container->call()\n#19 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Bus\\Dispatcher->Illuminate\\Bus\\{closure}()\n#20 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#21 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(132): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(124): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#23 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Queue\\CallQueuedHandler->Illuminate\\Queue\\{closure}()\n#24 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()\n#25 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Pipeline\\Pipeline->then()\n#26 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(70): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#27 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(98): Illuminate\\Queue\\CallQueuedHandler->call()\n#28 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(439): Illuminate\\Queue\\Jobs\\Job->fire()\n#29 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(389): Illuminate\\Queue\\Worker->process()\n#30 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(176): Illuminate\\Queue\\Worker->runJob()\n#31 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(137): Illuminate\\Queue\\Worker->daemon()\n#32 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(120): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#33 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#34 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()\n#35 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()\n#36 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#37 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()\n#38 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(208): Illuminate\\Container\\Container->call()\n#39 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()\n#40 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()\n#41 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(1081): Illuminate\\Console\\Command->run()\n#42 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(320): Symfony\\Component\\Console\\Application->doRunCommand()\n#43 /home/<USER>/arznzamezd/public_html/vendor/symfony/console/Application.php(174): Symfony\\Component\\Console\\Application->doRun()\n#44 /home/<USER>/arznzamezd/public_html/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(200): Symfony\\Component\\Console\\Application->run()\n#45 /home/<USER>/arznzamezd/public_html/artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle()\n#46 {main}', '2024-12-30 16:30:44');

-- --------------------------------------------------------

--
-- Table structure for table `files`
--

CREATE TABLE `files` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `fileable_type` varchar(255) NOT NULL,
  `fileable_id` bigint(20) NOT NULL,
  `hash_name` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `extension` varchar(255) NOT NULL,
  `path` text NOT NULL,
  `type` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `files`
--

INSERT INTO `files` (`id`, `fileable_type`, `fileable_id`, `hash_name`, `name`, `extension`, `path`, `type`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\Company', 1, '6762c4ece8d03_3I4rxeodJW.jpg', 'Group 15956.jpg', 'jpg', 'admin/companies/1/6762c4ece8d03_3I4rxeodJW.jpg', 'image', '2024-12-16 04:56:12', '2024-12-18 04:49:48'),
(2, 'App\\Models\\Company', 1, '67602384e0109_rHzUmUeuhr.png', 'test2.png', 'png', 'admin/companies/1/67602384e0109_rHzUmUeuhr.png', 'image', '2024-12-16 04:56:36', '2024-12-16 04:56:36'),
(3, 'App\\Models\\Company', 1, '676026deb3a3d_diBgXudZWj.png', 'test2.png', 'png', 'admin/companies/1/676026deb3a3d_diBgXudZWj.png', 'image', '2024-12-16 05:10:54', '2024-12-16 05:10:54'),
(4, 'App\\Models\\Company', 1, '676026f4bf797_5lDD2e6j4F.png', 'test2.png', 'png', 'admin/companies/1/676026f4bf797_5lDD2e6j4F.png', 'image', '2024-12-16 05:11:16', '2024-12-16 05:11:16'),
(6, 'App\\Models\\Company', 3, '6762c51cb6980_pNAZ8miaqJ.jpg', 'lagree-logo.jpg', 'jpg', 'admin/companies/3/6762c51cb6980_pNAZ8miaqJ.jpg', 'image', '2024-12-18 04:50:36', '2024-12-18 04:50:36'),
(7, 'App\\Models\\Company', 2, '6796a6edc0497_bLbiv41Ult.jpg', 'logo-ims-invoice.jpg', 'jpg', 'admin/companies/2/6796a6edc0497_bLbiv41Ult.jpg', 'image', '2025-01-26 12:19:41', '2025-01-26 12:19:41');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `payment_id` bigint(20) UNSIGNED NOT NULL,
  `number` int(10) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`id`, `customer_id`, `payment_id`, `number`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 8, 127, 1, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(2, 8, 128, 2, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(3, 8, 129, 3, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(4, 8, 130, 4, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(5, 8, 131, 5, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(6, 8, 132, 6, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(7, 8, 133, 7, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(8, 8, 134, 8, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(9, 8, 135, 9, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(10, 8, 136, 10, '2024-12-30 05:49:31', '2024-12-30 08:33:41', NULL),
(11, 8, 137, 11, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(12, 8, 138, 12, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(13, 8, 139, 13, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(14, 8, 140, 14, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(15, 8, 141, 15, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(16, 8, 142, 16, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(17, 8, 143, 17, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(18, 8, 144, 18, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(19, 8, 145, 19, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(20, 8, 146, 20, '2024-12-30 07:19:03', '2024-12-30 08:33:41', NULL),
(21, 8, 147, 21, '2024-12-30 08:30:06', '2024-12-30 08:33:41', NULL),
(22, 8, 148, 22, '2024-12-30 08:30:06', '2024-12-30 08:33:41', NULL),
(23, 8, 149, 23, '2024-12-30 08:30:06', '2024-12-30 08:33:41', NULL),
(24, 8, 150, 24, '2024-12-30 08:30:06', '2024-12-30 08:33:41', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_products`
--

CREATE TABLE `invoice_products` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `number` int(10) UNSIGNED NOT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'awaiting',
  `company_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `send_date` date NOT NULL,
  `sent_date` date DEFAULT NULL,
  `shipping_fee` decimal(8,2) DEFAULT NULL,
  `handling_fee` decimal(8,2) DEFAULT NULL,
  `show_tax` int(11) NOT NULL,
  `paid` int(11) NOT NULL DEFAULT 0,
  `note` text DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `reminder_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoice_products`
--

INSERT INTO `invoice_products` (`id`, `number`, `status`, `company_id`, `customer_id`, `send_date`, `sent_date`, `shipping_fee`, `handling_fee`, `show_tax`, `paid`, `note`, `deleted_at`, `created_at`, `updated_at`, `reminder_date`) VALUES
(11, 1, 'sent', 1, 5, '2024-12-26', '2024-12-26', NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 07:39:10', '2024-12-26 07:41:44', NULL),
(12, 12, 'sent', 1, 1, '2024-12-26', '2024-12-26', NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 08:09:54', '2024-12-26 08:10:02', NULL),
(13, 13, 'awaiting', 1, 6, '2024-12-26', NULL, NULL, NULL, 0, 0, NULL, NULL, '2024-12-26 11:14:42', '2024-12-26 11:14:42', NULL),
(14, 14, 'awaiting', 1, 1, '2024-12-30', NULL, NULL, NULL, 1, 0, NULL, NULL, '2024-12-30 07:20:22', '2024-12-30 07:22:34', NULL),
(15, 15, 'awaiting', 2, 11, '2024-12-31', NULL, NULL, NULL, 0, 0, NULL, NULL, '2024-12-31 05:23:33', '2024-12-31 05:23:33', NULL),
(16, 16, 'sent', 4, 15, '2025-01-03', '2025-01-22', 50.00, 112.00, 1, 0, 'test note', NULL, '2025-01-03 10:31:40', '2025-01-22 03:02:37', '2025-01-22'),
(17, 17, 'sent', 1, 18, '2025-01-03', '2025-01-06', NULL, NULL, 0, 0, 'LEASE DEPOSIT', NULL, '2025-01-03 14:00:50', '2025-01-06 09:54:47', NULL),
(18, 18, 'awaiting', 1, 6, '2025-01-06', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-06 08:16:18', '2025-01-06 08:16:18', NULL),
(19, 19, 'sent', 1, 19, '2025-01-06', '2025-01-06', NULL, NULL, 0, 0, NULL, '2025-01-08 09:20:44', '2025-01-06 10:52:12', '2025-01-08 09:20:44', NULL),
(20, 20, 'sent', 3, 19, '2025-01-06', '2025-01-06', NULL, NULL, 0, 0, 'LICENSE POSTAL CODE: V2Y', '2025-01-08 09:20:44', '2025-01-06 11:04:35', '2025-01-08 09:20:44', NULL),
(21, 21, 'awaiting', 1, 16, '2025-01-07', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-07 08:28:09', '2025-01-07 08:28:09', NULL),
(22, 22, 'awaiting', 1, 16, '2025-01-07', NULL, 330.00, 22.00, 0, 0, NULL, NULL, '2025-01-07 08:33:42', '2025-01-13 06:42:26', NULL),
(23, 23, 'awaiting', 4, 8, '2025-01-15', NULL, 22.00, 33.00, 0, 0, 'test', NULL, '2025-01-15 14:52:18', '2025-01-15 14:52:18', NULL),
(24, 24, 'sent', 4, 15, '2025-01-17', '2025-01-17', 20.00, 10.00, 1, 1, NULL, NULL, '2025-01-17 03:00:02', '2025-01-17 14:26:50', '2025-01-17'),
(25, 25, 'awaiting', 1, 22, '2025-01-22', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-22 00:53:34', '2025-01-22 09:18:26', NULL),
(26, 26, 'awaiting', 1, 7, '2025-01-22', NULL, NULL, NULL, 0, 0, NULL, NULL, '2025-01-22 01:47:05', '2025-01-22 01:47:05', NULL),
(27, 27, 'sent', 4, 15, '2025-01-22', '2025-01-22', 22.00, 33.00, 0, 1, NULL, NULL, '2025-01-22 01:54:26', '2025-01-22 03:13:24', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_product_items`
--

CREATE TABLE `invoice_product_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `invoice_product_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `discount` decimal(8,2) NOT NULL,
  `discount_type` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoice_product_items`
--

INSERT INTO `invoice_product_items` (`id`, `invoice_product_id`, `product_id`, `price`, `quantity`, `discount`, `discount_type`, `created_at`, `updated_at`) VALUES
(84, 27, 112, 260.00, 1, 0.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(85, 27, 113, 90.00, 1, 0.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(86, 27, 114, 80.00, 2, 0.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(87, 27, 115, 100.00, 2, 0.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(88, 27, 116, 90.00, 1, 0.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(89, 27, 213, 222.00, 1, 20.00, '$', '2025-01-26 03:23:16', '2025-01-26 04:36:49'),
(90, 27, 215, 22.00, 1, 0.00, '$', '2025-01-26 04:36:30', '2025-01-26 04:36:49'),
(91, 27, 216, 21.00, 1, 0.00, '$', '2025-01-26 04:36:49', '2025-01-26 04:36:49');

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `leases`
--

CREATE TABLE `leases` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `company_id` bigint(20) UNSIGNED NOT NULL,
  `studio_id` bigint(20) UNSIGNED NOT NULL,
  `initial_currency_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `machine_id` bigint(20) UNSIGNED NOT NULL,
  `machine_price` int(11) NOT NULL,
  `machine_quantity` int(11) NOT NULL,
  `duration` int(11) NOT NULL,
  `starting_date` date NOT NULL,
  `deposit_amount` int(11) DEFAULT NULL,
  `deposit_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `leases`
--

INSERT INTO `leases` (`id`, `company_id`, `studio_id`, `initial_currency_id`, `customer_id`, `machine_id`, `machine_price`, `machine_quantity`, `duration`, `starting_date`, `deposit_amount`, `deposit_date`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, 1, 1, 150000, 10, 48, '2024-12-24', 1000000, '2024-12-24', 1, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16');

-- --------------------------------------------------------

--
-- Table structure for table `licenses`
--

CREATE TABLE `licenses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `company_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `studio_id` bigint(20) UNSIGNED NOT NULL,
  `initial_currency_id` bigint(20) UNSIGNED DEFAULT NULL,
  `type` varchar(255) NOT NULL,
  `location` varchar(255) NOT NULL,
  `price` int(11) NOT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `starting_date` date NOT NULL,
  `deposit_amount` int(11) DEFAULT NULL,
  `deposit_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `licenses`
--

INSERT INTO `licenses` (`id`, `company_id`, `customer_id`, `studio_id`, `initial_currency_id`, `type`, `location`, `price`, `duration`, `starting_date`, `deposit_amount`, `deposit_date`, `is_active`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 3, 1, 1, 1, 'license', 'USA', 150000, '30', '2024-12-16', 0, NULL, 1, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(2, 2, 1, 1, 1, 'exclusivity', 'USA', 20000, '48', '2024-12-24', NULL, NULL, 1, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(3, 2, 8, 5, 1, 'license', 'International', 50000, '10', '2024-12-31', 50000, '2024-12-31', 1, NULL, '2024-12-30 05:49:31', '2025-01-15 14:50:38'),
(4, 2, 8, 3, 1, 'license', 'USA', 50000, '10', '2024-12-31', NULL, NULL, 1, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(5, 2, 8, 4, 1, 'license', 'USA', 52000, '4', '2024-12-31', NULL, NULL, 1, NULL, '2024-12-30 08:30:06', '2024-12-30 08:33:41');

-- --------------------------------------------------------

--
-- Table structure for table `machines`
--

CREATE TABLE `machines` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `machines`
--

INSERT INTO `machines` (`id`, `name`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 'The Mega Pro', NULL, '2024-12-24 00:21:29', '2024-12-29 00:41:26'),
(2, 'The Micro', NULL, '2024-12-24 01:10:19', '2024-12-24 01:10:19'),
(3, 'The Mini', NULL, '2024-12-24 01:10:24', '2024-12-24 01:10:24'),
(4, 'The Mini Pro', NULL, '2024-12-24 01:10:29', '2024-12-24 01:10:29'),
(5, 'The Mega Pro', NULL, '2024-12-24 01:10:34', '2024-12-24 01:10:34');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2018_08_08_100000_create_telescope_entries_table', 1),
(4, '2019_08_19_000000_create_failed_jobs_table', 1),
(5, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(6, '2023_01_01_115520_create_currencies_table', 1),
(7, '2023_05_14_103336_create_customers_table', 1),
(8, '2023_06_13_075456_create_states_table', 1),
(9, '2023_06_14_202230_create_companies_table', 1),
(10, '2023_06_27_104532_create_studios_table', 1),
(11, '2023_08_05_121755_create_licenses_table', 1),
(12, '2023_09_19_143259_create_notifications_table', 1),
(13, '2023_11_12_110715_create_machines_table', 1),
(14, '2023_11_13_085357_create_leases_table', 1),
(15, '2023_11_14_202809_create_payments_table', 1),
(16, '2024_07_31_091414_create_files_table', 1),
(17, '2024_08_09_182936_create_permission_tables', 1),
(18, '2024_08_19_180619_create_notes_table', 1),
(19, '2024_08_23_095854_create_invoices_table', 1),
(20, '2024_09_09_074324_create_suppliers_table', 1),
(21, '2024_09_09_075540_create_contact_people_table', 1),
(22, '2024_09_12_072230_create_orders_table', 1),
(23, '2024_09_12_072512_create_order_items_table', 1),
(24, '2024_09_30_085251_create_agreements_table', 1),
(25, '2024_10_01_092322_create_webhooks_table', 1),
(26, '2024_10_02_123833_create_jobs_table', 1),
(27, '2024_10_09_091256_create_admin_notifications_table', 1),
(28, '2024_10_10_105429_create_admin_settings_table', 1),
(29, '2024_10_15_094705_create_conversion_rates_table', 1),
(30, '2024_10_16_114654_add_initial_currency_id_to_licenses_table', 1),
(31, '2024_10_16_114700_add_initial_currency_id_to_leases_table', 1),
(32, '2024_10_18_074814_add_customer_id_to_invoices_table', 1),
(33, '2024_10_18_102013_add_soft_delete_to_invoices_table', 1),
(34, '2024_10_21_070051_create_email_templates_table', 1),
(35, '2024_12_01_081312_create_products_table', 1),
(36, '2024_12_02_111553_create_invoice_products_table', 1),
(37, '2024_12_03_080408_create_invoice_product_items_table', 1),
(38, '2024_12_16_144726_create_countries_table', 2),
(39, '2024_12_16_162447_add_country_id_to_suppliers_table', 2),
(40, '2024_12_23_224305_add_category_to_products_table', 3),
(41, '2024_12_25_232102_add_discount_type_to_invoice_product_items_table', 4),
(42, '2024_12_29_132050_add_show_tax_to_invoice_products_table', 5),
(43, '2024_12_29_151509_add_address_to_customers_table', 5),
(44, '2024_12_30_150432_add_country_id_to_studios_table', 6),
(45, '2024_12_31_094142_alter_category_from_products_table', 7),
(46, '2024_12_31_151204_add_location_to_studios_table', 8),
(47, '2024_12_31_162250_add_nullable_to_customers_table', 8),
(48, '2025_01_17_161227_add_paid_to_invoice_products_table', 9),
(49, '2025_01_17_224237_add_reminder_date_to_invoice_products_table', 10),
(50, '2025_01_18_193335_add_type_to_products_table', 11),
(51, '2025_01_22_173850_create_order_products_table', 12),
(53, '2025_01_23_163651_add_supplier_to_products_table', 13),
(54, '2025_01_23_173438_add_fields_to_order_items_table', 13),
(55, '2025_01_25_142332_add_stock_to_products_table', 14);

-- --------------------------------------------------------

--
-- Table structure for table `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(1, 'App\\Models\\User', 21),
(2, 'App\\Models\\User', 2),
(2, 'App\\Models\\User', 3),
(2, 'App\\Models\\User', 4),
(2, 'App\\Models\\User', 5),
(2, 'App\\Models\\User', 6),
(2, 'App\\Models\\User', 7),
(2, 'App\\Models\\User', 8),
(2, 'App\\Models\\User', 9),
(2, 'App\\Models\\User', 10),
(2, 'App\\Models\\User', 11),
(2, 'App\\Models\\User', 12),
(2, 'App\\Models\\User', 13),
(2, 'App\\Models\\User', 14),
(2, 'App\\Models\\User', 15),
(2, 'App\\Models\\User', 16),
(2, 'App\\Models\\User', 17),
(2, 'App\\Models\\User', 18),
(2, 'App\\Models\\User', 19),
(2, 'App\\Models\\User', 20),
(2, 'App\\Models\\User', 22),
(2, 'App\\Models\\User', 23),
(2, 'App\\Models\\User', 24),
(2, 'App\\Models\\User', 25),
(2, 'App\\Models\\User', 26),
(2, 'App\\Models\\User', 27),
(2, 'App\\Models\\User', 32);

-- --------------------------------------------------------

--
-- Table structure for table `notes`
--

CREATE TABLE `notes` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `body` text NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notes`
--

INSERT INTO `notes` (`id`, `customer_id`, `body`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 2, 'this costumer sucks', NULL, '2024-12-18 07:27:32', '2024-12-18 07:27:32'),
(2, 4, 'dadada', NULL, '2024-12-23 21:09:23', '2024-12-23 21:09:23'),
(3, 1, 'Non minor, inquit, voluptas percipitur ex vilissimis rebus quam ex pretiosissimis. Hoc est non modo cor non habere, sed ne palatum quidem. Duo Reges: constructio interrete', NULL, '2024-12-24 03:46:44', '2024-12-24 03:46:44'),
(4, 1, 'Ut enim consuetudo loquitur, id solum dicitur honestum, quod est populari fama gloriosum. Luxuriam non reprehendit, modo sit vacua infinita cupiditate et timore. Haec mihi videtur delicatior, ut ita dicam, molliorque ratio, quam virtutis vis gravitasque postulat.', NULL, '2024-12-24 03:46:55', '2024-12-24 03:46:55'),
(5, 15, 'my new note', NULL, '2025-01-22 03:00:00', '2025-01-22 03:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `notifiable_type`, `notifiable_id`, `data`, `read_at`, `created_at`, `updated_at`) VALUES
('625e5e2d-f32d-4a11-b4d5-e26234f886c6', 'App\\Notifications\\AdminNotification', 'App\\Models\\User', 2, '{\"title\":\"First text\",\"data\":\"This is very first notification.\",\"link\":\"http:\\/\\/somelink.com\"}', NULL, '2024-12-26 20:00:04', '2024-12-26 20:00:04');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `company_id` bigint(20) UNSIGNED NOT NULL,
  `supplier_id` bigint(20) UNSIGNED NOT NULL,
  `note` text DEFAULT NULL,
  `sent_at` date DEFAULT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `company_id`, `supplier_id`, `note`, `sent_at`, `status`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, '2024-12-24', 1, '2024-12-26 06:45:47', '2024-12-23 04:56:40', '2024-12-26 06:45:47'),
(2, 2, 1, 'This is order note', NULL, 0, '2024-12-31 03:37:39', '2024-12-27 01:22:01', '2024-12-31 03:37:39'),
(3, 2, 1, NULL, NULL, 0, '2024-12-31 03:37:35', '2024-12-31 03:27:05', '2024-12-31 03:37:35'),
(4, 2, 1, NULL, NULL, 0, NULL, '2024-12-31 04:07:53', '2024-12-31 04:07:53'),
(5, 1, 3, NULL, NULL, 0, NULL, '2025-01-26 07:26:08', '2025-01-26 07:26:08'),
(6, 1, 4, 'asd as dasd', NULL, 0, NULL, '2025-01-26 07:32:05', '2025-01-26 07:32:05'),
(7, 2, 2, 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi tempus, risus quis ultricies pharetra, dui tortor condimentum risus, at tristique ante elit vel velit. Proin elementum, nisi vitae auctor commodo, lectus ipsum dapibus velit, eu suscipit sapien ligula at tellus. Proin placerat eros vel arcu feugiat, id luctus orci eleifend. Curabitur nulla tortor, feugiat ut faucibus quis, consectetur eu augue. Duis sed convallis purus. Sed vel finibus dolor, non tincidunt augue.', '2025-01-26', 1, NULL, '2025-01-26 07:33:33', '2025-01-26 12:19:51'),
(8, 1, 4, NULL, NULL, 0, NULL, '2025-01-26 07:34:09', '2025-01-26 07:34:09'),
(9, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 07:35:26', '2025-01-26 07:35:26'),
(10, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 07:41:39', '2025-01-26 07:41:39'),
(11, 1, 1, NULL, '2025-01-10', 0, NULL, '2025-01-26 07:44:39', '2025-01-26 11:18:24'),
(12, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 07:45:48', '2025-01-26 07:45:48'),
(13, 1, 1, NULL, NULL, 0, NULL, '2025-01-26 07:46:33', '2025-01-26 07:46:33'),
(14, 1, 3, NULL, '2025-01-26', 0, NULL, '2025-01-26 07:48:30', '2025-01-26 07:48:30'),
(15, 1, 1, NULL, '2025-01-26', 0, NULL, '2025-01-26 07:53:19', '2025-01-26 07:53:19');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `order_id` bigint(20) UNSIGNED NOT NULL,
  `product_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(8,2) NOT NULL DEFAULT 0.00,
  `quantity` int(11) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `product_id`, `name`, `price`, `quantity`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 'Micro Pro', 0.00, 2, NULL, '2024-12-23 04:56:40', '2024-12-26 06:45:47'),
(2, 2, 2, 'Micro Pro 2', 0.00, 2, NULL, '2024-12-27 01:22:01', '2024-12-31 03:37:39'),
(3, 2, 3, 'Micro Pro 3', 0.00, 2, NULL, '2024-12-27 01:22:01', '2024-12-31 03:37:39'),
(4, 4, 4, '-', 130.00, 7, NULL, '2024-12-31 03:27:05', '2025-01-26 05:57:45'),
(5, 4, 5, '-', 220.00, 8, NULL, '2024-12-31 04:07:53', '2025-01-26 05:57:45'),
(6, 4, 224, '-', 330.00, 1, NULL, '2025-01-26 04:42:28', '2025-01-26 05:57:45'),
(7, 4, 35, '-', 1.69, 100, NULL, '2025-01-26 04:57:48', '2025-01-26 05:57:45'),
(8, 4, 88, '-', 1.16, 100, NULL, '2025-01-26 04:57:48', '2025-01-26 05:57:45'),
(9, 4, 88, '-', 1.16, 1, NULL, '2025-01-26 05:57:45', '2025-01-26 05:57:45'),
(10, 4, 28, '-', 2.55, 1, NULL, '2025-01-26 05:57:45', '2025-01-26 05:57:45'),
(11, 4, 22, '-', 3.88, 1, NULL, '2025-01-26 05:57:45', '2025-01-26 05:57:45'),
(12, 4, 88, '-', 1.16, 1, NULL, '2025-01-26 05:57:45', '2025-01-26 05:57:45'),
(13, 5, 1, '-', 3.37, 2, NULL, '2025-01-26 07:26:08', '2025-01-26 07:28:39'),
(14, 6, 149, '-', 45.48, 12, NULL, '2025-01-26 07:32:05', '2025-01-26 07:32:05'),
(15, 6, 149, '-', 42.96, 12, NULL, '2025-01-26 07:32:05', '2025-01-26 07:32:05'),
(16, 7, 149, '-', 1.03, 1, NULL, '2025-01-26 07:33:33', '2025-01-26 12:28:12'),
(17, 7, 151, '-', 20.00, 12, NULL, '2025-01-26 07:33:33', '2025-01-26 12:28:12'),
(18, 8, 149, '-', 42.96, 1, NULL, '2025-01-26 07:34:09', '2025-01-26 07:34:09'),
(19, 9, 149, '-', 1.69, 1, NULL, '2025-01-26 07:35:26', '2025-01-26 07:35:26'),
(20, 10, 149, '-', 1.69, 1, NULL, '2025-01-26 07:41:39', '2025-01-26 07:41:39'),
(21, 13, 149, '-', 1.03, 1, NULL, '2025-01-26 07:46:33', '2025-01-26 07:46:33'),
(22, 13, 29, '-', 2.43, 1, NULL, '2025-01-26 07:46:33', '2025-01-26 07:46:33'),
(23, 14, 34, '-', 5.06, 1, NULL, '2025-01-26 07:48:30', '2025-01-26 07:48:30'),
(24, 15, 149, '-', 1.03, 1, NULL, '2025-01-26 07:53:19', '2025-01-26 12:27:36'),
(25, 11, 88, '-', 1.16, 1, NULL, '2025-01-26 10:32:49', '2025-01-26 11:18:24'),
(26, 11, 229, '-', 22.00, 2, NULL, '2025-01-26 10:32:49', '2025-01-26 11:18:24'),
(27, 7, 230, '-', 22.00, 12, NULL, '2025-01-26 11:46:30', '2025-01-26 12:28:12'),
(28, 7, 38, '-', 3.44, 12, NULL, '2025-01-26 11:46:30', '2025-01-26 12:28:12'),
(29, 7, 231, '-', 44.00, 12, NULL, '2025-01-26 11:46:30', '2025-01-26 12:28:12'),
(30, 15, 149, '-', 1.03, 1, NULL, '2025-01-26 12:27:14', '2025-01-26 12:27:36'),
(31, 15, 29, '-', 2.43, 12, NULL, '2025-01-26 12:27:36', '2025-01-26 12:27:36'),
(32, 7, 232, '-', 22.00, 12, NULL, '2025-01-26 12:28:12', '2025-01-26 12:28:12'),
(33, 7, 233, '-', 33.00, 12, NULL, '2025-01-26 12:28:12', '2025-01-26 12:28:12'),
(34, 7, 38, '-', 3.44, 12, NULL, '2025-01-26 12:28:12', '2025-01-26 12:28:12');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `license_id` bigint(20) UNSIGNED DEFAULT NULL,
  `lease_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `machine_id` bigint(20) UNSIGNED DEFAULT NULL,
  `payment_number` int(11) NOT NULL,
  `payment_amount` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `status` int(11) NOT NULL DEFAULT 0,
  `payment_reminder` tinyint(1) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payments`
--

INSERT INTO `payments` (`id`, `license_id`, `lease_id`, `customer_id`, `machine_id`, `payment_number`, `payment_amount`, `payment_date`, `status`, `payment_reminder`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 1, NULL, 1, 150000, '2025-01-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(2, 1, NULL, 1, NULL, 2, 150000, '2025-02-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(3, 1, NULL, 1, NULL, 3, 150000, '2025-03-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(4, 1, NULL, 1, NULL, 4, 150000, '2025-04-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(5, 1, NULL, 1, NULL, 5, 150000, '2025-05-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(6, 1, NULL, 1, NULL, 6, 150000, '2025-06-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(7, 1, NULL, 1, NULL, 7, 150000, '2025-07-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(8, 1, NULL, 1, NULL, 8, 150000, '2025-08-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(9, 1, NULL, 1, NULL, 9, 150000, '2025-09-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(10, 1, NULL, 1, NULL, 10, 150000, '2025-10-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(11, 1, NULL, 1, NULL, 11, 150000, '2025-11-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(12, 1, NULL, 1, NULL, 12, 150000, '2025-12-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(13, 1, NULL, 1, NULL, 13, 150000, '2026-01-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(14, 1, NULL, 1, NULL, 14, 150000, '2026-02-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(15, 1, NULL, 1, NULL, 15, 150000, '2026-03-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(16, 1, NULL, 1, NULL, 16, 150000, '2026-04-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(17, 1, NULL, 1, NULL, 17, 150000, '2026-05-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(18, 1, NULL, 1, NULL, 18, 150000, '2026-06-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(19, 1, NULL, 1, NULL, 19, 150000, '2026-07-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(20, 1, NULL, 1, NULL, 20, 150000, '2026-08-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(21, 1, NULL, 1, NULL, 21, 150000, '2026-09-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(22, 1, NULL, 1, NULL, 22, 150000, '2026-10-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(23, 1, NULL, 1, NULL, 23, 150000, '2026-11-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(24, 1, NULL, 1, NULL, 24, 150000, '2026-12-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(25, 1, NULL, 1, NULL, 25, 150000, '2027-01-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(26, 1, NULL, 1, NULL, 26, 150000, '2027-02-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(27, 1, NULL, 1, NULL, 27, 150000, '2027-03-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(28, 1, NULL, 1, NULL, 28, 150000, '2027-04-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(29, 1, NULL, 1, NULL, 29, 150000, '2027-05-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(30, 1, NULL, 1, NULL, 30, 150000, '2027-06-01', 0, 0, NULL, '2024-12-16 10:02:34', '2024-12-16 10:04:33'),
(31, 2, NULL, 1, NULL, 1, 20000, '2025-01-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(32, 2, NULL, 1, NULL, 2, 20000, '2025-02-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(33, 2, NULL, 1, NULL, 3, 20000, '2025-03-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(34, 2, NULL, 1, NULL, 4, 20000, '2025-04-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(35, 2, NULL, 1, NULL, 5, 20000, '2025-05-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(36, 2, NULL, 1, NULL, 6, 20000, '2025-06-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(37, 2, NULL, 1, NULL, 7, 20000, '2025-07-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(38, 2, NULL, 1, NULL, 8, 20000, '2025-08-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(39, 2, NULL, 1, NULL, 9, 20000, '2025-09-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(40, 2, NULL, 1, NULL, 10, 20000, '2025-10-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(41, 2, NULL, 1, NULL, 11, 20000, '2025-11-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(42, 2, NULL, 1, NULL, 12, 20000, '2025-12-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(43, 2, NULL, 1, NULL, 13, 20000, '2026-01-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(44, 2, NULL, 1, NULL, 14, 20000, '2026-02-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(45, 2, NULL, 1, NULL, 15, 20000, '2026-03-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(46, 2, NULL, 1, NULL, 16, 20000, '2026-04-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(47, 2, NULL, 1, NULL, 17, 20000, '2026-05-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(48, 2, NULL, 1, NULL, 18, 20000, '2026-06-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(49, 2, NULL, 1, NULL, 19, 20000, '2026-07-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(50, 2, NULL, 1, NULL, 20, 20000, '2026-08-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(51, 2, NULL, 1, NULL, 21, 20000, '2026-09-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(52, 2, NULL, 1, NULL, 22, 20000, '2026-10-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(53, 2, NULL, 1, NULL, 23, 20000, '2026-11-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(54, 2, NULL, 1, NULL, 24, 20000, '2026-12-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(55, 2, NULL, 1, NULL, 25, 20000, '2027-01-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(56, 2, NULL, 1, NULL, 26, 20000, '2027-02-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(57, 2, NULL, 1, NULL, 27, 20000, '2027-03-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(58, 2, NULL, 1, NULL, 28, 20000, '2027-04-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(59, 2, NULL, 1, NULL, 29, 20000, '2027-05-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(60, 2, NULL, 1, NULL, 30, 20000, '2027-06-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(61, 2, NULL, 1, NULL, 31, 20000, '2027-07-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(62, 2, NULL, 1, NULL, 32, 20000, '2027-08-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(63, 2, NULL, 1, NULL, 33, 20000, '2027-09-01', 0, 0, NULL, '2024-12-24 00:16:58', '2024-12-24 00:16:58'),
(64, 2, NULL, 1, NULL, 34, 20000, '2027-10-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(65, 2, NULL, 1, NULL, 35, 20000, '2027-11-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(66, 2, NULL, 1, NULL, 36, 20000, '2027-12-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(67, 2, NULL, 1, NULL, 37, 20000, '2028-01-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(68, 2, NULL, 1, NULL, 38, 20000, '2028-02-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(69, 2, NULL, 1, NULL, 39, 20000, '2028-03-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(70, 2, NULL, 1, NULL, 40, 20000, '2028-04-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(71, 2, NULL, 1, NULL, 41, 20000, '2028-05-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(72, 2, NULL, 1, NULL, 42, 20000, '2028-06-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(73, 2, NULL, 1, NULL, 43, 20000, '2028-07-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(74, 2, NULL, 1, NULL, 44, 20000, '2028-08-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(75, 2, NULL, 1, NULL, 45, 20000, '2028-09-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(76, 2, NULL, 1, NULL, 46, 20000, '2028-10-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(77, 2, NULL, 1, NULL, 47, 20000, '2028-11-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(78, 2, NULL, 1, NULL, 48, 20000, '2028-12-01', 0, 0, NULL, '2024-12-24 00:16:59', '2024-12-24 00:16:59'),
(79, NULL, 1, 1, 1, 1, 1500000, '2025-01-01', 0, 0, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16'),
(80, NULL, 1, 1, 1, 2, 1500000, '2025-02-01', 0, 0, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16'),
(81, NULL, 1, 1, 1, 3, 1500000, '2025-03-01', 0, 0, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16'),
(82, NULL, 1, 1, 1, 4, 1500000, '2025-04-01', 0, 0, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16'),
(83, NULL, 1, 1, 1, 5, 1500000, '2025-05-01', 0, 0, NULL, '2024-12-24 00:22:16', '2024-12-24 00:22:16'),
(84, NULL, 1, 1, 1, 6, 1500000, '2025-06-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(85, NULL, 1, 1, 1, 7, 1500000, '2025-07-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(86, NULL, 1, 1, 1, 8, 1500000, '2025-08-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(87, NULL, 1, 1, 1, 9, 1500000, '2025-09-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(88, NULL, 1, 1, 1, 10, 1500000, '2025-10-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(89, NULL, 1, 1, 1, 11, 1500000, '2025-11-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(90, NULL, 1, 1, 1, 12, 1500000, '2025-12-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(91, NULL, 1, 1, 1, 13, 1500000, '2026-01-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(92, NULL, 1, 1, 1, 14, 1500000, '2026-02-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(93, NULL, 1, 1, 1, 15, 1500000, '2026-03-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(94, NULL, 1, 1, 1, 16, 1500000, '2026-04-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(95, NULL, 1, 1, 1, 17, 1500000, '2026-05-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(96, NULL, 1, 1, 1, 18, 1500000, '2026-06-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(97, NULL, 1, 1, 1, 19, 1500000, '2026-07-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(98, NULL, 1, 1, 1, 20, 1500000, '2026-08-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(99, NULL, 1, 1, 1, 21, 1500000, '2026-09-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(100, NULL, 1, 1, 1, 22, 1500000, '2026-10-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(101, NULL, 1, 1, 1, 23, 1500000, '2026-11-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(102, NULL, 1, 1, 1, 24, 1500000, '2026-12-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(103, NULL, 1, 1, 1, 25, 1500000, '2027-01-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(104, NULL, 1, 1, 1, 26, 1500000, '2027-02-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(105, NULL, 1, 1, 1, 27, 1500000, '2027-03-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(106, NULL, 1, 1, 1, 28, 1500000, '2027-04-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(107, NULL, 1, 1, 1, 29, 1500000, '2027-05-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(108, NULL, 1, 1, 1, 30, 1500000, '2027-06-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(109, NULL, 1, 1, 1, 31, 1500000, '2027-07-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(110, NULL, 1, 1, 1, 32, 1500000, '2027-08-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(111, NULL, 1, 1, 1, 33, 1500000, '2027-09-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(112, NULL, 1, 1, 1, 34, 1500000, '2027-10-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(113, NULL, 1, 1, 1, 35, 1500000, '2027-11-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(114, NULL, 1, 1, 1, 36, 1500000, '2027-12-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(115, NULL, 1, 1, 1, 37, 1500000, '2028-01-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(116, NULL, 1, 1, 1, 38, 1500000, '2028-02-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(117, NULL, 1, 1, 1, 39, 1500000, '2028-03-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(118, NULL, 1, 1, 1, 40, 1500000, '2028-04-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(119, NULL, 1, 1, 1, 41, 1500000, '2028-05-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(120, NULL, 1, 1, 1, 42, 1500000, '2028-06-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(121, NULL, 1, 1, 1, 43, 1500000, '2028-07-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(122, NULL, 1, 1, 1, 44, 1500000, '2028-08-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(123, NULL, 1, 1, 1, 45, 1500000, '2028-09-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(124, NULL, 1, 1, 1, 46, 1500000, '2028-10-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(125, NULL, 1, 1, 1, 47, 1500000, '2028-11-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(126, NULL, 1, 1, 1, 48, 1500000, '2028-12-01', 0, 0, NULL, '2024-12-24 00:22:17', '2024-12-24 00:22:17'),
(127, 3, NULL, 8, NULL, 1, 50000, '2025-01-01', 1, 0, NULL, '2024-12-30 05:49:31', '2025-01-15 14:50:15'),
(128, 3, NULL, 8, NULL, 2, 50000, '2025-02-01', 1, 0, NULL, '2024-12-30 05:49:31', '2025-01-15 14:50:38'),
(129, 3, NULL, 8, NULL, 3, 50000, '2025-03-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(130, 3, NULL, 8, NULL, 4, 50000, '2025-04-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(131, 3, NULL, 8, NULL, 5, 50000, '2025-05-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(132, 3, NULL, 8, NULL, 6, 50000, '2025-06-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(133, 3, NULL, 8, NULL, 7, 50000, '2025-07-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(134, 3, NULL, 8, NULL, 8, 50000, '2025-08-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(135, 3, NULL, 8, NULL, 9, 50000, '2025-09-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(136, 3, NULL, 8, NULL, 10, 50000, '2025-10-01', 0, 0, NULL, '2024-12-30 05:49:31', '2024-12-30 08:33:41'),
(137, 4, NULL, 8, NULL, 1, 50000, '2025-01-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(138, 4, NULL, 8, NULL, 2, 50000, '2025-02-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(139, 4, NULL, 8, NULL, 3, 50000, '2025-03-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(140, 4, NULL, 8, NULL, 4, 50000, '2025-04-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(141, 4, NULL, 8, NULL, 5, 50000, '2025-05-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(142, 4, NULL, 8, NULL, 6, 50000, '2025-06-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(143, 4, NULL, 8, NULL, 7, 50000, '2025-07-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(144, 4, NULL, 8, NULL, 8, 50000, '2025-08-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(145, 4, NULL, 8, NULL, 9, 50000, '2025-09-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(146, 4, NULL, 8, NULL, 10, 50000, '2025-10-01', 0, 0, NULL, '2024-12-30 07:19:03', '2024-12-30 08:33:41'),
(147, 5, NULL, 8, NULL, 1, 52000, '2025-01-01', 0, 0, NULL, '2024-12-30 08:30:06', '2024-12-30 08:33:41'),
(148, 5, NULL, 8, NULL, 2, 52000, '2025-02-01', 0, 0, NULL, '2024-12-30 08:30:06', '2024-12-30 08:33:41'),
(149, 5, NULL, 8, NULL, 3, 52000, '2025-03-01', 0, 0, NULL, '2024-12-30 08:30:06', '2024-12-30 08:33:41'),
(150, 5, NULL, 8, NULL, 4, 52000, '2025-04-01', 0, 0, NULL, '2024-12-30 08:30:06', '2024-12-30 08:33:41');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `products`
--

CREATE TABLE `products` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(8,2) NOT NULL,
  `category` varchar(255) DEFAULT NULL,
  `custom_product` int(11) NOT NULL DEFAULT 0,
  `supplier_price` decimal(8,2) NOT NULL DEFAULT 0.00,
  `supplier_id` bigint(20) UNSIGNED DEFAULT NULL,
  `stock` int(11) NOT NULL DEFAULT 0,
  `wp_id` bigint(20) UNSIGNED DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `products`
--

INSERT INTO `products` (`id`, `name`, `price`, `category`, `custom_product`, `supplier_price`, `supplier_id`, `stock`, `wp_id`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 'Ankle Strap (set of 2)', 70.00, 'product', 0, 3.37, 2, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(2, 'Black Cables, Classic (set of 2)', 240.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(3, 'Black Moon Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(4, 'Bungee Cuff, Classic', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(5, 'Footstraps, Classic (set of 2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(6, 'Bungee, Classic (M2)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(7, 'Bungee, Classic (M3)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(12, 'Carabiner (set of 2)', 3.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(13, 'Carabiner (Single)', 2.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(14, 'Door Mount Bracket', 50.00, 'product', 0, 27.22, 4, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(15, 'Extension Straps', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(16, 'Inflatable Pad', 50.00, 'product', 0, 5.90, 3, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(17, 'Bungee Attachment (Micro)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(18, 'Cables, Micro (set of 2)', 95.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(19, 'The Micro Cables w/ Black Handle, Bundle (set of 2)', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(20, 'Micro Cables w/ Footstrap Handle Bundle (set of 2)', 190.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(21, 'The Micro Cables w/ Footstraps & Black Handles, Bundle (set of 2)', 280.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(22, 'Grey Spring, Medium (Micro)', 30.00, 'product', 0, 3.88, 2, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(23, 'Handles (Micro)', 190.00, 'product', 0, 42.96, 4, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(24, 'Pulley Cable Bundle (Micro)', 370.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(25, 'Pulley Cables (Micro)', 320.00, 'product', 0, 45.48, 4, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(26, 'Rear Platform (Micro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(27, 'Storage Wall Bracket (Micro)', 40.00, 'product', 0, 4.82, 3, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(28, 'Strap (Micro)', 60.00, 'product', 0, 2.55, 2, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(29, 'White Spring, Super Light (Micro)', 19.00, 'product', 0, 2.43, 1, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(30, 'The Lift Kit', 900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(31, 'Strap (Mini)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(32, 'Handles (Mini/Mini Pro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(33, 'Rear Platform (Mini/Mini Pro)', 490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(34, 'Rope Handle (set of 2)', 70.00, 'product', 0, 5.06, 3, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(35, 'Sliders', 20.00, 'product', 0, 1.69, 1, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(36, 'To Go Kit', 461.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(37, 'Universal Belt', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(38, 'Universal Bungee', 70.00, 'product', 0, 3.44, 2, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(39, 'Water Bottle', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(40, 'Water Bottle + Sliders (Bundle)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(41, 'Black Spring, Medium (M2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(42, 'Blue Spring, Heavy (M2)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(43, 'Carriage Strap (M2)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(44, 'Platform Strap (M2)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(46, 'Yellow Spring, Light (M2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(48, 'Top Handle Covers, M2/M3 (set of 4)', 170.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(51, 'Carriage Strap (M3)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(52, 'Curved Covers, M3 (set of 4)', 220.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(53, 'Platform Strap (M3)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(54, 'Red Spring, Heavy (M3)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(56, 'Yellow Spring, Light (M3)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(61, 'Platform Strap (M3K)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(65, 'Mega Wheels', 10.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(66, 'Micro Black Spring - Light', 25.00, 'product', 0, 3.11, 2, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(68, 'Micro Red Spring - Heavy', 35.00, 'product', 0, 3.94, 3, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(70, 'New Footstraps (set of 2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(71, 'New Long Cables (set of 2)', 260.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(73, 'Red Mega Spring Knobs', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(74, 'Resistance Bungee (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(76, 'Side Panels (No Light)', 500.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(77, 'Side Panels (with Light)', 650.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(78, 'The Micro', 990.00, 'product', 0, 211.04, 4, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(79, 'The Micro Prime', 2215.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(80, 'The Mini Pro', 3490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(81, 'The Micro (Fully Loaded)', 2290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(82, 'The Ramp', 5900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(84, 'The Micro (OPEN BOX)', 990.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(85, 'The Micro Deluxe', 1995.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(87, 'Universal Cuff (set of 2)', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(88, 'Universal Cuff (Single)', 20.00, 'product', 0, 1.16, 1, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(91, 'Willow Balm', 10.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(92, 'Mega Spring Knobs (Yellow)', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(93, 'The Mini', 2490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(95, 'Soft Shell Moon Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(96, 'Soft Shell Crossbody Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(97, 'Soft Shell Nano Bag (Lagree x Got Bag)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(98, 'Black Nano Bag (Lagree x Got Bag)', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(100, 'Mat (Lagree x Stakt)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(101, 'The Micro Pro', 1590.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(102, 'Handles (Micro Pro)', 290.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(103, 'Rear Platform (Micro Pro)', 490.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(105, 'Training fee', 1500.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:20:13', '2024-12-24 13:20:13'),
(106, 'License - Micro', 990.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:20:27', '2024-12-24 13:20:27'),
(107, 'License - Mini', 1990.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:19', '2024-12-24 13:21:19'),
(108, 'License - Mega', 3990.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(109, 'License - EVO', 3990.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:46', '2024-12-24 13:21:46'),
(110, 'Bundle #1 (minimum)', 800.00, 'bundle', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:22:58', '2024-12-24 13:22:58'),
(111, 'Bundle #2 (recommended)', 1320.00, 'bundle', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:23:14', '2024-12-24 13:23:14'),
(112, 'Black rubberized steel cables', 260.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(113, 'Double handle/Foot strap (set)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(114, 'Platform straps', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(115, 'Universal strap', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(116, 'The Pole', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(117, 'Red rubberized steel cables', 250.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(118, 'Red handles (pair)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(119, 'Bungee chord + Bungee bar', 150.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(120, 'X-Strap (M3)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(121, 'The Mega Pro', 8900.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(122, 'Ab Wheel + Mat (Bundle)', 140.00, 'product', 0, 60.00, NULL, 22, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(123, 'Bear Hoodie (Ivory)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(124, 'Bear Shorts (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(125, 'Black Follow Me Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(126, 'Black I Totally Lagree Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(127, 'Black Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(128, 'Gray Zip-up', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(129, 'Classic Short Cables (set of 2)', 230.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(130, 'Cobra Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(131, 'Cobra Bra (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(132, 'Cropped Crewneck (White)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(133, 'Follow Me Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(134, 'Gray Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(135, 'Grip Tops Crop (Lagree x Arebesk)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(136, 'Handles, Classic (set of 2)', 60.00, 'product', 0, 1.17, 1, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(137, 'I Totally Lagree Sweatshirt (Dark Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(138, 'I Totally Lagree Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(139, 'Ice Breaker Bra (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(140, 'Ice Breaker Bra (Oatmeal)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:20:27', '2024-12-24 13:20:27'),
(141, 'Lunger Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(142, 'Lunger Legging (Charcoal)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(143, 'Mega Ab Wheel', 40.00, 'product', 0, 10.95, 4, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(144, 'Mega Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(145, 'Megababe Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(146, 'Megababe Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(147, 'Misc', 5.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(148, 'Muse Closed Toe (Black)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(149, 'New Handles (set of 2)', 60.00, 'product', 0, 1.03, 1, 0, NULL, NULL, NULL, NULL),
(150, 'New Short Cables (set of 2)', 250.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(151, 'Newspaper Tank (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(152, 'Newspaper Tank (Ivory)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(153, 'Pardon My French Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(154, 'Phish Net Closed Toe (Black/White)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(155, 'Pike V-Neck Tee (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(156, 'Pike V-Neck Tee (Ivory)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(157, 'Puffer Tote Bag (Lagree x Got Bag)', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(158, 'Racerback', 40.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(159, 'Racerback + Lagree Water Bottle (Bundle)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(160, 'Racerback + Lagree Water Bottle + SCSG Sweater (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(161, 'Racerback + Lagree Water Bottle + Sweater (Bundle)', 130.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(162, 'Resistance Bungees', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(163, 'Ribbon Tank (Pink)', 20.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(164, 'Rower Tee (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(165, 'Rower Tee (Oatmeal)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:46', '2024-12-24 13:21:46'),
(166, 'Runners Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(167, 'Saw Tank (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(168, 'Saw Tank (Mocha)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(169, 'SCSG Sweater + Lagree Water Bottle (Bundle)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(170, 'Sexy Angel Brami (Black)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(171, 'Sexy Angel Brami (Oatmeal)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(172, 'Sexy Back Bra (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(173, 'Skater Legging (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(174, 'Skater Legging (Walnut)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(175, 'SoCal, So Lagree Sweatshirt', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(176, 'Soul Train Long Sleeve (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(177, 'Soul Train Long Sleeve (Oatmeal)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(178, 'Spider Leggings (Black)', 100.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(179, 'Spoon Bra (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(180, 'Spoon Bra (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(181, 'Spoon Bra (Mocha)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(182, 'Spring with Shroud', 30.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(184, 'Spring with Shroud (Sets)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(185, 'Square Bag (Lagree x Got Bag)', 50.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:34:14', '2024-12-24 13:34:14'),
(186, 'Supra Short (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(187, 'Supra Short (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(188, 'Sweatshirt (Black)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(189, 'Sweatshirt (Gray)', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(192, 'Totally Lagree', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(193, 'Twisted Brami (Black)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(194, 'Twisted Brami (Ivory)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(195, 'Twisted Brami (Walnut)', 70.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:23', '2024-12-12 10:46:23'),
(196, 'White Sweatpants', 80.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(197, 'X-Strap (M2)', 90.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, NULL, NULL),
(198, 'Black Zip-up', 60.00, 'product', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-12 10:46:22', '2024-12-12 10:46:22'),
(199, 'License - Micro (Deposit)', 490.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:20:27', '2024-12-24 13:20:27'),
(200, 'License - Mini (Deposit)', 490.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:19', '2024-12-24 13:21:19'),
(201, 'License - Mega (Deposit)', 490.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:21:32', '2024-12-24 13:21:32'),
(202, 'Lease - Mega Pro (Deposit)', 4000.00, 'fee', 0, 0.00, NULL, 0, NULL, NULL, '2024-12-24 13:20:27', '2024-12-24 13:20:27'),
(204, 'Platform Straps', 80.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 10:00:40', '2025-01-24 10:00:40'),
(205, 'Carriage Straps', 100.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 10:00:40', '2025-01-24 10:00:40'),
(206, 'X-strap', 90.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-24 10:00:40', '2025-01-24 10:00:40'),
(213, 'ASD ASD AS D', 222.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 03:23:16', '2025-01-26 11:09:58'),
(214, 'asdasdasd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:28:14', '2025-01-26 04:28:14'),
(215, 'asdasd aaaaaaaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:36:30', '2025-01-26 11:09:58'),
(216, 'bbbbbbbbbbbbbb', 21.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:36:49', '2025-01-26 11:09:58'),
(217, 'asd asd asd ads', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:37:09', '2025-01-26 04:37:09'),
(218, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:37:26', '2025-01-26 04:37:26'),
(219, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:38:05', '2025-01-26 04:38:05'),
(220, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:38:16', '2025-01-26 04:38:16'),
(221, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:39:48', '2025-01-26 04:39:48'),
(222, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:40:39', '2025-01-26 04:40:39'),
(223, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:41:30', '2025-01-26 04:41:30'),
(224, 'asd asd asd', 330.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 04:42:28', '2025-01-26 05:57:45'),
(225, 'custom door', 12.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 07:00:12', '2025-01-26 07:00:12'),
(226, 'asdasd asd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 07:04:12', '2025-01-26 07:04:12'),
(227, 'asd asd asd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 07:09:08', '2025-01-26 07:09:08'),
(228, 'aaaaaaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 10:32:27', '2025-01-26 10:32:27'),
(229, 'asdasdas das dasd', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 10:32:49', '2025-01-26 11:18:24'),
(230, 'aaaaaa', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 11:46:30', '2025-01-26 12:28:12'),
(231, 'bbbbbbb', 44.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 11:46:30', '2025-01-26 12:28:12'),
(232, 'ccccc', 22.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 12:28:12', '2025-01-26 12:28:12'),
(233, 'cddddddd', 33.00, 'product', 1, 0.00, NULL, 0, NULL, NULL, '2025-01-26 12:28:12', '2025-01-26 12:28:12');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'super-admin', 'web', '2024-12-12 11:46:22', '2024-12-12 11:46:22'),
(2, 'customer', 'web', '2024-12-12 11:46:22', '2024-12-12 11:46:22');

-- --------------------------------------------------------

--
-- Table structure for table `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `states`
--

CREATE TABLE `states` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `states`
--

INSERT INTO `states` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'Alabama', NULL, NULL),
(2, 'Alaska', NULL, NULL),
(3, 'Arizona', NULL, NULL),
(4, 'Arkansas', NULL, NULL),
(5, 'California', NULL, NULL),
(6, 'Colorado', NULL, NULL),
(7, 'Connecticut', NULL, NULL),
(8, 'Delaware', NULL, NULL),
(9, 'Florida', NULL, NULL),
(10, 'Georgia', NULL, NULL),
(11, 'Hawaii', NULL, NULL),
(12, 'Idaho', NULL, NULL),
(13, 'Illinois', NULL, NULL),
(14, 'Indiana', NULL, NULL),
(15, 'Iowa', NULL, NULL),
(16, 'Kansas', NULL, NULL),
(17, 'Kentucky', NULL, NULL),
(18, 'Louisiana', NULL, NULL),
(19, 'Maine', NULL, NULL),
(20, 'Maryland', NULL, NULL),
(21, 'Massachusetts', NULL, NULL),
(22, 'Michigan', NULL, NULL),
(23, 'Minnesota', NULL, NULL),
(24, 'Mississippi', NULL, NULL),
(25, 'Missouri', NULL, NULL),
(26, 'Montana', NULL, NULL),
(27, 'Nebraska', NULL, NULL),
(28, 'Nevada', NULL, NULL),
(29, 'New Hampshire', NULL, NULL),
(30, 'New Jersey', NULL, NULL),
(31, 'New Mexico', NULL, NULL),
(32, 'New York', NULL, NULL),
(33, 'North Carolina', NULL, NULL),
(34, 'North Dakota', NULL, NULL),
(35, 'Ohio', NULL, NULL),
(36, 'Oklahoma', NULL, NULL),
(37, 'Oregon', NULL, NULL),
(38, 'Pennsylvania', NULL, NULL),
(39, 'Rhode Island', NULL, NULL),
(40, 'South Carolina', NULL, NULL),
(41, 'South Dakota', NULL, NULL),
(42, 'Tennessee', NULL, NULL),
(43, 'Texas', NULL, NULL),
(44, 'Utah', NULL, NULL),
(45, 'Vermont', NULL, NULL),
(46, 'Virginia', NULL, NULL),
(47, 'Washington', NULL, NULL),
(48, 'West Virginia', NULL, NULL),
(49, 'Wisconsin', NULL, NULL),
(50, 'Wyoming', NULL, NULL),
(51, 'American Samoa', NULL, NULL),
(52, 'District of Columbia', NULL, NULL),
(53, 'Federated States of Micronesia', NULL, NULL),
(54, 'Guam', NULL, NULL),
(55, 'Marshall Islands', NULL, NULL),
(56, 'Northern Mariana Islands', NULL, NULL),
(57, 'Palau', NULL, NULL),
(58, 'Puerto Rico', NULL, NULL),
(59, 'Virgin Islands', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `studios`
--

CREATE TABLE `studios` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `owner_first_name` varchar(255) NOT NULL,
  `owner_last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `address` varchar(255) NOT NULL,
  `city` varchar(255) NOT NULL,
  `zip` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `studios`
--

INSERT INTO `studios` (`id`, `state_id`, `country_id`, `name`, `owner_first_name`, `owner_last_name`, `email`, `phone`, `address`, `city`, `zip`, `created_at`, `updated_at`, `deleted_at`, `location`) VALUES
(1, 35, NULL, 'Sandra Howe', 'Bethany', 'Krajcik', '<EMAIL>', '************', '3860 Adriel Park', 'Greensboro', '02828-0436', '2024-12-16 10:02:34', '2024-12-16 10:02:34', NULL, NULL),
(2, 4, NULL, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 05:49:31', '2024-12-30 05:49:31', NULL, NULL),
(3, 43, NULL, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 07:19:03', '2024-12-30 07:19:03', NULL, NULL),
(4, 5, 2, 'test', 'test', 'qweq', '<EMAIL>', '78678', 'qwe', 'qweqwe', 'qwe', '2024-12-30 08:30:06', '2024-12-30 08:30:06', NULL, NULL),
(5, 4, 5, 'test', 'test', 'test', '<EMAIL>', 'test', 'test', 'test', 'test', '2024-12-30 08:30:43', '2024-12-30 08:30:43', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `suppliers`
--

CREATE TABLE `suppliers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `zip` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `location` varchar(255) NOT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `suppliers`
--

INSERT INTO `suppliers` (`id`, `state_id`, `country_id`, `name`, `email`, `phone`, `address`, `city`, `zip`, `is_active`, `location`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 192, 'Anlap', '<EMAIL>', '+84 38 4545 777', 'The Prince Residence, 17-19-21 Nguyen Van Troi St.', 'Ho Chi Minh', '00700', 1, 'International', NULL, '2024-12-23 04:55:30', '2024-12-23 04:55:30'),
(2, 29, 152, 'V-test supplier', '<EMAIL>', '+****************', 'Nisi earum ipsum mo', 'Et blanditiis nobis', '69430', 0, 'International', NULL, '2025-01-03 00:50:19', '2025-01-08 10:17:36'),
(3, 52, NULL, 'Lindsey Stroman', '<EMAIL>', '************', 'asdasd', 'asdasd', '123123', 1, 'International', NULL, '2025-01-08 11:28:47', '2025-01-08 11:28:47'),
(4, NULL, NULL, 'asdasd', '<EMAIL>', '123123', NULL, NULL, NULL, 1, 'USA', NULL, '2025-01-09 05:47:32', '2025-01-09 05:47:32');

-- --------------------------------------------------------

--
-- Table structure for table `telescope_entries`
--

CREATE TABLE `telescope_entries` (
  `sequence` bigint(20) UNSIGNED NOT NULL,
  `uuid` char(36) NOT NULL,
  `batch_id` char(36) NOT NULL,
  `family_hash` varchar(255) DEFAULT NULL,
  `should_display_on_index` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `content` longtext NOT NULL,
  `created_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `telescope_entries_tags`
--

CREATE TABLE `telescope_entries_tags` (
  `entry_uuid` char(36) NOT NULL,
  `tag` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `telescope_monitoring`
--

CREATE TABLE `telescope_monitoring` (
  `tag` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 0,
  `is_contact` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `email_verified_at`, `password`, `phone`, `remember_token`, `is_active`, `is_contact`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Super Admin', '', '<EMAIL>', '2024-12-12 11:46:22', '$2y$10$irx52rrQMQubIuckvZlTD.C6Fo/WWxBn95uQPt8PeCVdzafNwQz8S', '12345689', 'NSZyzN5ncGppnwHYt5F1UTrT9BH3JXBr59UfZVYa7QCQULxfFshHVg6vYHDq', 0, 0, '2024-12-12 11:46:22', '2025-01-16 02:19:16', NULL),
(2, 'Sebastien', 'Lagree', '<EMAIL>', NULL, '$2y$10$S52EGQaCOsf7FON5i/lsVuS2iazC8tgrhvjR4XRSzNvuwgM/Bbksa', '123', NULL, 1, 0, '2024-12-12 03:51:42', '2024-12-31 05:22:01', NULL),
(3, 'Michael', 'Something', '<EMAIL>', NULL, '$2y$10$uyWRF9B4UINIZvL5P55dTufg58OxwsU1jlsBXaYw4vDk9jFVQsDrm', '1233', NULL, 1, 0, '2024-12-18 07:21:01', '2024-12-21 00:47:17', NULL),
(4, 'Sasha', 'BIRON', '<EMAIL>', NULL, '$2y$10$W4QO2Sk.6gyGqk4uhFdIo.4qevRl8UH7LeFEsCwyLMUgGsz4cDK1W', '555', NULL, 1, 0, '2024-12-21 13:38:09', '2024-12-21 13:38:09', NULL),
(5, 'Proba', '123', '<EMAIL>', NULL, '$2y$10$B3Daqxbdpck1XF8/VVz2HeNxW7hh2NwxAlOk7IqysdZOCDhW/6IPO', '4564654564', NULL, 1, 0, '2024-12-23 06:06:32', '2024-12-23 06:06:32', NULL),
(6, 'Julie', 'Fayer', '<EMAIL>', NULL, '$2y$10$COcFwmcbS5eDbUY1yG9f4O4Lw.bP9VvchG3ciX130dl8nP04Sz0su', '***********', NULL, 1, 0, '2024-12-26 07:35:41', '2025-01-03 17:25:31', NULL),
(7, 'Romy', 'Drouby', '<EMAIL>', NULL, '$2y$10$TexBosVSCwf6dLPySOGE5eX5M.sf47iLtTuVFgH5QO.3fcqzDkTWW', '+974-5545-7144', NULL, 1, 0, '2024-12-26 11:10:13', '2024-12-31 03:20:55', NULL),
(8, 'Caitlyn', 'Coyle', '<EMAIL>', NULL, '$2y$10$r6dw6y8SlQAfocWVAg6TSOkPMtCbr8h4iEVY57ZNafmJpKg66WAyO', '555', NULL, 1, 0, '2024-12-27 10:12:58', '2024-12-30 05:03:51', NULL),
(9, 'Luka', 'Anđelković', '<EMAIL>', NULL, '$2y$10$UN6J2dEAQQpJKylYCe7gKOFQObBBiSRRJyz.ECbAwDMQwe/lKZeD.', '0631399084', NULL, 1, 0, '2024-12-30 05:40:40', '2024-12-30 06:56:49', NULL),
(10, 'Luka', 'Anđelković', '<EMAIL>', NULL, '$2y$10$KgBdiv8iRL2hgmHkRLFKAOAa.rsqGAAj4vOIK291Ar40j1eh6uchG', '0631399084', NULL, 1, 0, '2024-12-30 06:07:36', '2024-12-30 06:07:36', NULL),
(11, 'Luka', 'Anđelković', '<EMAIL>', NULL, '$2y$10$QqAroZa9gnNlrrEyHSKU5.CmnZmH.J1Q2qRGP0fMJb1OVjTxSHYne', '0631399084', NULL, 1, 0, '2024-12-30 06:08:27', '2024-12-30 06:08:27', NULL),
(12, 'Luka', 'Anđelković', '<EMAIL>', NULL, '$2y$10$BwhPAhYPtGafpYa7tEYjFOiNYd7dnTPQrlNc4fJUhQ0OxiZbmy./q', '78787897', NULL, 1, 0, '2024-12-31 05:22:53', '2024-12-31 05:22:53', NULL),
(13, 'dada', 'dada', '<EMAIL>', NULL, '$2y$10$rjJY9KlfrOY9P587XKL2SueHPyezpQ1sWUUjNYmZs9qG.0Q9erlKq', '2222', NULL, 1, 0, '2024-12-31 06:48:34', '2024-12-31 06:48:34', NULL),
(14, 'dgdg', 'dgdgh', '<EMAIL>', NULL, '$2y$10$dM/VkL8kCm.ClMGqE8F5MuXL1F9ozw39dAjtxT97tXJfzcyWpdqxS', 'dh', NULL, 1, 0, '2024-12-31 07:27:22', '2024-12-31 07:27:22', NULL),
(15, 'milan', 'milan', '<EMAIL>', NULL, '$2y$10$fFIvGIiPJyltAx0.yIE2s.QBSdPiWcwxy.lX8gojunOFPTWaqmLd.', '123', NULL, 1, 0, '2024-12-31 07:37:10', '2024-12-31 07:37:10', NULL),
(16, 'test', 'test', '<EMAIL>', NULL, '$2y$10$t04XSPNUNwB0mCQy/QGYZu/1zfGaoG4258M6nrogoOzTcDy7T.By6', 'test', NULL, 1, 0, '2024-12-31 07:40:56', '2024-12-31 07:40:56', NULL),
(17, 'test', 'test', '<EMAIL>', NULL, '$2y$10$vVGzfxz7urW0arDG6vnCpec0TRYm/PjoKZaCti3RW28yhKwlq4ewW', 'test', NULL, 0, 0, '2024-12-31 07:41:59', '2024-12-31 07:41:59', NULL),
(18, 'Milan', 'Ckomi', '<EMAIL>', NULL, '$2y$10$iOUAd1SNfHT8kb9fuKGjyOHkrIUAKPLnktxTD6DMgqh0psdEUPD7S', '1230002', NULL, 1, 0, '2024-12-31 07:42:21', '2024-12-31 07:42:21', NULL),
(19, 'kdoakod', 'kodako', '<EMAIL>', NULL, '$2y$10$LQv1kfYI2OWa7YPefoVyB.RcU.isUzjyXOgGvrS09oTcSa7ktgiua', '20202020', NULL, 1, 0, '2024-12-31 07:49:31', '2024-12-31 07:49:31', NULL),
(20, 'No name', 'No name', '<EMAIL>', NULL, '$2y$10$aQgT/ClMpjfe5eHAAqoMhur435byUwxtwNXzDdV/9luk6CNld4rTi', 'No name', NULL, 1, 0, '2024-12-31 07:54:00', '2024-12-31 07:54:00', NULL),
(21, 'test', 'test', '<EMAIL>', '2024-12-12 11:46:22', '$2y$10$8ObqWvcVrFB0rREw4JKB2ezlL.7..Xi4LMMVHOW.XkWezIuSTYWb6', 'tetst', NULL, 0, 0, '2024-12-31 08:42:20', '2025-01-09 02:29:15', NULL),
(22, 'Marco', 'Sarco', '<EMAIL>', NULL, '$2y$10$S5GKJdBx.xGlEY5BsxcGl.4Rgqf/Pu8COjx3VuMopjT8L0msYwhsG', '12121212', NULL, 1, 0, '2024-12-31 09:24:33', '2024-12-31 09:24:33', NULL),
(23, 'dada', 'rara', '<EMAIL>', NULL, '$2y$10$zv.hvoT.73YDv/IeLeEMpe3mRpWFSbnNbWhFq2JB7VwGle0enD.CO', '234556', NULL, 1, 0, '2024-12-31 09:26:55', '2025-01-04 01:44:07', NULL),
(24, 'Denise', 'Chakoian', '<EMAIL>', NULL, '$2y$10$phQ.F4szXdN4d.tZZAQkZ.9lHpxU0fCGvNFz3qDRQWH03HlYZ2436', '555-444-333', NULL, 1, 0, '2025-01-03 13:57:41', '2025-01-03 13:57:41', NULL),
(25, 'Sarah', 'Hill', '<EMAIL>', NULL, '$2y$10$w7uWowOL4ZZPYaWs2ZTUtOb1m5LtlgMu9Qs2./TAO1Rcf3jOI7LLq', '1-604-897-4988', NULL, 1, 0, '2025-01-06 10:49:29', '2025-01-06 10:49:29', NULL),
(26, 'Janick', 'Dibbert', '<EMAIL>', NULL, '$2y$10$tcu0gllVOeNCceKEY1qQm.kuHKMiHSSh1jpp1b1Rbs02MJVru/vi2', '900-868-6984', NULL, 1, 0, '2025-01-08 11:37:10', '2025-01-08 11:37:10', NULL),
(27, 'Berniece', 'Bergstrom', '<EMAIL>', NULL, '$2y$10$C5tZ6CPo3NNzFrEH9z0f0es.6pTyCOathVlC00d.8qKkpVsUz4jI6', '179-756-2230', NULL, 1, 0, '2025-01-08 11:37:18', '2025-01-09 05:42:35', NULL),
(28, 'Sebastien', 'Lagree', '<EMAIL>', '2024-12-12 11:46:22', '$2y$10$x0wp2mmBNL2JTt1F8QDihOcysoWc1amlUTWda6BqXEZkPAwDfl7l6', '1-604-897-4988', NULL, 0, 0, '2025-01-06 10:49:29', '2025-01-06 10:49:29', NULL),
(29, 'Jessica', 'Luna', 'jessica@‌imslagree.com', '2024-12-12 11:46:22', '$2y$10$VE2kOVqFnIy9LlD3dktwqO5/dGy3kjbYHMpNB9gjJ5.qrDVcJBCyW', '1-604-897-4988', NULL, 0, 0, '2025-01-06 10:49:29', '2025-01-06 10:49:29', NULL),
(30, 'Rachel', 'Lagree', 'rachel@‌imslagree.com', '2024-12-12 11:46:22', '$2y$10$PVoiDNcdLw.oNwpsAF4hee28w4MpH6rvS3ou0KmkyTmholxDU1xAq', '1-604-897-4988', NULL, 0, 0, '2025-01-06 10:49:29', '2025-01-06 10:49:29', NULL),
(31, 'Chris', 'Thompson', 'chris@‌imslagree.com', '2024-12-12 11:46:22', '$2y$10$xcEu7cq2GtlXE6AYKaIc6ujbb7m1V9FmXEU1xrvWYmhNTifgral1a', '1-604-897-4988', NULL, 0, 0, '2025-01-06 10:49:29', '2025-01-06 10:49:29', NULL),
(32, 'Tasdasd', 'asdasda', '<EMAIL>', NULL, '$2y$10$lrBzbfTBrqHI8iMBwlPsZ.3ded3ErMSNpLLg3gtFeKF8mbi6S8yhy', 'sdasda', NULL, 1, 0, '2025-01-09 05:43:05', '2025-01-13 07:09:44', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `webhooks`
--

CREATE TABLE `webhooks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_notifications_customer_id_foreign` (`customer_id`);

--
-- Indexes for table `admin_settings`
--
ALTER TABLE `admin_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_settings_admin_id_foreign` (`admin_id`),
  ADD KEY `admin_settings_currency_id_foreign` (`currency_id`);

--
-- Indexes for table `agreements`
--
ALTER TABLE `agreements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `agreements_customer_id_foreign` (`customer_id`);

--
-- Indexes for table `companies`
--
ALTER TABLE `companies`
  ADD PRIMARY KEY (`id`),
  ADD KEY `companies_state_id_foreign` (`state_id`);

--
-- Indexes for table `contact_people`
--
ALTER TABLE `contact_people`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `conversion_rates`
--
ALTER TABLE `conversion_rates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `conversion_rates_currency_id_foreign` (`currency_id`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customers_owner_id_foreign` (`owner_id`),
  ADD KEY `customers_state_id_foreign` (`state_id`),
  ADD KEY `customers_country_id_foreign` (`country_id`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email_templates_unique_name_unique` (`unique_name`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `files`
--
ALTER TABLE `files`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invoices_number_unique` (`number`),
  ADD KEY `invoices_payment_id_foreign` (`payment_id`),
  ADD KEY `invoices_customer_id_foreign` (`customer_id`);

--
-- Indexes for table `invoice_products`
--
ALTER TABLE `invoice_products`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `invoice_products_number_unique` (`number`),
  ADD KEY `invoice_products_company_id_foreign` (`company_id`),
  ADD KEY `invoice_products_customer_id_foreign` (`customer_id`);

--
-- Indexes for table `invoice_product_items`
--
ALTER TABLE `invoice_product_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `invoice_product_items_invoice_product_id_foreign` (`invoice_product_id`),
  ADD KEY `invoice_product_items_product_id_foreign` (`product_id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `leases`
--
ALTER TABLE `leases`
  ADD PRIMARY KEY (`id`),
  ADD KEY `leases_company_id_foreign` (`company_id`),
  ADD KEY `leases_studio_id_foreign` (`studio_id`),
  ADD KEY `leases_customer_id_foreign` (`customer_id`),
  ADD KEY `leases_machine_id_foreign` (`machine_id`),
  ADD KEY `leases_initial_currency_id_foreign` (`initial_currency_id`);

--
-- Indexes for table `licenses`
--
ALTER TABLE `licenses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `licenses_company_id_foreign` (`company_id`),
  ADD KEY `licenses_customer_id_foreign` (`customer_id`),
  ADD KEY `licenses_studio_id_foreign` (`studio_id`),
  ADD KEY `licenses_initial_currency_id_foreign` (`initial_currency_id`);

--
-- Indexes for table `machines`
--
ALTER TABLE `machines`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indexes for table `notes`
--
ALTER TABLE `notes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notes_customer_id_foreign` (`customer_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `orders_company_id_foreign` (`company_id`),
  ADD KEY `orders_supplier_id_foreign` (`supplier_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_items_order_id_foreign` (`order_id`),
  ADD KEY `FK_order_items_suppliers_foreign_key` (`product_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `payments_license_id_foreign` (`license_id`),
  ADD KEY `payments_lease_id_foreign` (`lease_id`),
  ADD KEY `payments_customer_id_foreign` (`customer_id`),
  ADD KEY `payments_machine_id_foreign` (`machine_id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_products_suppliers_foreign_key` (`supplier_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indexes for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indexes for table `states`
--
ALTER TABLE `states`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `studios`
--
ALTER TABLE `studios`
  ADD PRIMARY KEY (`id`),
  ADD KEY `studios_country_id_foreign` (`country_id`),
  ADD KEY `studios_state_id_foreign` (`state_id`);

--
-- Indexes for table `suppliers`
--
ALTER TABLE `suppliers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `suppliers_email_unique` (`email`),
  ADD KEY `suppliers_state_id_foreign` (`state_id`),
  ADD KEY `suppliers_country_id_foreign` (`country_id`);

--
-- Indexes for table `telescope_entries`
--
ALTER TABLE `telescope_entries`
  ADD PRIMARY KEY (`sequence`),
  ADD UNIQUE KEY `telescope_entries_uuid_unique` (`uuid`),
  ADD KEY `telescope_entries_batch_id_index` (`batch_id`),
  ADD KEY `telescope_entries_family_hash_index` (`family_hash`),
  ADD KEY `telescope_entries_created_at_index` (`created_at`),
  ADD KEY `telescope_entries_type_should_display_on_index_index` (`type`,`should_display_on_index`);

--
-- Indexes for table `telescope_entries_tags`
--
ALTER TABLE `telescope_entries_tags`
  ADD PRIMARY KEY (`entry_uuid`,`tag`),
  ADD KEY `telescope_entries_tags_tag_index` (`tag`);

--
-- Indexes for table `telescope_monitoring`
--
ALTER TABLE `telescope_monitoring`
  ADD PRIMARY KEY (`tag`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `webhooks`
--
ALTER TABLE `webhooks`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_settings`
--
ALTER TABLE `admin_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `agreements`
--
ALTER TABLE `agreements`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `companies`
--
ALTER TABLE `companies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `contact_people`
--
ALTER TABLE `contact_people`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `conversion_rates`
--
ALTER TABLE `conversion_rates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=196;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `files`
--
ALTER TABLE `files`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

--
-- AUTO_INCREMENT for table `invoice_products`
--
ALTER TABLE `invoice_products`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `invoice_product_items`
--
ALTER TABLE `invoice_product_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=92;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `leases`
--
ALTER TABLE `leases`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `licenses`
--
ALTER TABLE `licenses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `machines`
--
ALTER TABLE `machines`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- AUTO_INCREMENT for table `notes`
--
ALTER TABLE `notes`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=35;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=175;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=234;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `states`
--
ALTER TABLE `states`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT for table `studios`
--
ALTER TABLE `studios`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `suppliers`
--
ALTER TABLE `suppliers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `telescope_entries`
--
ALTER TABLE `telescope_entries`
  MODIFY `sequence` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `webhooks`
--
ALTER TABLE `webhooks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  ADD CONSTRAINT `admin_notifications_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`);

--
-- Constraints for table `admin_settings`
--
ALTER TABLE `admin_settings`
  ADD CONSTRAINT `admin_settings_admin_id_foreign` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `admin_settings_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`);

--
-- Constraints for table `agreements`
--
ALTER TABLE `agreements`
  ADD CONSTRAINT `agreements_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`);

--
-- Constraints for table `companies`
--
ALTER TABLE `companies`
  ADD CONSTRAINT `companies_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`);

--
-- Constraints for table `conversion_rates`
--
ALTER TABLE `conversion_rates`
  ADD CONSTRAINT `conversion_rates_currency_id_foreign` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`);

--
-- Constraints for table `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `customers_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`),
  ADD CONSTRAINT `customers_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `customers_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`);

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `invoices_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `invoices_payment_id_foreign` FOREIGN KEY (`payment_id`) REFERENCES `payments` (`id`);

--
-- Constraints for table `invoice_products`
--
ALTER TABLE `invoice_products`
  ADD CONSTRAINT `invoice_products_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  ADD CONSTRAINT `invoice_products_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`);

--
-- Constraints for table `invoice_product_items`
--
ALTER TABLE `invoice_product_items`
  ADD CONSTRAINT `invoice_product_items_invoice_product_id_foreign` FOREIGN KEY (`invoice_product_id`) REFERENCES `invoice_products` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `invoice_product_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `leases`
--
ALTER TABLE `leases`
  ADD CONSTRAINT `leases_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  ADD CONSTRAINT `leases_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `leases_initial_currency_id_foreign` FOREIGN KEY (`initial_currency_id`) REFERENCES `currencies` (`id`),
  ADD CONSTRAINT `leases_machine_id_foreign` FOREIGN KEY (`machine_id`) REFERENCES `machines` (`id`),
  ADD CONSTRAINT `leases_studio_id_foreign` FOREIGN KEY (`studio_id`) REFERENCES `studios` (`id`);

--
-- Constraints for table `licenses`
--
ALTER TABLE `licenses`
  ADD CONSTRAINT `licenses_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  ADD CONSTRAINT `licenses_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `licenses_initial_currency_id_foreign` FOREIGN KEY (`initial_currency_id`) REFERENCES `currencies` (`id`),
  ADD CONSTRAINT `licenses_studio_id_foreign` FOREIGN KEY (`studio_id`) REFERENCES `studios` (`id`);

--
-- Constraints for table `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notes`
--
ALTER TABLE `notes`
  ADD CONSTRAINT `notes_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`);

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`),
  ADD CONSTRAINT `orders_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`);

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `FK_order_items_suppliers_foreign_key` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`),
  ADD CONSTRAINT `order_items_order_id_foreign` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`);

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `payments_lease_id_foreign` FOREIGN KEY (`lease_id`) REFERENCES `leases` (`id`),
  ADD CONSTRAINT `payments_license_id_foreign` FOREIGN KEY (`license_id`) REFERENCES `licenses` (`id`),
  ADD CONSTRAINT `payments_machine_id_foreign` FOREIGN KEY (`machine_id`) REFERENCES `machines` (`id`);

--
-- Constraints for table `products`
--
ALTER TABLE `products`
  ADD CONSTRAINT `FK_products_suppliers_foreign_key` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`),
  ADD CONSTRAINT `products_supplier_id_foreign` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`);

--
-- Constraints for table `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `studios`
--
ALTER TABLE `studios`
  ADD CONSTRAINT `studios_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`),
  ADD CONSTRAINT `studios_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `suppliers`
--
ALTER TABLE `suppliers`
  ADD CONSTRAINT `suppliers_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`),
  ADD CONSTRAINT `suppliers_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`);

--
-- Constraints for table `telescope_entries_tags`
--
ALTER TABLE `telescope_entries_tags`
  ADD CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
