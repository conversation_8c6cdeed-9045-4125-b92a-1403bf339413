<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

class Invoice extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'customer_id',
        'payment_id',
        'number'
    ];

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    public static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            DB::transaction(function () use ($invoice) {
                $lastInvoiceNumber = DB::table('invoices')->lockForUpdate()->max('number') ?? 0;
                $invoice->number   = $lastInvoiceNumber + 1;
            });
        });
    }

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'payment'   => ['id', 'invoices.payment_id'],
    ];

    public function getPrimaryAndForeignKeys(string $relation): array
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function sortableFields(): array
    {
        return [
            'id',
            'number',
            'payment.payment_amount',
            'payment.payment_date'
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function getFormattedNumberAttribute(): string
    {
        return str_pad($this->number, 4, '0', STR_PAD_LEFT);
    }

    public function payment(): BelongsTo
    {
        return $this->belongsTo(Payment::class);
    }
}
