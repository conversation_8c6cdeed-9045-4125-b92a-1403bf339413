<?php

namespace App\Services\Admin\InvoiceTemplate;

use App\Models\InvoiceTemplate;
use App\Models\Customers;
use Illuminate\Pagination\LengthAwarePaginator;

interface IInvoiceTemplateService
{
    public function store(array $data): void;
    public function update(array $data, InvoiceTemplate $invoiceTemplate): void;
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $customerId): LengthAwarePaginator;
    public function delete(InvoiceTemplate $invoiceTemplate): void;

}
