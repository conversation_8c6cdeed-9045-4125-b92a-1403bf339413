<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

//Route::post('test-adobe', function () {
//    \App\Jobs\SendAgreement::dispatch(\App\Models\Agreement::LICENSE_TEMPLATE, \App\Models\Customer::first(), \App\Models\License::first(), \App\Models\Company::first());
//});

Route::post('/adobe-sign-webhook', [\App\Http\Controllers\Admin\AdobeWebhookController::class, 'handleWebhook']);
Route::get('/adobe-sign-webhook', [\App\Http\Controllers\Admin\AdobeWebhookController::class, 'verifyWebhook']);

