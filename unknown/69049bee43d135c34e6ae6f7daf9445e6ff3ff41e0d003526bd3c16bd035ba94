<h5 class="mb-0 fs-14px text-uppercase custom-fw-500 pt-45 pb-45">
    {{ __('Notes') }}{{ ' (' . $notes->count() . ')' }}
</h5>

<form action="{{ route('admin.license-notes.store', ['license' => $license, 'customer' => $customer]) }}" method="POST">
    @csrf
    <textarea class="form-text form-control py-4" style="max-width: none;min-height: 180px;max-height: 180px;" name="body"
        id="note" rows="10" placeholder="Enter"></textarea>
    <button type="submit" class="btn btn-primary mt-5 fw-normal">{{ __('Save') }}</button>
</form>
<div class="border-bottom border-light mt-7"></div>
<div class="internal-notes">
    @foreach ($notes as $note)
        <div class="border-bottom border-light pt-45 pb-50 px-3 px-sm-0">
            {{-- <p class="text-secondary mb-2">{{ $note->updated_at->format('m/d/Y') }}</p> --}}
            <p class="note fs-14px lh-25 mb-4">{{ $note->body }}</p>

            <span class="text-secondary text-decoration-underline">
                <a href="javascript:;" class="delete-note px-0" data-bs-toggle="modal"
                    data-bs-target="#deleteModal{{ 'Note' }}{{ $note->id }}">
                    <button type="button"
                        class="btn btn-small btn-ghost-light-bigger fw-normal ">{{ __('Delete') }}</button>
                </a>
            </span>
        </div>
        @include('partials.modals.delete', [
            'type' => 'Note',
            'id' => $note->id,
            'route' => route('admin.license-notes.destroy', ['license' => $license, 'customer'=>$customer,  'notes' => $note]),
            'title' => 'Note',
        ])
    @endforeach
</div>
