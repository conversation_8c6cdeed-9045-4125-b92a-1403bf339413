<?php

namespace App\Http\Controllers;

use Stripe\Stripe;
use Stripe\Webhook;
use Illuminate\Http\Request;
use App\Models\License;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\SignatureVerificationException;

class StripeWebhookController extends Controller
{
    // public function handleWebhook(Request $request)
    // {
    //     $endpointSecret = env('STRIPE_WEBHOOK_SECRET_LOCAL');
    //     // $endpointSecret = env('STRIPE_WEBHOOK_SECRET_LIVE');
    //     $sigHeader = $request->header('Stripe-Signature');
    //     $payload = $request->getContent();

    //     try {
    //         $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
    //     } catch (SignatureVerificationException $e) {
    //         Log::error("Webhook signature verification failed: " . $e->getMessage());
    //         return response()->json(['error' => 'Invalid signature'], 400);
    //     } catch (\Exception $e) {
    //         Log::error("Error processing webhook: " . $e->getMessage());
    //         return response()->json(['error' => 'Webhook processing failed'], 500);
    //     }

    //     Log::info("Webhook received: " . json_encode($event));

    //     // Process the event after verification
    //     if ($event->type === 'checkout.session.completed') {
    //         $sessionId = $event->data->object->id;
    //         $license = License::where('stripe_session_id', $sessionId)->first();
    //         if ($license) {
    //             $license->updateQuietly(['is_active' => true]);
    //             Log::info("License {$license->id} activated after payment.");
    //             return redirect()->to(url("admin/customers-dashboard/{$license->customer_id}?tab=licence"));
    //         } else {
    //             Log::warning("License not found for session ID: $sessionId");
    //         }
    //     }
    //     return response()->json(['status' => 'success']);
    // }
}
