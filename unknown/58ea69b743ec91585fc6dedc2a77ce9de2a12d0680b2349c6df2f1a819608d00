@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('Checkout') }}</h3>
        </div>
    </div>

    <div class="mb-6">
        <form id="payment-form">
            <label>Email <input type="text" id="email" placeholder="<EMAIL>"/></label>
            <div type="text" id="email-errors"></div>
            <h4>Payment</h4>
            <div id="payment-element">
              <!--Stripe.js injects the Payment Element-->
            </div>
            <button id="submit">
              <div class="spinner hidden" id="spinner"></div>
              <span id="button-text">Pay now</span>
            </button>
            <div id="payment-message" class="hidden"></div>
        </form>
    </div>
@endsection
{{-- <script type="module">
document.addEventListener('DOMContentLoaded', function () {
    const stripe = Stripe("pk_test_0jQdP7IaDF9MMBGYzyz6bGhk00KmrxMqmY");

    const fetchClientSecret = () =>
    fetch('{{ route('payment.processCheckout') }}', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
    })
    .then((r) => r.json())
    .then((r) => r.clientSecret);

    checkout = await stripe.initCheckout({
        fetchClientSecret,
        elementsOptions: { appearance },
    });

    const emailInput = document.getElementById("email");
    const emailErrors = document.getElementById("email-errors");

    emailInput.addEventListener("input", () => {
        // Clear any validation errors
        emailErrors.textContent = "";
    });

    emailInput.addEventListener("blur", async () => {
        const newEmail = emailInput.value;
        if (!newEmail) {
            return;
        }

        const { isValid, message } = await validateEmail(newEmail);
        if (!isValid) {
            emailErrors.textContent = message;
        }
    });

    const paymentElement = checkout.createPaymentElement();
    paymentElement.mount("#payment-element");

    // 
        

    async function handleSubmit(e) {
        e.preventDefault();
        setLoading(true);

        const email = document.getElementById("email").value;
        const { isValid, message } = await validateEmail(email);
        if (!isValid) {
            showMessage(message);
            setLoading(false);
            return;
        }

        const { error } = await checkout.confirm();

        // This point will only be reached if there is an immediate error when
        // confirming the payment. Otherwise, your customer will be redirected to
        // your `return_url`. For some payment methods like iDEAL, your customer will
        // be redirected to an intermediate site first to authorize the payment, then
        // redirected to the `return_url`.
        showMessage(error.message);

        setLoading(false);
    }
        

});
 --}}</script>
