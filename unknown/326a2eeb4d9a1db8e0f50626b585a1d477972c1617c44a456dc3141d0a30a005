<ul class="list-group checkbox-group {{$field_class ?? ''}} checkbox-group-extended @if($errors->has($field_name)) has-danger @endif">
    @foreach($values as $value)

        <li class="list-group-item">
            <span></span>
            <input class="form-check-input me-1" id="{{ $id . '_' . $value->id}}" name="{{$field_name}}" placeholder="{{$field_label}}"
                   value="{{$value->id}}"
                   type="checkbox"
                @readonly(isset($readonly) && $readonly == 'readonly')
                @disabled(isset($disabled) && $disabled == 'disabled')
                @checked(in_array($value->id, $selected))
                @required(isset($required) && $required == 'required')
            aria-controls="#{{$id}}_{{$value->id}}_count"
            >
            <label class="form-check-label stretched-link" for="{{$field_name}}">{{ $value->$field }}</label>

            @include('partials.forms.input', [
                'field_name' => $id . '_' . $value->id . '_count',
                'field_label' => null,
                'field_type' => 'number',
                'placeholder' => '# of ' . $value->$field,
                'field_value' => $values_count[$value->id] ?? null,
                'field_class' => in_array($value->id, $selected)? '' : 'd-none-soft',
                'min_num' => 0,
            ])
        </li>
    @endforeach

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</ul>
