<?php

namespace App\Services\Admin\Dashboard;

use App\Models\Currency;
use App\Models\Order;
use App\Models\Payment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

interface IDashboardService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator;
    public function getLastTenPayments(Currency $currentCurrency): Collection;
    public function countCustomer(): int;
    public function countActiveLicenses(): array;
    public function countActiveLeases(): int;
    public function sumPayments(string $period, Currency $currentCurrency): array;
}
