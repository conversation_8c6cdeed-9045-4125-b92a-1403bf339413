@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('Products') }}</h3>
            <a href="{{ url()->previous() }}" class="back-link">← Back</a>
        </div>
        <a href="{{ route('admin.products.create') }}" class="btn btn-primary index-addnew-desk">{{ __('ADD NEW') }}</a>
        <a href="{{ route('admin.products.create') }}" class="btn btn-primary index-addnew-mob">+</a>
    </div>

    <div class="nr-items">
        <h5 id="productsCount"></h5>

        <div class="sortbysearch">
            <div class="ms-auto lg-search h-40px">
                <input class="typeahead form-control" id="search" type="text" name="lagree-search" placeholder="Search"
                       autocomplete="off">
                <span class="lg-search-ico"><img src="/search.svg" alt="search" class=""></span>
            </div>
        </div>
    </div>

    <div id="products-table" class="entity-table bg--loading-black"></div>
@endsection
<script type="module">
    document.addEventListener('DOMContentLoaded', function () {
        const searchInput = $('#search');
        const searchIcon = $('.lg-search-ico');
        let searchData = '';
        let orderParam = 'name';
        let orderType = 'asc';
        const sortIcon = $('.sort-icon');
        const productsTable = $('#products-table');
        const html = $('html, body');

        @php
            $route = route('admin.products.search');
        @endphp
        function fetchData(url) {
            let perPage = $('.pagination-select').val() ?? 10;
            $.ajax({
                url: url,
                data: {
                    search_data: searchData,
                    order_param: orderParam,
                    order_type: orderType,
                    per_page: perPage,
                },
                success: function (data) {
                    setTimeout(function () {
                        productsTable.removeClass('bg--loading-black').html(data);
                        console.log(data);
                        productsTable.trigger('resultLoaded');
                        $('.pagination-select').val(perPage);
                    }, 300);
                },
                error: function () {
                    flasherJS.error('', 'Error occurred while loading data.');
                }
            });
        }

        $('body').on('change', '.pagination-select', function (e) {
            fetchData("{{ $route }}");
        });

        $('body').on('click', '.pagination a', function (e) {
            e.preventDefault();
            const url = $(this).attr('href');
            fetchData(url);
        });

        searchInput.on('input', debounce(function () {
            searchData = $(this).val();
            fetchData("{{ $route }}");
        }, 500));

        searchInput.on('click focus', function(e){
            e.stopPropagation();
        });

        productsTable.on('resultLoaded', function () {
            setSortIcon();

            $('#products-table .sortable-list-header').click(function () {
                if (!$(this).get(0)?.id) {
                    return;
                }
                orderParam = $(this).get(0).id;
                orderType = !$(this).children('.sort-icon').get(0).classList.contains('asc')
                || $(this).children('.sort-icon').get(0).classList.contains('desc')
                    ? 'asc' : 'desc';

                setSortIcon();

                fetchData("{{ $route }}");
            });
        });

        function debounce(func, wait, immediate) {
            let timeout;
            return function () {
                let context = this, args = arguments;
                let later = function () {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                let callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        function setSortIcon() {
            $('#products-table .sortable-list-header').each(function () {
                if ($(this).get(0)?.id !== orderParam) {
                    return;
                }
                sortIcon.removeClass('asc desc'); // Remove existing sort classes
                $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(orderType); // Add active sort class
            });
        }

        searchIcon.click(function (e) {
            e.stopPropagation();
            searchInput.get(0).classList.toggle("lg-search-expanded");

            if (searchData !== '' && !searchInput.get(0).classList.contains("lg-search-expanded")) {
                searchInput.val('');
                searchData = '';
                fetchData("{{ $route }}");
            }else{
                searchInput.focus();
            }
        });

        html.click(function (e) {
            e.stopPropagation();
            searchInput.get(0).classList.remove("lg-search-expanded");
            searchInput.val('');
            searchData = '';
        });

        // Initial load
        fetchData("{{ $route }}");
    });
</script>
