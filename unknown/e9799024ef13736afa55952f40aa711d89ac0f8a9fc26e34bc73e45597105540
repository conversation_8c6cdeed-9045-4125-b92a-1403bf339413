<?php

namespace App\Services\Admin\Notification;

use App\Models\AdminNotification;
use App\Models\Company;
use Illuminate\Pagination\LengthAwarePaginator;

interface INotificationService
{
    public function store(array $data);
    public function update(array $data, AdminNotification $notification);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator;
    public function delete(AdminNotification $notification);

}
