<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Bundle\StoreBundleRequest;
use App\Http\Requests\Admin\Bundle\UpdateBundleRequest;
use App\Models\Bundle;
use App\Models\Products;
use App\Services\Admin\Bundle\IBundleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;
use URL;

class BundleController extends Controller
{
    private IBundleService $bundleService;

    public function __construct(
        IBundleService $bundleService,
    )
    {
        $this->bundleService = $bundleService;
    }

    public function index(): View
    {
        // $bundles = Bundle::all();
        // return view('admin.bundles.index', compact('bundles'));

        return view('admin.bundles.index');
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $bundles = $this->bundleService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
        );

        // if (!isset($page) || $page == 1) {
        //     $sequential_id = 1;
        // } else {
        //     $sequential_id = (($page - 1) * $perPage)+1;
        // }

        foreach ($bundles as $bundle) {
            // $bundle['sequential_id'] = $sequential_id++;
            $bundle['orderParam'] = $orderParam;
            $bundle['orderType'] = $orderType;
        }
        $viewContent = view(
            'partials.forms.bundle.bundle-search',
            compact('bundles')
        )->render();

        return response()->json($viewContent);
    }

    public function searchCustomerInvoices(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';
        $customer = $request->get('customerId') ?? '';

        // echo '<pre>';
        // print_r($customer->toArray());
        // die();
        
        $bundles = $this->bundleService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $customer
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($bundles as $bundle) {
            $bundle['sequential_id'] = $sequential_id++;
            $bundle['orderParam'] = $orderParam;
            $bundle['orderType'] = $orderType;
            $product_items = $bundle->load('items.product', 'customer');
            $bundle['amount'] = 0;
            $subTotal = 0;
            $totalDiscount = 0;

            if(count($product_items->items) > 0){
                foreach($product_items->items as $key => $item){
                    $discount_val = 0;
                    if($item->discount_type == "$"){
                        $discount_val = $item->discount;
                    }else{
                        $discount_val = ($item->price * $item->quantity) * ($item->discount / 100);
                    }
    
                    $subTotal += ($item->price * $item->quantity);
                    $totalDiscount += $discount_val;
                }
            }
            
            $tax = $bundle->show_tax ? (($subTotal - $totalDiscount) * 0.095) : 0;
            $tax = $tax < 0 ? 0 : $tax; // Ensure tax is not negative

            $bundle['amount'] = $subTotal - $totalDiscount + $tax + $bundle->shipping_fee + $bundle->handling_fee;
        }

        $viewContent = view('partials.forms.bundles.bundles-search-tab', compact('bundles'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $products = Products::where(['custom_product' => 0, 'category' => 'product'])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);

        return view(
            'admin.bundles.create',
            compact('products')
        );
    }

    public function store(StoreBundleRequest $request): RedirectResponse
    {
        // dd($request->all()); // Dumps and stops execution
        try {
            $this->bundleService->store($request->validated());
            toastr()->addSuccess('', 'Invoice created successfully.');
            return redirect()->route('admin.bundles.index');
        } catch (\Exception $e) {
            // dd($e);
            toastr()->addError('Invoice creation failed');
            return redirect(route('admin.bundles.create'));
        }
    }

    public function edit(Bundle $bundle): View
    {
        $products = Products::where(['custom_product' => 0, 'category' => 'product'])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);

        $bundle = $bundle->load('items.product');
        return view(
            'admin.bundles.edit',
            compact( 'products', 'bundle')
        );
    }

    public function update(Bundle $bundle, UpdateBundleRequest $request): RedirectResponse
    {
        try {
            $this->bundleService->update($request->validated(), $bundle);
            toastr()->addSuccess('', 'Invoice updated successfully.');
            return redirect()->route('admin.bundles.edit', $bundle);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Invoice update failed');
            return redirect()->back();
        }
    }

    public function destroy(Bundle $bundle): RedirectResponse
    {
        try {
            $this->bundleService->delete($bundle);
            toastr()->addSuccess('', 'Invoice deleted successfully.');
            return redirect(route('admin.bundles.index'));
        } catch (\Exception $e) {
            toastr()->addError('Invoice delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request): RedirectResponse
    {
        try {
            $bundleIds = $request->input('selectedItems');
            foreach ($bundleIds as $bundleId) {
                $bundle = Bundle::find($bundleId);
                $this->bundleService->delete($bundle);
            }
            toastr()->addSuccess('', 'Selected bundles deleted successfully.');

            return redirect(route('admin.bundles.index'));
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Bundles delete failed');
            return redirect()->back();
        }
    }
}
