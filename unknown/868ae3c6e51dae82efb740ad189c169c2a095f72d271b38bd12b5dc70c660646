// Wrapper element included just to be able to override default CSS
.date-selector-wrap {
    .datepicker {
        border: 1px solid $light;
        padding: 26px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        border-radius: 0;
        width: 314px;
        top: 50px !important;

        table {
            width: 100%;
            table-layout: fixed;

            tr td, tr td span {
                border-radius: 999px;

                &.active, &.active.active {
                    background-color: $black !important;
                    background-image: none;
                }
                &.active.hover {
                    background-color: $secondary !important;
                    background-image: none;
                }
            }
        }

        td, th {
            padding: 8px;
            font-size: 12px;
        }

        th {
            font-weight: normal;
            color: $secondary;

            &.datepicker-switch {
                color: $black
            }

            &.dow {
                font-size: 0;
                &:first-letter {
                    font-size: 12px;
                }
            }
        }
    }

    .datepicker-dropdown {
        &:before, &:after {
            display: none;
        }
    }

}
