@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('Edit b2c customer') }}</h3>
            <a href="{{ route('admin.customers.b2c.index') }}" class="back-link">← Back</a>
        </div>
    </div>

    {{-- {{ dd($customer->toArray()) }} --}}
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.customers.b2c.update', $customer) }}" id="studio-location-form">
            @csrf
            @method('PUT')

            <h5 class="form-section-title first-title">{{ __('status') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'is_active',
                'field_label' => 'Radio group status',
                'values' => [
                    'status_active' => ['text' => 'Active', 'value' => '1'],
                    'status_inactive' => ['text' => 'Inactive', 'value' => '0'],
                ],
                'field' => 'status',
                'required' => 'required',
                'checked' => $customer->owner->is_active,
            ])

            <h5 class="form-section-title">{{ __('account info') }}</h5>

            {{-- @include('partials.forms.input', [
                    'field_name' => 'customer_name',
                    'field_label' => 'LICENSEE NAME *',
                    'field_type' => 'text',
                    'field_value' => $customer->name,
                ]) --}}

            

            <div class="cust-pass-text">
                <p>The password field must be at least 8 characters.<br>
                    The password field must contain at least one uppercase and one lowercase letter.</p>
            </div>
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button" href="{{ route('admin.customers.edit', $customer->id) }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button" data-bs-toggle="modal"
                    data-bs-target="#deleteModal{{ 'Customer' }}{{ $customer->id }}"
                    data-bs-whatever="">{{ __('TERMINATE') }}
                </button>
            </div>
        </form>
    </div>
@endsection
@include('partials.modals.delete', [
    'type' => 'Customer',
    'id' => $customer->id,
    'route' => route('admin.customers.destroy', ['customer' => $customer]),
    'title' => $customer->name,
])
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        function toggleLocationContainers() {
            if ($('#location_international').is(':checked')) {
                $('#location_usa_container').hide();
                $('#location_international_container').show();
            } else if ($('#location_usa').is(':checked')) {
                $('#location_usa_container').show();
                $('#location_international_container').hide();
            }
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        }

        function toggleShippingLocationContainers() {
            if ($('#shipping_location_international').is(':checked')) {
                $('#shipping_location_usa_container').hide();
                $('#shipping_location_international_container').show();
            } else if ($('#shipping_location_usa').is(':checked')) {
                $('#shipping_location_usa_container').show();
                $('#shipping_location_international_container').hide();
            }
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        }

        $('#location_international, #location_usa').on('click', toggleLocationContainers);
        $('#shipping_location_international, #shipping_location_usa').on('click',
            toggleShippingLocationContainers);

        $(document).ready(toggleLocationContainers);
        $(document).ready(toggleLocationContainers);
    });
</script>
