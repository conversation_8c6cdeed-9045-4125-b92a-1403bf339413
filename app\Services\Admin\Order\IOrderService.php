<?php

namespace App\Services\Admin\Order;

use App\Models\Order;
use Illuminate\Pagination\LengthAwarePaginator;

interface IOrderService
{
    public function store(array $data);
    public function update(array $data, Order $order);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, int|null $supplierId): LengthAwarePaginator;
    public function delete(Order $order);
    public function deleteMultiple(array $ids);
    public function sendToSupplier(Order $order);
}
