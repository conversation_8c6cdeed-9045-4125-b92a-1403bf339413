<?php

namespace App\Http\Requests\Admin\Purchase;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class StorePurchaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        // dd(request());
        return [
            'studio_id'               => 'required|exists:studios,id',
            'company_id'              => 'required|exists:companies,id',
            'machine_id'              => 'required|exists:machines,id',
            'configuration_id'        => 'nullable|integer',
            'machine_price'           => 'required|numeric',
            'monthly_installment'     => 'nullable|numeric',
            'machine_quantity'        => 'required|integer',
            'duration'                => 'nullable|integer',
            'condition'               => 'required|string',
            'status'                  => 'required|integer',
            'starting_date'           => 'required|date',
            'deposit_amount'          => 'nullable|numeric',
            'buy_out'                 => 'nullable|numeric',
            'deposit_date'            => 'nullable|date',
        ];
    }

    public function attributes()
    {
        return [
            'studio.name' => 'name',
            'studio.owner_first_name' => 'owner first name',
            'studio.owner_last_name' => 'owner last name',
            'studio.email' => 'email',
            'studio.phone' => 'phone',
            'studio.address' => 'address',
            'studio.city' => 'city',
            'studio.state_id' => 'state',
            'studio.zip' => 'zip',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()) {
            // dd($validator->errors());
            toastr()->addError('Please check all the fields');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
