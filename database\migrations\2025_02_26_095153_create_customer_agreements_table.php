<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('customer_agreements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained()->onDelete('cascade'); 
            $table->string('type');
            $table->string('file_name')->nullable();
            $table->string('file_path');
            $table->string('original_name');
            $table->enum('status', ['Active', 'Await delivery', 'Inactive', 'Pending', 'On hold']);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('customer_agreements');
    }

};
