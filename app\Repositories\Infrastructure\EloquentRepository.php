<?php

namespace App\Repositories\Infrastructure;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

abstract class EloquentRepository implements BaseRepository
{
    /** @var Model $model */
    protected Model $model;

    /**
     * EloquentRepository constructor.
     * @param Model $model
     */
    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    /**
     * {@inheritdoc}
     */
    public function findOneById(string $id)
    {
        return $this->findOneBy(['id' => $id]);
    }

    /**
     * {@inheritdoc}
     */
    public function findOneBy(array $criteria = [], array $relationships = [])
    {
        return $this->model
            ->where($criteria)
            ->with($relationships)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * {@inheritdoc}
     */
    public function findByFilters(
        string $orderParam = 'created_at',
        string $orderType = 'desc',
        array $criteria = [],
        array $relationships = []
    ) {
        return $this->model
            ->where($criteria)
            ->with($relationships)
            ->orderBy($orderParam, $orderType)
            ->get();
    }

    /**
     * {@inheritdoc}
     */
    public function findByFiltersPaginate(
        string $orderParam = 'created_at',
        string $orderType = 'desc',
        array $criteria = [],
        array $relationships = []
    ) {
        return $this->model
            ->where($criteria)
            ->with($relationships)
            ->orderBy($orderParam, $orderType)
            ->paginate();
    }

    /**
     * {@inheritdoc}
     */
    public function findIn(string $field, array $data)
    {
        return $this->model->whereIn($field, $data)->get();
    }

    /**
     * @inheritDoc
     */
    public function findByHasRelationship(
        string $relationship,
        array $data,
        array $whereCriteria = [],
        string $method = 'get'
    ) {
        return $this->model->whereHas(
            $relationship,
            function (Builder $query) use ($data) {
                return $query->where($data);
            }
        )->where($whereCriteria)->$method();
    }

    /**
     * @inheritDoc
     */
    public function findByHasOrWhereRelationship(
        string $relationship,
        array $data,
        array $orWhereData,
        array $whereCriteria = []
    ) {
        return $this->model->whereHas(
            $relationship,
            function (Builder $query) use ($data, $orWhereData) {
                $query->where($data);
                $query->orWhere($orWhereData);
                return $query;
            }
        )->where($whereCriteria)->get();
    }

    /**
     * @inheritDoc
     */
    public function find(array $criterias)
    {
        $query = $this->model;

        foreach ($criterias as $criteria => $data) {
            switch ($criteria) {
                case 'whereNotIn':
                case 'whereIn':
                    $query = $query->$criteria(...$data);
                    break;
                case 'where':
                    $query = $query->$criteria($data);
                    break;
            }
        }

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function findOrCreate(array $criteria, array $data = [])
    {
        return $this->model->firstOrCreate($criteria, $data);
    }

    /**
     * {@inheritdoc}
     */
    public function store(array $data)
    {
        return $this->model->create($data);
    }

    /**
     * {@inheritdoc}
     */
    public function insert(array $data)
    {
        return $this->model->insert($data);
    }

    /**
     * {@inheritdoc}
     */
    public function update(Model $model, array $data)
    {
        return tap($model)->update($data);
    }

    /**
     * {@inheritdoc}
     */
    public function updateWhere(array $criteria, array $data, bool $trash = false)
    {
        $query = $this->model->where($criteria);

        if ($trash) {
            $query = $query->withTrashed();
        }

        return $query->update($data);
    }

    /**
     * {@inheritdoc}
     */
    public function updateWhereIn(string $field, array $criteria, array $data, array $whereCriteria = [])
    {
        return $this->model->whereIn($field, $criteria)->where($whereCriteria)->update($data);
    }

    /**
     * @inheritDoc
     */
    public function updateWhereHas(
        string $relationship,
        array $whereHasCriteria,
        array $data,
        ?array $whereCriteria = [],
        bool $trash = false
    ): void {
        $query = $this->model->whereHas(
            $relationship,
            function ($q) use ($whereHasCriteria, $trash) {
                if ($trash) {
                    $q->where($whereHasCriteria)->withTrashed();
                }
                if (!$trash) {
                    $q->where($whereHasCriteria);
                }
            }
        )->where($whereCriteria);

        if ($trash) {
            $query = $query->withTrashed();
        }

        $query->update($data);
    }

    /**
     * @inheritDoc
     */
    public function updateWhereNotIn(
        string $relationship,
        array $whereHasCriteria,
        array $data,
        string $columnName,
        ?array $whereCriteria = [],
        bool $trash = false
    ): void {
        $query = $this->model->whereHas(
            $relationship,
            function ($q) use ($whereHasCriteria, $trash) {
                if ($trash) {
                    $q->where($whereHasCriteria)->withTrashed();
                }
                if (!$trash) {
                    $q->where($whereHasCriteria);
                }
            }
        )->whereNotIn($columnName, $whereCriteria);

        if ($trash) {
            $query = $query->withTrashed();
        }
        $query->update($data);
    }

    /**
     * {@inheritDoc}
     */
    public function updateOrCreate(array $attributes, array $data = [])
    {
        return $this->model->updateOrCreate($attributes, $data);
    }

    /**
     * {@inheritDoc}
     */
    public function attach(Model $model, string $relationship, array $data)
    {
        return $model->$relationship()->attach($data);
    }

    /**
     * {@inheritDoc}
     */
    public function detach(Model $model, string $relationship, array $data)
    {
        return $model->$relationship()->detach($data);
    }

    /**
     * {@inheritDoc}
     */
    public function sync(Model $model, string $relationship, array $data)
    {
        return $model->$relationship()->sync($data);
    }

    /**
     * {@inheritDoc}
     */
    public function syncWhere(Model $model, string $relationship, array $data, string $pivotField, string $pivotData)
    {
        return $model->$relationship()->wherePivot($pivotField, $pivotData)->sync($data);
    }

    /**
     * {@inheritDoc}
     */
    public function syncWithoutDetaching(Model $model, string $relationship, array $data)
    {
        return $model->$relationship()->syncWithoutDetaching($data);
    }

    /**
     * {@inheritDoc}
     */
    public function associate(Model $model, string $relationship, Model $associate)
    {
        $model->$relationship()->associate($associate);

        return tap($model)->save();
    }

    /**
     * {@inheritDoc}
     */
    public function dissociate(Model $model, string $relationship, Model $associate)
    {
        $model->$relationship()->dissociate($associate);

        return tap($model)->save();
    }

    /**
     * {@inheritdoc}
     */
    public function delete(Model $model)
    {
        return tap($model)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function deleteAll(array $ids, string $field = 'id')
    {
        return $this->model->whereIn($field, $ids)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function deleteWhere(string $field, $value)
    {
        return $this->model->where($field, $value)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function deleteWhereCriteria(array $criteria)
    {
        return $this->model->where($criteria)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function deleteWhereHas(string $relationship, array $whereHasCriteria, ?array $whereCriteria = [])
    {
        return $this->model->whereHas(
            $relationship,
            function ($q) use ($whereHasCriteria) {
                $q->where($whereHasCriteria);
            }
        )->where($whereCriteria)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function deleteOneById(int $id)
    {
        return $this->model->where('id', $id)->delete();
    }

    /**
     * {@inheritDoc}
     */
    public function getAll(array $relationships = [], bool $trashed = false)
    {
        $query = $this->model
            ->with($relationships)
            ->orderBy('created_at', 'desc')
            ->get();
        if ($trashed) {
            $query = $this->model
                ->with($relationships)
                ->orderBy('created_at', 'desc')
                ->withTrashed()
                ->get();
        }

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function forceDelete(Model $model)
    {
        return tap($model)->forceDelete();
    }

    /**
     * {@inheritdoc}
     */
    public function forceDeleteMany(array $ids)
    {
        return $this->model->whereIn('id', $ids)->forceDelete();
    }

    /**
     * {@inheritdoc}
     */
    public function getResultByLikeFilter(string $column, string $value)
    {
        return $this->model
            ->where($column, 'LIKE', '%' . $value . '%')
            ->get();
    }

    /**
     * {@inheritDoc}
     */
    public function getNumberOfdData(int $count, array $relationships = [], bool $trashed = false)
    {
        $query = $this->model
            ->with($relationships)
            ->orderBy('created_at', 'desc')
            ->take($count)
            ->get();
        if ($trashed) {
            $query = $this->model
                ->with($relationships)
                ->orderBy('created_at', 'desc')
                ->withTrashed()
                ->take($count)
                ->get();
        }

        return $query;
    }

    /**
     * {@inheritdoc}
     */
    public function reorderSortable(array $data): void
    {
        $items = collect($data)->keyBy('old');

        $records = $this->model->whereIn($this->model->sortable, $items->keys())->get();

        foreach ($records as $record) {
            $newItem = $items[$record->{$this->model->sortable}]['new'];
            $record->{$this->model->sortable} = $newItem;
            $record->save();
        }
    }

    /**
     * {@inheritdoc}
     */
    public function storeWithSortable(array $data, bool $asc = true)
    {
        $sortable = 1;

        if ($asc) {
            $this->model->increment($this->model->sortable);
        } else {
            $lastRecord = $this->findByFilters($this->model->sortable)->first();
            $sortable = $lastRecord ? $lastRecord->{$this->model->sortable} + 1 : $sortable;
        }

        $data[$this->model->sortable] = $sortable;

        return $this->store($data);
    }
}
