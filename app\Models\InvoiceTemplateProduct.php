<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceTemplateProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_product_id',
        'product_id',
        'price',
        'quantity',
        'deposit',
        'discount',
        'discount_type',
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(InvoiceTemplate::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }
}
