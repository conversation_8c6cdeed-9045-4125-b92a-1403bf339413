<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AdminSettings;

class SetTimezone
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // if (Auth::check() && Auth::user()->hasRole('super-admin')) {
            if (Auth::check() && Auth::user()->hasAnyRole(explode('|', \App\Helpers\Constants::ROLES))) {
            // $settings = \auth()->user()->adminSettings;
            // $timezone = $settings->timezone;
            //  Asking - Customer own timezone settings.
            $timezone = AdminSettings::first()->timezone;
            date_default_timezone_set($timezone);
            config(['app.timezone' => $timezone]);
        }

        return $next($request);
    }
}
