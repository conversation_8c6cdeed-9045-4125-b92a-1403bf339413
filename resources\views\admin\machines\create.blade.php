@extends('layouts.app')

@section('content')
        <div class="page-title">
            <div class="title-left">
                <h3>{{ __('new machine') }}</h3>
                <a href="{{ route('admin.machines.index') }}" class="back-link">← Back</a>
            </div>
            <!--<a href="{{ route('admin.machines.index') }}">
                <button type="button" class="btn btn-ghost-light-bigger back-btn">{{ __('Back') }}</button>
            </a>-->
        </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.machines.store') }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('machine info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'MACHINE NAME *',
                'field_type' => 'text',
                'field_value' => old('name', NULL),
                ])

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'UNIT PRICE *',
               'field_type' => 'text',
               'currency' => true,
               'field_value' => old('price', NULL),
               'input_field_class' => 'decimal-field',
            ])

            <div class="d-flex flex-column align-items-end pt-4">
                <div class="form-check d-flex align-items-center w-100 ps-0 mb-0">
                    <input id="parent_show" class="form-check-input ms-0 me-3" value="1" type="checkbox" onclick="$(this).is(':checked') ? $('.parent_machines_list').show() : $('.parent_machines_list').hide();$(this).is(':checked') ? $('#parent_show-hidden').val(1) : $('#parent_show-hidden').val(0)" @checked(old('parent_machine_id'))>
                    <label for="parent_show" class="form-check-label ms-0 fs-14px ls-0.7px">Is this machine a configuration?</label>
                    <input type="hidden" name="parent_show" value="{{ old('parent_show', 0) }}" id="parent_show-hidden">
                </div>
            </div>

            <div class="parent_machines_list pt-4" @if(old('parent_show') == 0)) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'parent_machine_id',
                    'field_label' => 'MACHINE *',
                    'values' => $machines,
                    'field' => 'machine',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('parent_machine_id', NULL),
                    'include_empty' => true,
                ])      

                @include('partials.forms.textarea', [
                    'field_name' => 'description',
                    'field_label' => 'DESCRIPTION',
                    'field_type' => 'text',
                    'placeholder' => 'Enter (optional)',
                    'field_value' => old('description', NULL),
                ])
            </div>

            <h5 class="form-section-title">{{ __('lease info (agreement)') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'monthly_installment',
                'field_label' => 'MONTHLY INSTALLMENT (LEASE) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('monthly_installment', NULL),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'lease_deposit',
                'field_label' => 'DEPOSIT (PER UNIT) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('lease_deposit', NULL),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'lease_duration',
                'field_label' => 'DEFAULT LEASE DURATION *',
                'field_type' => 'text',
                'placeholder' => '0',
                'field_right_mark' => 'months',
                'field_value' => old('lease_duration', NULL),
                'input_field_class' => 'decimal-field',
            ])

            <h5 class="form-section-title">{{ __('purchase info (agreement)') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'purchase_deposit',
                'field_label' => 'DEPOSIT (PER UNIT) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('purchase_deposit'),
                'input_field_class' => 'decimal-field',
            ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.companies.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
{{-- 
<script type="module">
document.addEventListener('DOMContentLoaded', function() {

    const inputElements = document.querySelectorAll('.currency-input input');
    inputElements.forEach(inputElement => {
        if (inputElement) {
            Inputmask({
                alias: "numeric",
                groupSeparator: ",",
                autoGroup: true,
                digits: 2,
                digitsOptional: false,
                prefix: "$",
                placeholder: "0",
                showMaskOnHover: false
            }).mask(inputElement);
        }
    });
});
</script> --}}