<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Company\StoreCompanyRequest;
use App\Http\Requests\Admin\Company\UpdateCompanyRequest;
use App\Models\Company;
use App\Models\State;
use App\Services\Admin\Company\ICompanyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class CompanyController extends Controller
{
    private ICompanyService $companyService;

    public function __construct(
        ICompanyService $companyService,
    )
    {
        $this->companyService = $companyService;
    }

    public function index(): View
    {
        return view('admin.companies.index');
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $companies = $this->companyService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($companies as $company) {
            $company['sequential_id'] = $sequential_id++;
            $company['orderParam'] = $orderParam;
            $company['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.companies-search', compact('companies'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $states = State::all();
        return view('admin.companies.create', compact('states'));
    }

    public function store(StoreCompanyRequest $request)
    {
        try {
            $this->companyService->store($request->validated());
            toastr()->addSuccess('', 'Company created successfully.');
            return redirect()->route('admin.companies.index');
        } catch (\Exception $e) {
            toastr()->addError('Company creation failed');
            return redirect()->back();
        }
    }

    public function edit(Company $company): View
    {
        $states = State::all();
        return view('admin.companies.edit', compact('company', 'states'));
    }

    public function update(Company $company, UpdateCompanyRequest $request)
    {
        try {
            $this->companyService->update($request->validated(), $company);
            toastr()->addSuccess('', 'Company updated successfully.');
            return redirect()->route('admin.companies.edit', $company);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Company update failed');
            return redirect()->back();
        }
    }

    public function destroy(Company $company)
    {
        try {
            $this->companyService->delete($company);
            toastr()->addSuccess('', 'Company deleted successfully.');
            return redirect(route('admin.companies.index'));
        } catch (\Exception $e) {
            toastr()->addError('Company delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $companyIds = $request->input('selectedItems');
            foreach ($companyIds as $companyId) {
                $company = Company::find($companyId);
                $this->companyService->delete($company);
            }
            toastr()->addSuccess('', 'Selected companies deleted successfully.');

            return redirect(route('admin.companies.index'));
        } catch (\Exception $e) {
            toastr()->addError('Company delete failed');
            return redirect()->back();
        }
    }
}
