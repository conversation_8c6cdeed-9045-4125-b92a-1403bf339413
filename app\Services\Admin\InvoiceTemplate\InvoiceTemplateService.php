<?php

namespace App\Services\Admin\InvoiceTemplate;

use App\Models\Customer;
use App\Models\InvoiceTemplate;
use App\Models\InvoiceTemplateProduct;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceTemplateService implements IInvoiceTemplateService
{
    public function store(array $data): void
    {
        // echo '<pre>';
        // print_r($data->toArray());
        // die();        

        $lastInvoiceTemplate = InvoiceTemplate::withTrashed()->orderBy('id', 'desc')->first();
        $invoiceTemplate = InvoiceTemplate::create(
            array_merge($data, ['number' => $lastInvoiceTemplate ? $lastInvoiceTemplate->id + 1 : 1])
        );
        $invoiceTemplate->items()->createMany($data['items']);
    }

    public function update(array $data, InvoiceTemplate $invoiceTemplate): void
    {
        $invoiceTemplate->update($data);
        $ids = [];
        foreach ($data['items'] as $item) {
            if (isset($item['id'])) {
                $ids[] = $item['id'];
                unset($item['product_name']);
                $invoiceTemplate->items()->where('id', $item['id'])->update($item);
            } else {
                unset($item['product_name']);
                $newItem = $invoiceTemplate->items()->create($item);
                $ids[] = $newItem->id;
            }
        }
        InvoiceTemplateProduct::where('invoice_template_id', $invoiceTemplate->id)->whereNotIn('id', $ids)->delete();
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $customerId): LengthAwarePaginator
    {
        $query = InvoiceTemplate::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();
        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('title', 'LIKE', '%' . ltrim($searchData, '0') . '%');
                //   ->orWhere('title', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                //   ->orWhere('note', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });
        return $query->paginate($perPage);
    }

    public function delete(InvoiceTemplate $invoiceTemplate): void
    {
        $invoiceTemplate->delete();
    }
}
