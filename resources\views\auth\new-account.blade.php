@extends('layouts.login')

@section('content')
    <div class="auth-card login-wrap">

        <div class="login-above">
            <div class="mb-7 align-items-center d-flex justify-content-between">
                <h1 class="fs-4 mb-0 text-uppercase fw-semibold align-items-center d-flex">
                    {{ __('Create an account') }}
                </h1>
                <a href="/" style="display: flex; align-items: center; text-decoration: none; color: #969696;">
                    ← &nbsp;<span>Go Back</span>
                </a>
            </div>
            <form method="POST" class="my-auto">
                @csrf
                <div class="input-placeholder">
                    <div class="form-group discount_type-field">
                        <select name="account-type" class="form-control dynamic-select" id="account-type">
                            <option value="" selected></option>
                            <option value="studio">Studio</option>
                            <option value="customer">B2C Customer</option>
                            <option value="trainer">Trainer</option>
                            <option value="master-trainer">Master Trainer</option>
                        </select>
                    </div>
                    @error('email')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                </div>
            </form>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const accountType = document.getElementById('account-type');
                $('#account-type').select2({
                    placeholder: "Select Option",
                    minimumResultsForSearch: Infinity
                });
                $('#account-type').on('change', function() {
                    let selectedValue = $(this).val();
                    if (selectedValue) {
                        window.location.href = "/signup?type=" + selectedValue;
                    }
                });

            });
        </script>
    @endsection
