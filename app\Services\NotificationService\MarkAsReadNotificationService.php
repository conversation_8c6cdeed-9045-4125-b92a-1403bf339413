<?php

namespace App\Services\NotificationService;

use Illuminate\Support\Facades\DB;

class MarkAsReadNotificationService
{
    /**
     * @param $notification
     * @return void
     */
    public function markAsRead($notification): void
    {
        DB::table('notifications')
            ->where('id', $notification)
            ->update(['read_at' => now()]);
    }

    /**
     * @param $userId
     * @return void
     */
    public function markAsReadAll($userId): void
    {
        DB::table('notifications')
            ->where('notifiable_id', $userId)
            ->update(['read_at' => now()]);
    }
}
