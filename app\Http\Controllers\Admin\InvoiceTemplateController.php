<?php

namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use App\Enum\InvoiceProduct\Status;
use App\Helpers\InvoiceHelper;
use App\Http\Requests\Admin\InvoiceTemplate\StoreInvoiceTemplateRequest;
use App\Http\Requests\Admin\InvoiceTemplate\UpdateInvoiceTemplateRequest;
use App\Models\Bundle;
use App\Models\Company;
use App\Models\Customer;
use App\Models\InvoiceTemplate;
use App\Models\InvoiceTemplateProduct;
use App\Models\Products;
use App\Services\Admin\InvoiceTemplate\IInvoiceTemplateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

class InvoiceTemplateController extends Controller {
    private IInvoiceTemplateService $invoiceTemplateService;

    public function __construct(IInvoiceTemplateService $invoiceTemplateService)
    {
        $this->invoiceTemplateService = $invoiceTemplateService;
    }

    public function index(){
        return view('admin.invoice-templates.index');
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $invoices = $this->invoiceTemplateService->search($searchData, $orderParam, $orderType, $perPage, $status, '');

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = ($page - 1) * $perPage + 1;
        }

        foreach ($invoices as $invoiceProduct) {
            $invoiceProduct['sequential_id'] = $sequential_id++;
            $invoiceProduct['orderParam'] = $orderParam;
            $invoiceProduct['orderType'] = $orderType;
        }
        $viewContent = view('partials.forms.invoice-templates.invoice-templates-search', compact('invoices'))->render();

        return response()->json($viewContent);
    }

    public function get_templates(Request $request) : JsonResponse{
        $template_items['items'] = [];
        if(isset($request->id) && $request->id != "0"){
            $main_info = InvoiceTemplate::where('id', $request->id)->first();
            $products = InvoiceTemplateProduct::where('invoice_template_id', $request->id)
                ->select('product_id', 'price', 'quantity', 'discount_type', 'discount', 'deposit')
                ->get();
            foreach($products as $product){
                $template_items['items'][] = [
                    'id' => $product->product_id, 
                    'price' => $product->price, 
                    'quantity' => $product->quantity,
                    'discount_type' => $product->discount_type,
                    'discount' => $product->discount,
                    'deposit' => $product->deposit
                ];
            }
            $template_items['template'] = $main_info;
        }
        return response()->json($template_items);
    }

    public function create(): View
    {
        $companies = Company::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $customers = Customer::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);
        $bundles = Bundle::all();
        $new_products = [];

        foreach ($products as $product) {
            if ($product->category == 'fee') {
                $new_products['fee'][] = $product;
            } elseif ($product->category == 'product') {
                $new_products['products'][] = $product;
            }
        }

        foreach ($bundles as $key => $value) {
            $new_products['bundle'][] = $value;
        }
        return view('admin.invoice-templates.create', compact('companies', 'customers', 'new_products', 'products'));
    }

    public function store(StoreInvoiceTemplateRequest $request): RedirectResponse
    {
        // dd($request->all()); // Dumps and stops execution
        try {
            $this->invoiceTemplateService->store($request->validated());
            toastr()->addSuccess('', 'Invoice Template created successfully.');
            return redirect()->route('admin.invoice-templates.index');
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Invoice template creation failed');
            return redirect()->back();
        }
    }

    public function edit(InvoiceTemplate $invoiceTemplate): View
    {
        $companies = Company::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $customers = Customer::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);
        $bundles = Bundle::all();

        $new_products = [];

        foreach ($products as $product) {
            if ($product->category == 'fee') {
                $new_products['fee'][] = $product;
            } elseif ($product->category == 'product') {
                $new_products['products'][] = $product;
            }
        }

        foreach ($bundles as $key => $value) {
            $value['name'] = $value['title'];
            $new_products['bundle'][] = $value;
        }

        $invoice = $invoiceTemplate->load('items.product', 'customer');
        return view('admin.invoice-templates.edit', compact('companies', 'customers', 'new_products', 'products', 'invoice'));
    }

    public function update(InvoiceTemplate $invoiceTemplate, UpdateInvoiceTemplateRequest $request): RedirectResponse
    {
        try {
            $this->invoiceTemplateService->update($request->validated(), $invoiceTemplate);
            toastr()->addSuccess('', 'Invoice Template updated successfully.');
            return redirect()->route('admin.invoice-templates.edit', $invoiceTemplate);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Invoice Template update failed');
            return redirect()->back();
        }
    }
    public function destroy(InvoiceTemplate $invoiceTemplate): RedirectResponse
    {
        try {
            $this->invoiceTemplateService->delete($invoiceTemplate);
            toastr()->addSuccess('', 'Invoice Template deleted successfully.');
            return redirect(route('admin.invoice-templates.index'));
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Invoice template delete failed');
            return redirect()->back();
        }
    }
    public function deleteMultiple(Request $request): RedirectResponse
    {
        try {
            $invoiceTemplateIds = $request->input('selectedItems');
            foreach ($invoiceTemplateIds as $invoiceTemplateId) {
                $invoiceTemplate = InvoiceTemplate::find($invoiceTemplateId);
                $this->invoiceTemplateService->delete($invoiceTemplate);
            }
            toastr()->addSuccess('', 'Selected invoice templates deleted successfully.');
            return redirect(route('admin.invoice-templates.index'));
        } catch (\Exception $e) {
            toastr()->addError('Invoice templates delete failed');
            return redirect()->back();
        }
    }

}