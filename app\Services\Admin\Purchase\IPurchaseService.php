<?php

namespace App\Services\Admin\Purchase;

use App\Models\Customer;
use App\Models\Machine;
use App\Models\Purchase;
use Illuminate\Pagination\LengthAwarePaginator;

interface IPurchaseService
{
    public function store(array $data, Customer $customer);
    public function update(array $data, Purchase $purchase, Customer $customer);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator;
    public function delete(Purchase $purchase);
}
