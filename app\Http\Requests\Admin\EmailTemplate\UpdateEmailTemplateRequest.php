<?php

namespace App\Http\Requests\Admin\EmailTemplate;

use App\Rules\ValidKeywords;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class UpdateEmailTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $template = $this->route('template');
        return [
            'name' => 'required|string',
            'body' => ['required', new ValidKeywords($template)],
        ];
    }

    protected function failedValidation(Validator $validator): void
    {
        $errors = $validator->errors()->all();

        $errorString = implode('<br>', $errors);

        toastr()->addError('', $errorString);

        throw ValidationException::withMessages([])->redirectTo($this->getRedirectUrl());
    }
}
