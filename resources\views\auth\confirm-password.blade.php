@extends('layouts.login')

@section('content')
    <div class="d-flex align-items-center justify-content-center min-vh-100 py-3">
        <div class="auth-card px-3 px-sm-6 px-md-10 py-5 py-md-9">
            <a class="auth-close" href="{{ url('/') }}">
                <button type="button" class="btn-close" aria-label="Close"></button>
            </a>

            <h1 class="mb-5 mb-md-9 fs-4 fw-semibold text-uppercase">{{ __('Confirm Password') }}</h1>
            <p>Please confirm your password before continuing.</p>

            <form method="POST" action="{{ route('password.confirm') }}">
                @csrf

                <input id="password" type="password"
                       class="@error('password') is-invalid @enderror"
                       name="password" required autocomplete="current-password" placeholder="Name">
                <i class="fa fa-eye bi-eye-slash" onclick="togglePasswordEye()"></i>
                @error('password')
                <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                @enderror

                <button type="submit" class="btn btn-primary">
                    {{ __('SEND') }}
                </button>

                @if (Route::has('password.request'))
                    <a class="btn btn-link text-decoration-none card-link text-end padding-right-15"
                       href="{{ route('password.request') }}">
                        {{ __('Forgot Your Password?') }}
                    </a>
                @endif

            </form>
        </div>
    </div>
@endsection
