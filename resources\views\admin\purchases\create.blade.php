@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('new purchase') }}</h3>
            {{-- <a href="{{ route('admin.customers.dashboard', $customer) }}" class="back-link">← Back</a> --}}
            <a href="{{ route('admin.customers.dashboard', $customer) }}?tab=purchases" class="back-link">← Back</a>
        </div>
    </div>
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.purchases.store', $customer) }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('Lagree Company') }}</h5>

            @include('partials.forms.select', [
              'field_name' => 'company_id',
              'field_label' => null,
              'values' => $companies,
              'field' => 'state',
              'option_key' => 'id',
              'option_label' => 'name',
              'field_value' => old('company_id'),
              'field_label_class' => 'd-none',
              'include_empty' => true,
            ])

            <h5 class="form-section-title">{{ __('Select Location') }}</h5>

            @include('partials.forms.select', [
               'field_name' => 'studio_id',
               'field_label' => null,
               'values' => $studios,
               'field' => 'state',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('studio_id', NULL),
               'field_label_class' => 'd-none',
               'include_empty' => true,
               'field_id' => 'purchase_id'
           ])

            <h5 class="form-section-title">{{ __('PURCHASE info') }}</h5>

            @include('partials.forms.input',  [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Select',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', NULL),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field'
            ])

            @include('partials.forms.select', [
                'field_name' => 'machine_id',
                'field_label' => 'MACHINE *',
                'values' => $machines,
                'field' => 'machine',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('machine_id', NULL),
                'include_empty' => true,
            ])
            
            <div class="parent_machines_field" @if(old('configuration_id') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'configuration_id',
                    'field_label' => 'CONFIGURATION *',
                    'values' => NULL,
                    'field' => 'CONFIGURATION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('configuration', NULL),
                    'include_empty' => true,
                ])
            </div>

            <div class="condition_field" @if(old('condition') == 0) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'condition',
                    'field_label' => 'CONDITION *',
                    'values' => ['new' => 'New', 'used' => 'Used', 'restored' => 'Restored'],
                    'field' => 'CONDITION',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('condition', NULL),
                    'include_empty' => true,
                ])
            </div>

            @include('partials.forms.input', [
                'field_name' => 'machine_price',
                'field_label' => 'UNIT PRICE *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'field_value' => old('machine_price', NULL),
                'input_field_class' => 'decimal-field'
            ])

            {{-- @include('partials.forms.input', [
                'field_name' => 'monthly_installment',
                'field_label' => 'MONTHLY INSTALLMENT *',
                'field_type' => 'text',
                'placeholder' => '0',
                'currency' => true,
                'field_value' => old('monthly_installment', NULL),
                'input_field_class' => 'decimal-field'
            ]) --}}

            @include('partials.forms.input', [
                'field_name' => 'machine_quantity',
                'field_label' => '# OF MACHINES *',
                'placeholder' => '0',
                'field_type' => 'text',
                'field_right_mark' => 'qty',
                'field_value' => old('machine_quantity', NULL),
                'field_class' => ' mb-7',
                'input_field_class' => 'integer-field'
            ])

            {{-- @include('partials.forms.input', [
                'field_name' => 'duration',
                'field_label' => 'DURATION *',
                'field_type' => 'text',
                'placeholder' => '0',
                'field_right_mark' => 'months',
                'field_value' => old('duration', NULL),
                'input_field_class' => 'integer-field'
            ]) --}}

            @include('partials.forms.checkbox', [
                'field_name' => 'toggle',
                'field_to_toggle' => 'deposit_container',
                'field_label' => 'Deposit (optional)',
                'wrap_class_name' => ' mb-7 toggle_bottom',
            ])

            @include('partials.forms.input', [
               'field_name' => 'deposit_amount',
               'field_label' => 'DEPOSIT (PER UNIT) *',
               'field_type' => 'text',
               'placeholder' => '0',
               'currency' => true,
               'field_value' => old('deposit_amount', NULL),
               'input_field_class' => 'decimal-field',
               'field_class' => 'd-none deposit_container',
               ])

            {{-- @include('partials.forms.input', [
               'field_name' => 'buy_out',
               'field_label' => 'BUY-OUT AMOUNT *',
               'field_type' => 'text',
               'placeholder' => '0',
               'currency' => true,
               'field_value' => old('buy_out', NULL),
               'input_field_class' => 'decimal-field'
            ]) --}}

            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', NULL),
            ])

            @include('partials.forms.input',  [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d')
            ])

            @include('partials.forms.input',  [
                'field_name' => 'status',
                'field_type' => 'hidden',
                'field_value' => 0
            ])

            <div class="w100 border-top mt-7">
                <p class="lh-1 mt-7 ">Machine(s) total: <span class="monthly_installment_val">$0</span></p>
                <p class="lh-1 deposit-wrap" style="display: none;">Deposit: <span class="deposit_val">$0</span></p>
            </div>

            <div class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.purchases.create', $customer)}}" class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>

        </form>
    </div>

@endsection
@push('scripts')
<script>
    var selected_machine = [];
    var selected_machine_conf = [];
    document.addEventListener('DOMContentLoaded', function () {
        @if (old("configuration_id"))
        setTimeout(function(){
            $('#machine_id-machine').trigger('change');        
        }, 500);
        @endif
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }

        function calculate_installments_deposit(){
            console.log('ff calculate_installments_deposit');
            
            var deposit = parseInt($('#deposit_amount').val());
            var machine_price = parseInt($('#machine_price').val());
            var qty = $('#machine_quantity').val() != '' ? $('#machine_quantity').val() : 1;
            
            console.log('deposit', deposit);
            console.log('machine_price', machine_price);
            console.log('qty', qty);
                        
            if($('[placeholder="Deposit (optional)"]').is(':checked')) {
                $('.deposit_container').show();
            }
            $('.deposit_val').html('$' + (deposit * qty) + ' ($' + deposit + ' x ' + qty + ')').addClass('text-success');
            $('.monthly_installment_val').html('$' + (machine_price * qty) + ' ($' + machine_price + ' x ' + qty + ')').addClass('text-success');
        }

        $('#machine_quantity').on('change', function() { calculate_installments_deposit(); });
        $('#deposit_amount').on('change', function() { calculate_installments_deposit(); });
        $('#monthly_installment').on('change', function() { calculate_installments_deposit(); });

        $('#configuration_id-CONFIGURATION').change(function() {
        $('[placeholder="Deposit (optional)"]').change(function() {
            if($(this).is(':checked')) {
                $('.deposit-wrap').show();
            }else{
                $('.deposit-wrap').hide();
            }
        });
            var machine_id = $(this).val();
            let url = '{{ route('admin.machines.get-machine') }}';

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                }
            });
            $.ajax({
                url: url,
                method: 'POST',
                dataType: 'json',
                data: {
                    machine_id
                },
                success: function(data) {
                    console.log('conf:', data);                        
                    if (data.success) {
                        selected_machine_conf = data.machine;
                    }
                },
                error: function() {
                    // flasherJS.error('', 'Error occurred while loading data.');
                }
            });
        });
        $('#condition-CONDITION').change(function() {
            var val = $(this).val();

            console.log('val: ', val);
            
            if(val == 'Used' || val == 'Restored'){
                $('#machine_price').val('');
                $('#duration').val('');
                $('#machine_quantity').val('');
                $('#monthly_installment').val('');
                $('#deposit_amount').val('');
                $('.deposit_val').html('$0').removeClass('text-success');
                $('.monthly_installment_val').html('$0').removeClass('text-success');
            }else if(val == 'New' && selected_machine_conf){
                $('#machine_price').val(selected_machine_conf.price);
                $('#machine_quantity').val(1);
                // $('#duration').val(selected_machine_conf.lease_duration);
                // $('#monthly_installment').val(selected_machine_conf.monthly_installment);
                $('#deposit_amount').val(selected_machine_conf.lease_deposit);
                setTimeout(function(){
                    calculate_installments_deposit();
                }, 300);
            }
        });
        $('#machine_id-machine').change(function() {
            let machine_id = $(this).val();
            let for_conf = ["8","3","4"];

            console.log(machine_id);
            
            if (machine_id) {
                $('.condition_field').show();
                
                let url = '{{ route('admin.machines.get-machine') }}';

                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}"
                    }
                });
                $.ajax({
                    url: url,
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        machine_id
                    },
                    success: function(data) {
                        console.log(data);                        
                        if (data.success) {
                            selected_machine = data.machine;
                            if($.inArray(machine_id, for_conf) !== -1){
                                $('.parent_machines_field').show();
                                if (data.conf_machines.length) {
                                    $('#configuration_id-CONFIGURATION').html('');                                        
                                    $('#configuration_id-CONFIGURATION').append('<option></option>');
                                    $.each(data.conf_machines, function(index, single_machine){
                                        var sel = single_machine.id == '{{ old("configuration_id") }}' ? "SELECTED" : "";
                                        $('#configuration_id-CONFIGURATION').append('<option value="' + single_machine.id + '" ' + sel + '>' + single_machine.name + '</option>');
                                    });
                                    // $("select").select2("destroy").select2();
                                    $('#configuration_id-CONFIGURATION').select2("destroy").select2({
                                        minimumResultsForSearch: 10,
                                        placeholder: "Select",
                                        closeOnSelect: true
                                    });
                                }
                            }else{
                                $('.parent_machines_field').hide();
                            }
                        }
                    },
                    error: function() {
                        flasherJS.error('', 'Error occurred while loading data.');
                    }
                });
            }
        });
    });
</script>
@endpush
