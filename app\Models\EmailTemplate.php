<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'body',
        'keywords',
        'type',
        'unique_name',
    ];

    protected $casts = [
        'keywords' => 'array'
    ];
}
