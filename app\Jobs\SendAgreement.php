<?php

namespace App\Jobs;

use App\Models\Company;
use App\Models\Customer;
use App\Services\Admin\AdobeSign\AdobeSignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendAgreement implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private string $type;
    private array $templateNames;
    private Customer $customer;
    private Model $model;
    private Company $company;

    /**
     * Create a new job instance.
     */
    public function __construct(string $type, array $templateNames, Customer $customer, Model $model, Company $company)
    {
        $this->type = $type;
        $this->templateNames = $templateNames;
        $this->customer = $customer;
        $this->model = $model;
        $this->company = $company;
    }

    /**
     * Execute the job.
     * @throws \Exception
     */
    public function handle(): void
    {
        $baseUri = AdobeSignService::getBaseUri();
        // $template = AdobeSignService::getTemplate($this->templateNames, $baseUri);
        AdobeSignService::createAgreement($this->customer, $this->model, $this->type, $this->company, $baseUri, $this->templateNames);
    }
}
