<?php

namespace App\Http\Requests\Admin\InvoiceTemplate;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UpdateInvoiceTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            // 'company_id' => 'required|exists:companies,id',
            // 'customer_id' => 'required|exists:customers,id',
            'title' => 'required|string',
            // 'send_date' => 'required|date',
//            'sent_date' => 'required|date|after_or_equal:send_date',
            // 'show_tax' => 'required|numeric|min:0',
            // 'paid' => 'numeric',
            'note' => 'nullable|string',
            'items' => 'required|array',
            'items.*' => 'required_with:invoice_template_product|array',
            'items.*.id' => 'nullable|integer|exists:invoice_template_products,id',
            'items.*.product_id' => 'required_with:invoice_template_product.*|integer|exists:products,id',
            'items.*.price' => 'required_with:invoice_template_product.*|numeric|min:0',
            'items.*.quantity' => 'required_with:invoice_template_product.*|integer|min:1',
            'items.*.deposit' => 'required_with:invoice_template_product.*|numeric|min:0',
            'items.*.discount' => 'required_with:invoice_template_product.*|numeric|min:0',
            'items.*.discount_type' => 'required|string',
            'items.*.product_name' => 'string',
        ];
    }

    // This method runs before validation to clean the request data
    protected function prepareForValidation()
    {
        // dd($this); // Dumps and stops execution

        // Clean fields that should not contain the "$" symbol
        // if ($this->has('shipping_fee')) {
        //     $this->merge([
        //         'shipping_fee' => $this->cleanCurrency($this->input('shipping_fee'))
        //     ]);
        // }

        // if ($this->has('handling_fee')) {
        //     $this->merge([
        //         'handling_fee' => $this->cleanCurrency($this->input('handling_fee'))
        //     ]);
        // }

        // Clean all items' price and discount fields
        if ($this->has('items')) {
            $items = $this->input('items');
            foreach ($items as $index => $item) {
                if (isset($item['price'])) {
                    $items[$index]['price'] = $this->cleanCurrency($item['price']);
                }
                if (isset($item['discount'])) {
                    $items[$index]['discount'] = $this->cleanCurrency($item['discount']);
                }
                if (isset($item['deposit'])) {
                    $items[$index]['deposit'] = $this->cleanCurrency($item['deposit']);
                }
                if (isset($item['custom_product']) AND $item['custom_product'] == 1) {
                    if (isset($item['product_id']) AND $item['product_id'] > 0) {
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $items[$index]['price'] = $item['price'] != NULL ? $item['price'] : '0.00';

                        $edit_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'] != NULL ? $item['price'] : '0.00',
                            'updated_at' => Carbon::now(),
                        ];

                        DB::table('products')->where('id', $item['product_id'])->update($edit_custom_product);
                        // dd($items); // Dumps and stops execution
                    }else{
                        $item['price'] = $this->cleanCurrency($item['price']);
                        $new_custom_product = [
                            'name' => $item['product_name'],
                            'price' => $item['price'] != NULL ? $item['price'] : '0.00',
                            'category' => 'product',
                            'custom_product' => 1,
                            'created_at' => Carbon::now(),
                            'updated_at' => Carbon::now(),
                        ];
                        // dd($new_custom_product); // Dumps and stops execution
                        $items[$index]['price'] = $item['price'] != NULL ? $item['price'] : '0.00';

                        DB::table('products')->insert($new_custom_product);
                        $items[$index]['product_id'] = DB::getPdo()->lastInsertId();
                        // dd($items); // Dumps and stops execution
                    }
                }
            }
            $this->merge([
                'items' => $items
            ]);
            // dd($items);
        }
    }

    // Helper function to clean currency values by removing non-numeric characters
    private function cleanCurrency($value)
    {
        if ($value === '') {
            return null;
        }
        // Remove everything except numbers, dots, and negative sign
        return preg_replace('/[^0-9.-]+/', '', $value);
    }

    protected function failedValidation(Validator $validator)
    {
        var_dump("Validation failed: " . implode(', ', $validator->errors()->all()));
        exit;
        if ($validator->errors()->has('items')) {
            toastr()->addError('The items field is required.');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
