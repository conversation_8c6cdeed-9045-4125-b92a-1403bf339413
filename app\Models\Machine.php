<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Machine extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'price',
        'parent_machine_id',
        'parent_show',
        'monthly_installment',
        'lease_deposit',
        'lease_duration',
        'purchase_deposit',
    ];
}
