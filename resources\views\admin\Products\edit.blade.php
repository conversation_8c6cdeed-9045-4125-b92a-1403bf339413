@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('edit Product') }}</h3>
            <a href="{{ route('admin.products.index') }}" class="back-link">← Back</a>
        </div>
        <!--<a href="{{ route('admin.products.index') }}">
            <button type="button" class="btn btn-ghost-light-bigger back-btn">{{ __('Back') }}</button>
        </a>-->
    </div>
    <div class="mb-6">
        <form method="POST" action="{{ route('admin.products.update', $product) }}" id="studio-location-form">
            @csrf
            @method('PUT')
            <div class="mb-6 mb-sm-6 items-base" style="display:flex; align-items:baseline;">
                <h5 class="form-section-title">{{ __('Product info') }}</h5>
                <span class="text-white text-xs font-semibold rounded-10 
                    {{ $product->stock > 0 ? 'bg-success' : 'bg-success' }}" style="border-radius: 20px; padding: 4.5px 13px; font-size:12px; font-weight:500; line-height:13.2px; margin-left:22px;">
                    {{ $product->stock }} in stock
                </span>
            </div>
            @include('partials.forms.input', [
               'field_name' => 'name',
               'field_label' => 'NAME *',
               'field_type' => 'text',
               'field_value' => $product->name,
            ])

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'PRICE *',
               'field_type' => 'text',
               'field_value' => $product->price,
               'placeholder' => '$'
            ])
            <div class="form-control position-relative m-0 p-0 border-none">
            @include('partials.forms.input', [
                'field_name' => 'supplier_price',
                'field_label' => 'SUPPLIER PRICE *',
                'field_type' => 'text',
                'field_value' => $product->supplier_price,
                'placeholder' => '$',
                'product_profit' => number_format($product->price - $product->supplier_price, 2)
            ])
            </div>
            <div class="mt-7 text-xs text-[#f59e0b]">
                <span style="color:red;">Warning: Retail or supplier prices will be automatically updated on all Lagree platforms.</span>
            </div>
            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('SAVE') }}</button>
                <a type="button" href="{{route('admin.products.edit', $product)}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'products'}}{{$product->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>

@endsection
@include('partials.modals.delete', [
        'type' => 'products',
        'id' => $product->id,
        'route' => route('admin.products.destroy', ['product' => $product]),
        'title' => $product->name,
])
