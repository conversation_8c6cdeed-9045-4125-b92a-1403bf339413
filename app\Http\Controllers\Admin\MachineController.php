<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Machine\StoreMachineRequest;
use App\Http\Requests\Admin\Machine\UpdateMachineRequest;
use App\Models\Lease;
use App\Models\Machine;
use App\Services\Admin\Machine\IMachineService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class MachineController extends Controller
{
    private IMachineService $machineService;

    public function __construct(
        IMachineService $machineService,
    )
    {
        $this->machineService = $machineService;
    }

    public function index(): View
    {
        return view('admin.machines.index');
    }

    public function search(Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';

        $machines = $this->machineService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage
        );

        foreach ($machines as $machine) {
            $machine['orderParam'] = $orderParam;
            $machine['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.machines-search', compact('machines'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $machines  = Machine::select('id', 'name')->get();

        return view('admin.machines.create', compact('machines'));
    }

    public function store(StoreMachineRequest $request)
    {
        try {
            $this->machineService->store($request->validated());
            toastr()->addSuccess('', 'Machine created successfully.');
            return redirect()->route('admin.machines.index');
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Machine creation failed');
            return redirect()->back();
        }
    }

    public function edit(Machine $machine): View
    {
        $machines  = Machine::select('id', 'name')->get();

        return view('admin.machines.edit', compact('machine', 'machines'));
    }

    public function update(Machine $machine, UpdateMachineRequest $request)
    {
        try {
            $this->machineService->update($request->validated(), $machine);
            toastr()->addSuccess('', 'Machine updated successfully.');
            return redirect()->route('admin.machines.edit', $machine);
        } catch (\Exception $e) {
            toastr()->addError('Machine update failed');
            return redirect()->back();
        }
    }

    public function destroy(Machine $machine)
    {
        try {
            if (Lease::where('machine_id', $machine->id)->exists()) {
                toastr()->addError('Machine cannot be deleted because it is used.');
                return redirect(route('admin.machines.edit', $machine));
            }
            $machine->delete();

            toastr()->addSuccess('', 'Machine deleted successfully.');
            return redirect(route('admin.machines.index'));
        } catch (\Exception $e) {
            toastr()->addError('Machine delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $machineIds = $request->input('selectedItems');
            foreach ($machineIds as $machineId) {
                if (Lease::where('machine_id', $machineId)->exists()) {
                    toastr()->addError('Machine ' . Machine::find($machineId)->name . ' cannot be deleted because it is used.');
                    return redirect(route('admin.machines.index'));
                }
            }
            Machine::whereIn('id', $machineIds)->delete();
            toastr()->addSuccess('', 'Selected machines deleted successfully.');

            return redirect(route('admin.machines.index'));
        } catch (\Exception $e) {
            toastr()->addError('Machine delete failed');
            return redirect()->back();
        }
    }

    public function get_machine(Request $request): JsonResponse
    {
        $machine = Machine::find($request->machine_id);
        $machines = Machine::where('parent_machine_id', $request->machine_id)->get();

        return response()->json(['success' => true, 'machine' => $machine, 'conf_machines' => $machines]);
    }
}
