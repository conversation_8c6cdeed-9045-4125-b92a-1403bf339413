<?php

namespace App\Services\Admin\Products;

use App\Models\Customer;
use App\Models\Products;
use App\Models\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;

class ProductsService implements IProductsService
{
    public function store(array $data)
    {
        Products::create([
            'name' => $data['name'] ?? '',
            'price' => $data['price'] ?? '0.00',
            'category' => $data['category'] ?? 'product',
            'supplier_price' => $data['supplier_price'] ?? '0.00',
            'supplier_id' => $data['supplier_id'] ?? NULL,
        ]);
    }

    public function fees_store(array $data)
    {
        Products::create([
            'name' => $data['name'] ?? '',
            'price' => $data['price'] ?? '0.00',
            'category' => 'fee',
        ]);
    }


    public function update(array $data, Products $products)
    {
        $products->update($data);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $category=''): LengthAwarePaginator
    {
        $query = Products::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where(function ($q) use ($searchData) {
                $q->where('name', 'LIKE', '%' . ltrim($searchData, '0') . '%')
                  ->orWhere('price', 'LIKE', '%' . ltrim($searchData, '0') . '%');
            });
        });
        $query->when($category !== '', function ($query) use ($category) {
            $query->where('category', $category);
        });
        $query->whereNot('custom_product', 1);
        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }
}
