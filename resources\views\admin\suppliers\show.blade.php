@extends('layouts.app')

@section('content')

        <div class="page-title no-border-btm">
            {{--                    <div class="title-left">--}}
            <div class="title-left">
                <div class="d-flex align-items-center">
                    <h3>{{ $supplier->name }}</h3>
                    <span class="ms-3">
                     @include('partials.status-badge', [
                    'status' => $supplier->is_active ? 'active' : 'inactive',
                        ])
                    </span>
                </div>
                <a href="{{route('admin.suppliers.index')}}" class="back-link">← Back</a>
            </div>


            <div id="add-license-button" class="ms-auto d-none d-sm-inline-block">
                <a href="{{ route('admin.suppliers.edit', $supplier) }}" class="d-none d-sm-inline-block btn btn-primary float-right fw-normal">{{ __('Edit') }}</a>
            </div>
        </div>

        <ul class="nav nav-tabs lh-1" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active ps-0 ls-0.6px px-2 pe-sm-3 custom-fw-500 text-uppercase" id="profile-tab" data-bs-toggle="tab"
                        data-bs-target="#profile" data-tab="profile" type="button" role="tab" aria-controls="profile"
                        aria-selected="true">{{ __('profile') }}</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link ls-0.6px px-2 px-sm-3 custom-fw-500 text-uppercase" id="orders-tab" data-bs-toggle="tab"
                        data-bs-target="#orders" data-tab="orders" type="button" role="tab" aria-controls="orders"
                        aria-selected="false">{{ __('orders') }}</button>
            </li>
        </ul>


    <div class="tab-content" id="myTabContent1">
        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab" tabindex="1">
            <div class="main-subtitle no-border-btm">
                <h5>{{ __('basic info') }}</h5>
            </div>
            <div>
                <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Supplier Name:</div>
                    <div class="">
                        {{$supplier->name}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email (default):</div>
                    <div class="">
                        {{$supplier->email}}
                    </div>
                </div>
                @if($supplier->email2 != '')
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{$supplier->email2}}
                    </div>
                </div>
                @endif
                <div class="instructor-basic-info">
                    <div class="text-secondary">Phone #:</div>
                    <div class="">
                        {{$supplier->phone}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Address:</div>
                    <div class="">
                        {{$supplier->address}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">City:</div>
                    <div class="">
                        {{$supplier->city}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Zip:</div>
                    <div class="">
                        {{$supplier->zip}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">State:</div>
                    <div class="">
                        {{$supplier->state?->name}}
                    </div>
                </div>
                </div>
                <div class="main-subtitle no-border-btm">
                    <h5>{{ __('contact person') }}</h5>
                </div>
                <div class="profile-info-div">
                <div class="instructor-basic-info">
                    <div class="text-secondary">Full Name:</div>
                    <div class="">
                        {{$supplier->contact?->first_name}} {{$supplier->contact?->last_name}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Position:</div>
                    <div class="">
                        {{$supplier->contact?->position}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Email:</div>
                    <div class="">
                        {{$supplier->contact?->email}}
                    </div>
                </div>
                <div class="instructor-basic-info">
                    <div class="text-secondary">Phone #:</div>
                    <div class="">
                        {{$supplier->contact?->phone}}
                    </div>
                    </div>
                </div>
                </div>
        </div>

        <div class="tab-pane fade" id="orders" role="tabpanel" aria-labelledby="orders-tab" tabindex="2">

        </div>



        <script type="module">
            $(document).ready(function () {
                // Handle tab click event
                $('button[data-bs-toggle="tab"]').on('click', function () {
                    var target = $(this).data('bs-target');
                    localStorage.setItem('supplierActiveTab', target);
                    loadTabContent(this);
                });

                // Retrieve the active tab from localStorage
                var supplierActiveTab = localStorage.getItem('supplierActiveTab');
                if (supplierActiveTab) {
                    activateTab(supplierActiveTab);
                } else {
                    activateTab('#profile');
                }

                // Function to activate a tab and load its content
                function activateTab(target) {
                    // Remove active class from all tabs and content
                    $('.nav-link').removeClass('active');
                    $('.tab-pane').removeClass('show active');

                    // Add active class to the selected tab and content
                    $(`button[data-bs-target="${target}"]`).addClass('active');
                    $(target).addClass('show active');

                    // Load content for the selected tab
                    var tabElement = $(`button[data-bs-target="${target}"]`);
                    loadTabContent(tabElement);
                }

                // Function to load tab content via AJAX
                function loadTabContent(tabElement) {
                    var target = $(tabElement).data('bs-target');
                    var tab = $(tabElement).data('tab');
                    var url = '{{ route("admin.suppliers.load-tab-content", ["supplier" => $supplier->id]) }}' + '?tab=' + tab;
                    var targetContent = $(target);

                    if (!$.trim(targetContent.html())) {
                        $.ajax({
                            url: url,
                            method: 'GET',
                            success: function (data) {
                                setTimeout(function () {
                                    targetContent.html(data);
                                }, 300);
                            },
                            error: function () {
                                flasherJS.error('', 'Error occurred while loading data.');
                            }
                        });
                    }
                }
            });
        </script>
@endsection
