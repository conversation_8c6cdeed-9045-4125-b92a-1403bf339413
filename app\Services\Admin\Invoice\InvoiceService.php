<?php

namespace App\Services\Admin\Invoice;

use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Payment;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceService implements IInvoiceService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer): LengthAwarePaginator
    {
        $number = self::trimNumber($searchData);

        $query = Invoice::with('payment.license.conversionRate', 'payment.lease.conversionRate')->where('customer_id', $customer->id);

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData, $number) {
            $query->where('number', 'LIKE', '%' . $number . '%');
        });

//        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function trimNumber(string $searchData): string
    {
        return ltrim($searchData, '0');
    }
}
