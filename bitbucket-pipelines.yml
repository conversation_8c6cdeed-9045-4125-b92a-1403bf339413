# You can specify a custom docker image from Docker Hub as your build environment.

# Replace the 'composer install' with the setup command required by your project.

# Custom environment variables used by this pipeline (they need to be set up in deployment settings):
# SSH_USER
# SERVER
# DIR_PATH

image: atlassian/default-image:3

definitions:
  steps:
    - step: &pull-and-install
        name: 'Pull repository data and install packages'
        script:
          - pipe: atlassian/ssh-run:0.4.1
            variables:
              SSH_USER: $SSH_USER
              SERVER: $SERVER
              PORT: $SSH_PORT
              COMMAND: 'cd $DIR_PATH && git checkout $BITBUCKET_BRANCH && git pull && composer install'

pipelines:
  branches:
    # Define actions for specific branches.
    # development:
      # - step:
          # <<: *pull-and-install
          # deployment: staging
    master:
      - step:
          <<: *pull-and-install
          # trigger: manual
          deployment: production