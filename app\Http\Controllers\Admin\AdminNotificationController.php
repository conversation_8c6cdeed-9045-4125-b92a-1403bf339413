<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Company\StoreCompanyRequest;
use App\Http\Requests\Admin\Company\UpdateCompanyRequest;
use App\Http\Requests\Admin\Notification\StoreAdminNotificationRequest;
use App\Http\Requests\Admin\Notification\UpdateAdminNotificationRequest;
use App\Models\AdminNotification;
use App\Models\Company;
use App\Models\Customer;
use App\Models\State;
use App\Services\Admin\Notification\INotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AdminNotificationController extends Controller
{
    private INotificationService $notificationService;

    public function __construct(
        INotificationService $notificationService,
    )
    {
        $this->notificationService = $notificationService;
    }

    public function index(): View
    {
        return view('admin.notifications.index');
    }

    public function search(Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';

        $notifications = $this->notificationService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
        );

        $viewContent = view('partials.forms.admin-notifications.admin-notifications-search', compact('notifications'))->render();

        return response()->json($viewContent);
    }

//    public function show(AdminNotification $notification): View
//    {
//        $customers = Customer::all();
//        return view('admin.notifications.show', compact('notification', 'customers'));
//    }

    public function create(): View
    {
        $customers = Customer::all();
        return view('admin.notifications.create', compact('customers'));
    }

    public function store(StoreAdminNotificationRequest $request)
    {
        try {
            $this->notificationService->store($request->validated());
            toastr()->addSuccess('', 'Notification created successfully.');
            return redirect()->route('admin.notifications.index');
        } catch (\Exception $e) {
            toastr()->addError('Notification creation failed');
            return redirect()->back();
        }
    }

    public function edit(AdminNotification $notification): View
    {
        $customers = Customer::all();
        return view('admin.notifications.edit', compact('notification', 'customers'));
    }

    public function update(AdminNotification $notification, UpdateAdminNotificationRequest $request)
    {
        try {
            $this->notificationService->update($request->validated(), $notification);
            toastr()->addSuccess('', 'Notification updated successfully.');
            return redirect()->route('admin.notifications.edit', $notification);
        } catch (\Exception $e) {
            toastr()->addError('Notification update failed');
            return redirect()->back();
        }
    }

    public function destroy(AdminNotification $notification)
    {
        try {
            $this->notificationService->delete($notification);
            toastr()->addSuccess('', 'Notification deleted successfully.');
            return redirect(route('admin.notifications.index'));
        } catch (\Exception $e) {
            toastr()->addError('Notification delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $notificationIds = $request->input('selectedItems');
            foreach ($notificationIds as $notificationId) {
                $notification = AdminNotification::find($notificationId);
                $this->notificationService->delete($notification);
            }
            toastr()->addSuccess('', 'Selected notifications deleted successfully.');

            return redirect(route('admin.notifications.index'));
        } catch (\Exception $e) {
            toastr()->addError('Notification delete failed');
            return redirect()->back();
        }
    }
}
