@extends('layouts.app')

@section('content')
        <div class="page-title">
            <div class="title-left">
                <h3>{{ __('new Product') }}</h3>
                <a href="{{ route('admin.products.index') }}" class="back-link">← Back</a>
            </div>
            <!--<a href="{{ route('admin.products.index') }}">
                <button type="button" class="btn btn-ghost-light-bigger back-btn">{{ __('Back') }}</button>
            </a>-->
        </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.products.store') }}" id="studio-location-form">
            @csrf

            <h5 class="form-section-title first-title">{{ __('Product info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'NAME *',
                'field_type' => 'text',
            ])
            @include('partials.forms.select', [
                'field_name' => 'supplier_id',
                'field_label' => 'SUPPLIER *',
                'values' => $suppliers,
                'field' => 'supplier',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('supplier_id'),
                'include_empty' => true
            ])
            @include('partials.forms.input', [
                'field_name' => 'price',
                'field_label' => 'RETAIL PRICE *',
                'field_type' => 'text',
            ])
            @include('partials.forms.input', [
                'field_name' => 'supplier_price',
                'field_label' => 'SUPPLIER PRICE *',
                'field_type' => 'text',
            ])
            <div class="mt-7 text-xs text-[#f59e0b]">
                <span style="color:red;">Warning: Retail or supplier prices will be automatically updated on all Lagree platforms.</span>
            </div>

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('SAVE') }}</button>
                <a type="button" href="{{route('admin.companies.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
