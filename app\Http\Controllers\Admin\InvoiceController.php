<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\CurrencyConversionHelper;
use App\Helpers\InvoiceHelper;
use App\Http\Controllers\Controller;
use App\Mail\SendInvoice;
use App\Models\AdminSettings;
use App\Models\Company;
use App\Models\Customer;
use App\Models\Invoice;
use App\Models\Payment;
use App\Services\Admin\Invoice\IInvoiceService;
use App\Services\Admin\InvoiceProduct\IInvoiceProductService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class InvoiceController extends Controller
{
    private IInvoiceService $invoiceService;
    private IInvoiceProductService $invoiceProductService;

    public function __construct(
        IInvoiceService $invoiceService,
        IInvoiceProductService $invoiceProductService,
    )
    {
        $this->invoiceService = $invoiceService;
        $this->invoiceProductService = $invoiceProductService;
    }

    public function index()
    {
        return view('admin.invoices.index');
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage    = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'number';
        $orderType  = $request->get('order_type') ?? 'desc';

        $invoices = $this->invoiceProductService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            "all",
            $customer
        );

        $currentCurrency = AdminSettings::first()->currency;
        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }
        foreach ($invoices as $invoice) {
            $invoice['sequential_id'] = $sequential_id++;
            $conversionRate = ($invoice->payment->license_id)
                ? $invoice->payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate
                : $invoice->payment->lease->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            $invoice['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($invoice->payment, $conversionRate);
            $invoice['orderParam'] = $orderParam;
            $invoice['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.invoices.invoices-search', compact('invoices', 'customer'))->render();

        return response()->json($viewContent);
    }

    public function downloadInvoice(Customer $customer, Invoice $invoice)
    {
        $pdf = InvoiceHelper::generateInvoicePDF($invoice);
        return $pdf->download('Invoice-' . $invoice->formatted_number  . '.pdf');
    }

    public function emailInvoice(Customer $customer, Invoice $invoice)
    {
        try {
            $pdf = InvoiceHelper::generateInvoicePDF($invoice);
            Mail::to($customer->owner->email)->send(new SendInvoice($invoice, $pdf));
            toastr()->addSuccess('', 'Invoice emailed successfully.');

            return redirect()->back();
        } catch (\Exception $exception) {
            toastr()->addError('Invoice email failed');
            return redirect()->back();
        }
    }
}
