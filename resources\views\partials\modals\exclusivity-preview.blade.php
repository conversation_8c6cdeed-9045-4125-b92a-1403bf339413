<div class="modal fade" id="exclusivityPreviewModal{{$id}}" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content studiospopup">
            <div class="modal-header pt-5 pb-5 border-bottom">
                <h1 class="modal-title custom-fs-14px text-center fw-normal" id="">{{$customer->name}} - exclusivity</h1>
                <button type="button" class="btn-close ms-0 position-absolute " style="right: 20px;" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-0 py-0 customer-modal-body">
                @if(count($customer['active_exclusivity']) > 0 || count($customer['inactive_exclusivity']) > 0)
                    <div class="container-fluid">
                        @if(count($customer['active_exclusivity']) > 0)
                            <div class="active-exclusivity-header row border-bottom border-light py-3 px-3">
                                <span class="text-secondary">{{count($customer['active_exclusivity'])}} active {{count($customer['active_exclusivity']) > 1 ? 'locations' : 'location'}}</span>
                            </div>
                            <div class="active-exclusivity row border-bottom border-light d-grid">
                                @foreach($customer['active_exclusivity'] as $activeLicense)
                                    <div class="active-exclusivity-data studios-popup-data">
                                        <div class="license-left d-flex flex-column">
                                            <span>{{$activeLicense->studio->name}}</span>
                                            <span class="text-secondary">{{$activeLicense->studio->zip}}</span>
                                        </div>

                                        <div class="license-right d-flex flex-column text-end">
                                            @php
//                                                $paymentsSum = 0;
//                                                $activeLicensePaymentsNotPaid = $activeLicense->payments->where('status', '!=',\App\Helpers\Constants::PAYMENT_STATUS['paid']);
//                                                foreach ($activeLicensePaymentsNotPaid as $payment) {
//                                                    $conversionRate = $payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
//                                                    $paymentsSum += $payment->payment_amount / 100 / $conversionRate;
//                                                }

                                                $conversionRate = $activeLicense->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
                                                $licensePaymentsSum = \App\Helpers\CurrencyConversionHelper::calculateLicensePaymentsSum($activeLicense, $conversionRate);
                                            @endphp
                                            <span class="text-success">{{$admin_currency_symbol}}{{number_format($licensePaymentsSum['paymentSum'], 2)}} left</span>
                                            <span class="text-secondary">{{$licensePaymentsSum['count']}}/{{$activeLicense->duration}} months left</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        @if(count($customer['inactive_exclusivity']) > 0)
                            <div class="active-exclusivity-header row border-bottom border-light py-3 px-3">
                                <span class="text-secondary">{{count($customer['inactive_exclusivity'])}} inactive exclusivity</span>
                            </div>
                                @foreach($customer['inactive_exclusivity'] as $inactiveLicense)
                                    <div class="active-exclusivity-data d-flex justify-content-between align-items-center border-bottom border-light py-5">
                                        <div class="license-left d-grid px-3">
                                            <span>{{$inactiveLicense->studio->name}}</span>
                                            <span class="text-secondary">{{$inactiveLicense->studio->zip}}</span>
                                        </div>

                                        <div class="license-right d-grid px-3 text-end">
                                            <span class="text-danger input-error">License expired</span>
                                            <span class="text-secondary">Date: </span>
                                        </div>
                                    </div>
                                @endforeach
                        @endif
                    </div>
                @else
                    <div class="empty-license-text p-4 text-center">
                        Customer doesn't have any exclusivity.
                    </div>
                @endif

            </div>
        </div>
    </div>
</div>
