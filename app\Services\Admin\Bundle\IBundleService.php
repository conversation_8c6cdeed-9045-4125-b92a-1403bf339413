<?php

namespace App\Services\Admin\Bundle;

use App\Models\Bundle;
use App\Models\Customers;
use Illuminate\Pagination\LengthAwarePaginator;

interface IBundleService
{
    public function store(array $data): void;
    public function update(array $data, Bundle $bundle): void;
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator;
    public function delete(Bundle $bundle): void;
    public function deleteMultiple(array $ids): void;
}
