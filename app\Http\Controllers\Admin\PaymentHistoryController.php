<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Models\AdminSettings;
use App\Models\Customer;
use App\Models\Purchase;
use App\Services\Admin\PaymentHistory\IPaymentHistoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PaymentHistoryController extends Controller
{

    private IPaymentHistoryService $paymentHistoryService;

    public function __construct(
        IPaymentHistoryService $paymentHistoryService,
    )
    {
        $this->paymentHistoryService = $paymentHistoryService;
    }

    public function search(Customer $customer, Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'id';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $payments = $this->paymentHistoryService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $customer->id
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($payments as $payment) {
            $payment['sequential_id'] = $sequential_id++;
            // $payment['name'] = ($payment->license_id) ? $payment->license->studio->name : $payment->lease->machine->name;
            if($payment->license_id){
                $payment['name'] = $payment->license->studio->name;
            }else if($payment->lease_id){
                $payment['name'] = $payment->lease->machine->name;
            }else if($payment->purchase_id){
                $purchase = Purchase::find($payment->purchase_id);
                $payment['name'] = $purchase->machine->name;
            }
            if($payment->license_id){
                $payment['type'] = ucfirst($payment->license->type);
            }else if($payment->lease_id){
                $payment['type'] = 'Lease';
            }else if($payment->purchase_id){
                $payment['type'] = 'Purchase';
            }
            if($payment->license_id){
                $payment['duration'] = $payment->license->duration;
            }else if($payment->lease_id){
                $payment['duration'] = $payment->lease->duration;
            }else if($payment->purchase_id){
                $payment['duration'] = 1;
            }
            // $payment['type'] = ($payment->license_id) ? ucfirst($payment->license->type) : 'Lease';
            // $payment['duration'] = ($payment->license_id) ? $payment->license->duration : $payment->lease->duration;
            $payment['invoice_number'] = $payment->invoice?->formatted_number;
            // $conversionRate = ($payment->license_id)
            //     ? $payment->license->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate
            //     : $payment->lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;

            if($payment->license_id){
                $conversionRate = $payment->license->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;                
            }else if($payment->lease_id){
                $conversionRate = $payment->lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;                
            }else if($payment->purchase_id){
                $purchase = Purchase::findOrFail($payment->purchase_id);
                $conversionRate = $purchase->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            }
    
            $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);

        }

        $viewContent = view('partials.forms.payment-history.payment-history-search', compact('payments', 'customer'))->render();

        return response()->json($viewContent);
    }
}
