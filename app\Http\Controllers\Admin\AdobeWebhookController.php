<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Http\Controllers\Controller;
use App\Http\Controllers\StripePaymentController;
use App\Jobs\SaveAgreement;
use App\Mail\PaymentLinkEmail;
use App\Models\AdminNotification;
use App\Models\Customer;
use App\Models\Agreement;
use App\Models\Payment;
use App\Models\Purchase;
use App\Models\License;
use App\Models\Lease;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Services\Admin\AdobeSign\AdobeSignService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use PHPUnit\TextUI\Configuration\Constant;

class AdobeWebhookController extends Controller
{
    // public function verifyWebhook(Request $request)
    // {
    //     $clientId = $request->header('X-AdobeSign-ClientId');
    //     dd($request->all());

    //     return response()->json(['xAdobeSignClientId' => $clientId]);
    // }

    public function verifyWebhook(Request $request)
    {
        $clientId = $request->header('X-AdobeSign-ClientId');
        if (!$clientId) {
            Log::warning('Adobe webhook verification failed: missing X-AdobeSign-ClientId header.');
            return response()->json(['error' => 'Missing Client ID'], 400);
        }
        Log::info("Adobe webhook verification request received. Client ID: $clientId");
        return response()->json(['xAdobeSignClientId' => $clientId], 200);
    }

    public function handleWebhook(Request $request)
    {
        // Log::info("WEBHOOK IS WORKING FINE");
        // Log::info($request->all());
        $clientId = $request->header('X-AdobeSign-ClientId');
        $event = $request->get('event');
        $agreement = $request->get('agreement');
        $date = $request->get('eventDate');
        $participantRole = $request->get('participantRole');
        $agreement = Agreement::where('adobe_agreement_id', $agreement['id'])->firstOrFail();

        if ($event === 'AGREEMENT_WORKFLOW_COMPLETED') {
            if (!$agreement->completed_date) {
                SaveAgreement::dispatch($agreement, $date);
                // Log::info("Agreement {$agreement->id} completed.");
                switch ($agreement->type) {
                    case 'lease':
                        $lease = Lease::where('id', $agreement->lease_id)->first();
                        $customer = Customer::where('id', $lease->customer_id)->first();
                        $lease->is_active = '1';
                        $lease->status = Constants::LEASE_STATUSES['Awaiting Payment'];
                        $lease->save();
                        $agreement->status = Constants::LEASE_STATUSES['Awaiting Payment'];
                        $agreement->save();

                        admin_notify([
                            'title' => 'Lease was signed by both sides',
                            'description' => 'Lease was signed by both sides',
                            'link' => '',
                            'type' => 'regular', // external, popup, regular
                        ]);

                        // Send payment link email for the first lease payment
                        $lease_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $lease->id))));
                        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'lease', 'id' => $lease_id])));

                        break;
                    case 'license':
                        $license = License::where('id', $agreement->license_id)->first();
                        $customer = Customer::where('id', $license->customer_id)->first();
                        $license->is_active = '1';
                        $license->status = Constants::LICENSE_STATUSES['Awaiting Payment'];
                        $license->save();
                        $agreement->status = Constants::LICENSE_STATUSES['Awaiting Payment'];
                        $agreement->save();

                        admin_notify([
                            'title' => 'License was signed by both sides',
                            'description' => 'License was signed by both sides',
                            'link' => '',
                            'type' => 'regular', // external, popup, regular
                        ]);

                        $license_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $license->id))));
                        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'license', 'id' => $license_id])));

                        break;
                    case 'purchase':
                        $payment = Payment::where('purchase_id', $agreement->purchase_id)->first();
                        $purchase = Purchase::where('id', $agreement->purchase_id)->first();
                        $customer = Customer::where('id', $purchase->customer_id)->first();
                        $purchase->status = Constants::PURCHASE_STATUSES['Awaiting Payment'];
                        $purchase->save();
                        $agreement->status = Constants::PURCHASE_STATUSES['Awaiting Payment'];
                        $agreement->save();

                        admin_notify([
                            'title' => 'Purchase was signed by both sides',
                            'description' => 'Purchase was signed by both sides',
                            'link' => '',
                            'type' => 'regular', // external, popup, regular
                        ]);

                        $purchase_id = 'IMS'.strtoupper(hash('sha512', pack('H*', hash('sha512', $purchase->id))));
                        Mail::to($customer->owner->email)->send(new PaymentLinkEmail(route('payment.form', ['type' => 'purchase', 'id' => $purchase_id])));

                        break;

                    default:
                        # code...
                        break;
                }
            }
        }

        if ($event === 'AGREEMENT_ACTION_COMPLETED') {
            if ($participantRole === 'SIGNER') {
                if (!$agreement->singing_date) {
                    $agreement->update([
                        'singing_date' => now(),
                        'status' => Agreement::STATUSES['Awaiting Payment'],
                    ]);
                    // Log::info("Agreement {$agreement->id} signed.");
                    $sign = AdobeSignService::getAgreementInfo($agreement->adobe_agreement_id);

                    if(isset($sign['message'])){
                        $sign_url = 'javascript:;';
                    }else{
                        $sign_url = $sign['signingUrlSetInfos'][0]['signingUrls'][0]['esignUrl'];
                    }

                    switch ($agreement->type) {
                        case 'lease':
                            $lease = Lease::where('id', $agreement->lease_id)->first();
                            $customer = Customer::where('id', $lease->customer_id)->first();
                            $lease->status = Constants::LEASE_STATUSES['Signed 1/2'];
                            $lease->save();
                            $agreement->status = Constants::LEASE_STATUSES['Signed 1/2'];
                            $agreement->save();

                            admin_notify([
                                'title' => 'New Lease agreement signed  by ' . $customer->name,
                                'description' => 'Lease signed by ' . $customer->name,
                                'link' => $sign_url,
                                'type' => 'popup', // external, popup, regular
                            ]);

                            break;
                        case 'license':
                            $license = License::where('id', $agreement->license_id)->first();
                            $customer = Customer::where('id', $license->customer_id)->first();
                            $license->status = Constants::LICENSE_STATUSES['Signed 1/2'];
                            $license->save();
                            $agreement->status = Constants::LEASE_STATUSES['Signed 1/2'];
                            $agreement->save();

                            admin_notify([
                                'title' => 'New License agreement signed by ' . $customer->name,
                                'description' => 'License signed by ' . $customer->name,
                                'link' => $sign_url,
                                'type' => 'popup', // external, popup, regular
                            ]);

                            break;
                        case 'purchase':
                            // $payment = Payment::where('purchase_id', $agreement->purchase_id)->first();
                            $purchase = Purchase::where('id', $agreement->purchase_id)->first();
                            $customer = Customer::where('id', $purchase->customer_id)->first();
                            $purchase->status = Constants::PURCHASE_STATUSES['Signed 1/2'];
                            $purchase->save();
                            $agreement->status = Constants::PURCHASE_STATUSES['Signed 1/2'];
                            $agreement->save();

                            admin_notify([
                                'title' => 'New Purchase agreement signed by ' . $customer->name,
                                'description' => 'Purchase signed by ' . $customer->name,
                                'link' => $sign_url,
                                'type' => 'popup', // external, popup, regular
                            ]);
                            break;

                        default:
                            # code...
                            break;
                    }
                    }
            }
        }

        return response()->json(['xAdobeSignClientId' => $clientId], 200);
    }
}
