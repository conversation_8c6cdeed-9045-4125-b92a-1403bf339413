<?php

namespace App\Services\Admin\Woocommerce;

use Automattic\WooCommerce\Client;

class WoocommerceService implements IWoocommerceService
{
    protected $woocommerce;

    public function __construct() {
        $this->woocommerce = new Client(
            config('app.woocommerce_store_url'),  // Make sure this is set
            config('app.woocommerce_consumer_key'),
            config('app.woocommerce_consumer_secret'),
            [
                'version' => 'wc/v3',
                'verify_ssl' => false,
                'timeout' => 150
            ]
        );
    }

    public function getProductById($id) {
        return $this->woocommerce->get("products/{$id}");
    }
    
    public function getAllProducts() {
        $page = 1;
        $allProducts = [];
    
        do {
            $products = $this->woocommerce->get('products', [
                'per_page' => 100, // Maximum allowed
                'page' => $page
            ]);
    
            $allProducts = array_merge($allProducts, $products);
            $page++;
    
        } while (count($products) > 0); // Stop when no more products
    
        return $allProducts;
    }

    public function updateProduct($id, $data) {
        return $this->woocommerce->put("products/{$id}", $data); // Update product
    }

    public function createProduct($data) {
        return $this->woocommerce->post("products", $data); // Create a new product
    }
    public function getUpdatedProducts($afterDate) {
        return $this->woocommerce->get('products', [
            'after' => $afterDate, // Example: '2024-01-01T00:00:00'
            'per_page' => 100
        ]);
    }
    
}
