<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PaymentLinkEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $url;
    public $type;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($url, $type = '')
    {
        $this->type = $type;
        $this->url = $url;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view('emails.payment_link')
                    ->with([
                        'url' => $this->url,
                        'title' => $this->type . ' Payment Link',
                    ]);
    }
}
