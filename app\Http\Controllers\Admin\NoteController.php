<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Note\AddNoteRequest;
use App\Models\Customer;
use App\Models\Note;

class NoteController extends Controller
{
    public function index(Customer $customer)
    {
        $notes = $customer->notes()->orderBy('created_at', 'desc')->get();
        return view('admin.notes.index', compact('notes', 'customer'));
    }

    public function store(Customer $customer, AddNoteRequest $request)
    {
        try {
            $customer->notes()->create($request->validated());
            toastr()->addSuccess('', 'Note created successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'notes']);
        } catch (\Exception $e) {
            toastr()->addError('Note creation failed');
            return redirect()->back();
        }
    }

    public function destroy(Customer $customer, Note $note)
    {
        try {
            $note->delete();
            toastr()->addSuccess('', 'Note deleted successfully.');
            return redirect()->route('admin.customers.dashboard', ['customer' => $customer, 'tab' => 'notes']);
        } catch (\Exception $e) {
            toastr()->addError('Customer delete failed');
            return redirect()->back();
        }
    }

}
