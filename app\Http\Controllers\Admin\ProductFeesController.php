<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Products\StoreProductFeesRequest;
use App\Http\Requests\Admin\Products\UpdateProductsRequest;
use App\Models\Lease;
use App\Models\Products;
use App\Models\Supplier;
use App\Services\Admin\Products\IProductsService;
use App\Services\Admin\Woocommerce\IWoocommerceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class ProductFeesController extends Controller
{
    private IProductsService $ProductFeesService;
    private IWoocommerceService $WoocommerceService;

    public function __construct(
        IProductsService $ProductFeesService,
        IWoocommerceService $WoocommerceService,
    )
    {
        $this->ProductFeesService = $ProductFeesService;
        $this->WoocommerceService = $WoocommerceService;
    }

    public function index(): View
    {
        return view('admin.fees.index');
    }

    public function search(Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';
        $category = 'fee';

        $fees = $this->ProductFeesService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $category
        );
        $viewContent = view('partials.forms.product-fees-search', compact('fees', 'orderParam', 'orderType'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        return view('admin.fees.create');
    }

    public function store(StoreProductFeesRequest $request)
    {
        if($request->validated()){
            $this->ProductFeesService->fees_store($request->validated());
            toastr()->addSuccess('', 'Products created successfully.');
            return redirect()->route('admin.productfees.index');
        }else{
            toastr()->addError("Product saving is failed.");
            return redirect()->back();
        }
    }

    public function edit(Products $fee): View
    {
        return view('admin.fees.edit', compact('fee'));
    }

    public function update(Products $fee, UpdateProductsRequest $request)
    {
        // dd($product);
        try {
            $this->ProductFeesService->update($request->validated(), $fee);
            toastr()->addSuccess('', 'Products updated successfully.');
            return redirect()->route('admin.productfees.edit', $fee);
        } catch (\Exception $e) {
            toastr()->addError('Products update failed');
            return redirect()->back();
        }
    }

    public function destroy(Products $fee)
    {
        try {
            // if (Lease::where('products_id', $product->id)->exists()) {
            //     toastr()->addError('Products cannot be deleted because it is used.');
            //     return redirect(route('admin.products.edit', $product));
            // }
            $fee->delete();

            toastr()->addSuccess('', 'Products deleted successfully.');
            return redirect(route('admin.productfees.index'));
        } catch (\Exception $e) {
            // dd($e);
            toastr()->addError('Products delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $productsIds = $request->input('selectedItems');
            // foreach ($productsIds as $productsId) {
            //     if (Lease::where('products_id', $productsId)->exists()) {
            //         toastr()->addError('Products ' . Products::find($productsId)->name . ' cannot be deleted because it is used.');
            //         return redirect(route('admin.products.index'));
            //     }
            // }
            Products::whereIn('id', $productsIds)->delete();
            toastr()->addSuccess('', 'Selected  Products deleted successfully.');

            return redirect(route('admin.productfees.index'));
        } catch (\Exception $e) {
            toastr()->addError('Products delete failed');
            return redirect()->back();
        }
    }

    public function woocommerce(){
        // dd(env('WOOCOMMERCE_STORE_URL'));
        $products = $this->WoocommerceService->getProducts();
        if(!empty($products)){
            foreach($products as $key => $product){
                if(isset($product->status) AND $product->status != 'publish'){
                    unset($products[$key]);
                }
            }
            echo "Products count: " . count($products) . "<br><br>";
            dd($products);
        }else{
            echo "No products found";
        }
    }
}
