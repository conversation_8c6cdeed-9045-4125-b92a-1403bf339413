# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###

* Quick summary


### How do I get set up? ###

* Summary of set up
* Configuration
* Dependencies
* Database configuration
* How to run tests
* Deployment instructions

### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact

### sail ###
### sail ###

```./vendor/bin/sail up -d```

```./vendor/bin/sail stop```

```./vendor/bin/sail artisan migrate```

More info:
https://laravel.com/docs/10.x/sail#main-content


### DB Tool (for example Sequel Ace) ###

```
host: localhost
username: sail
password: password
database: laravel10
```
### How to add Model into repository pattern ###

* Create interface in App\Repositories\Contracts (example UsersRepository)
* Create class in App\Repositories (example UsersRepository)
* Register model and repository in RepositoryServiceProvider as it done 
Slack test

### Run seeders ###
* To fresh and run migration run  php artisan data:seed --fresh
* To run migration no fresh data php artisan data:seed --all

