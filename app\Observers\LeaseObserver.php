<?php

namespace App\Observers;

use App\Helpers\Constants;
use App\Models\Lease;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class LeaseObserver
{
    public function created(Lease $lease)
    {
        Log::info("Lease created: {$lease->id}");
        $depositDate = Carbon::parse($lease->starting_date);
        $startDate = Carbon::parse($lease->starting_date)->startOfMonth();

        if(isset($lease->deposit_amount) AND $lease->deposit_amount != '' AND $lease->deposit_amount != NULL){
            $deposit_payment = Payment::create([
                'lease_id' => $lease->id,
                'customer_id' => $lease->customer_id,
                'payment_number' => 1,
                'payment_amount' => $lease->deposit_amount * $lease->machine_quantity,
                'payment_date' => $depositDate,
                'description' => 'Deposit',
            ]);
            Log::info("License Deposit Payment created: {$deposit_payment->id}");
        }
        $deposit_invoice = $deposit_payment->invoice()->create(['customer_id' => $lease->customer_id]);
        Log::info("License Deposit Invoice created: {$deposit_invoice->id}");

        for ($i = 1; $i <= $lease->duration; $i++) {
            $payment = Payment::create([
                'lease_id' => $lease->id,
                'machine_id' => $lease->machine_id,
                'customer_id' => $lease->customer_id,
                'payment_number' => $i,
                'payment_amount' => $lease->monthly_installment * $lease->machine_quantity,
                'payment_date' => $startDate->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
                'description' => 'Monthly installment ' . $i . '/' . $lease->duration,
            ]);

            $payment->invoice()->create(['customer_id' => $lease->customer_id]);
        }
        Log::info("Payments created for lease ID: {$lease->id}");
    }

    public function updated(Lease $lease)
    {
        if ($lease->wasChanged('monthly_installment')) {
            $lease->payments()
                ->where('payment_date', '>', Carbon::now()->endOfMonth())
                ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                ->update(['payment_amount' => $lease->monthly_installment * $lease->machine_quantity]);
        }

        if ($lease->wasChanged('duration')) {
            $oldDuration = $lease->getOriginal('duration');
            $newDuration = $lease->duration;

            if ($oldDuration > $newDuration) {
                $paymentsToDelete = $lease->payments()
                    ->where('payment_number', '>', $newDuration)
                    ->where('payment_date', '>', Carbon::now())
                    ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                foreach ($paymentsToDelete as $payment) {
                    $payment->invoice->delete();
                    $payment->forceDelete();
                }

            } elseif ($oldDuration < $newDuration) {
                $numberOfPaymentsToBeAdded = $newDuration - $oldDuration;
                $dateOfCurrentLastPayment = $lease->payments->where('payment_number', $oldDuration)->first()->payment_date;
                for ($i = 1; $i <= $numberOfPaymentsToBeAdded; $i++) {
                    $payment = $lease->payments()->create([
                        'machine_id' => $lease->machine_id,
                        'customer_id'    => $lease->customer_id,
                        'payment_number' => $oldDuration + $i,
                        'payment_amount' => $lease->monthly_installment * $lease->machine_quantity,
                        'payment_date'   => Carbon::parse($dateOfCurrentLastPayment)->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
                    ]);

                    $payment->invoice()->create(['customer_id' => $lease->customer_id]);
                }
            }
        }

        if ($lease->wasChanged('starting_date')) {
            $paymentsToDelete = $lease->payments;
            foreach ($paymentsToDelete as $payment) {
                $payment->invoice->delete();
                $payment->forceDelete();
            }
            for ($i = 1; $i <= $lease->duration; $i++) {
                $payment = $lease->payments()->create([
                    'machine_id' => $lease->machine_id,
                    'customer_id' => $lease->customer_id,
                    'payment_number' => $i,
                    'payment_amount' => $lease->monthly_installment * $lease->machine_quantity,
                    'payment_date' => Carbon::parse($lease->starting_date)->copy()->addMonthsNoOverflow($i)->firstOfMonth(),
                ]);

                $payment->invoice()->create(['customer_id' => $lease->customer_id]);
            }
        }

        if ($lease->wasChanged('machine_quantity')) {
            $lease->payments()
                ->where('payment_date', '>', Carbon::now()->endOfMonth())
                ->where('status', '!=', Constants::PAYMENT_STATUS['paid'])
                ->update(['payment_amount' => $lease->monthly_installment * $lease->machine_quantity]);
        }
    }

}
