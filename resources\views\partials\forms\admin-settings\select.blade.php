<div class="form-group lh-1 @if($errors->has($field_name)) has-danger @endif {{ $field_class ?? '' }}">
    <label class="form-label mb-2 mb-sm-3" for="{{$field_name}}">{{ $field_label }}</label>
    <select class="form-select @error($field_name) is-invalid @enderror" name="{{$field_name}}" id="{{$field_name}}" {{$multiple ?? ''}} {{$readonly ?? ''}} {{$disabled ?? ''}}>
        @if(isset($include_empty))
            <option value="">Select</option>
        @endif
        @foreach($values as $value)
            @if(isset($value->group))
               <optgroup label="{{ $value->id ?? $value }}">
                @foreach($value->group as $group)
                    <option value="{{ $group->id ?? $group }}" @selected($field_value == $group->$option_key)>{{ $group->$option_label }}</option>
                @endforeach
                </optgroup>
            @else
                <option value="{{ $value->id ?? $value }}" @selected($field_value == $value->$option_key)>{{ $value->$option_label }}</option>
            @endif
        @endforeach
    </select>

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</div>
