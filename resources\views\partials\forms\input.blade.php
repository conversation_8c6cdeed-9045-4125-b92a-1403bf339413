@if(isset($field_type) AND $field_type == 'hidden')
    <input id="{{$field_name}}" name="{{$field_name}}" class="form-control {{$field_class ?? ''}}" value="{{old($field_name) ?? $field_value ?? ''}}" type="hidden" />
@else
    <div class="form-group {{ isset($field_plugin) && ($field_plugin === 'date-picker' || $field_plugin === 'date-picker-history') ? 'date-selector-wrap datepickerr' : '' }} position-relative @if(!isset($mb)) @endif lh-1 @if($errors->has($field_name)) has-danger @endif {{ $field_class ?? '' }} text-start">

        <label class="form-label mb-2 mb-sm-3" for="{{ $field_name }}">{{ $field_label }}</label>

        <div class="input-placeholder max500 {{ (isset($field_right_mark_right) AND $field_right_mark_right) ? 'real_right' : '' }}">

            @if(isset($currency))
                <span class="currency-symbol position-absolute start-0 translate-middle-y">{{ $admin_currency_symbol ?? $currentCurrency }}</span>
            @endif

            <input id="{{$field_name}}" name="{{$field_name}}" {!! (isset($field_type) AND ($field_type === 'decimal' || $field_type === 'number')) ? 'onkeydown="return onlyNumberKey(event)"' : '' !!} class="form-control {{$input_field_class ?? ''}} {{ isset($field_plugin) && $field_plugin != '' ? $field_plugin : '' }}
                @error(str_replace(['[', ']'], ['.', ''], $field_name)) is-invalid @enderror"
                placeholder="{{$placeholder ?? 'Enter' }}"
                value="{{ old(str_replace(['[', ']'], ['.', ''], $field_name), $field_value ?? '') }}"
                data-old="{{ old(str_replace(['[', ']'], ['.', ''], $field_name), $field_value ?? '') }}"
                @if(isset($currency))
                   style="padding-left: 30px; {{ isset($max_width) ? "max-width: $max_width;" : '' }}"
                @else
                   style="{{ isset($max_width) ? "max-width: $max_width;" : '' }}"
                @endif

                @readonly(isset($readonly) && $readonly == 'readonly')
                @disabled(isset($disabled) && $disabled == 'disabled')
                @checked(isset($checked) && $checked == 'checked')
                @required(isset($required) && $required == 'required')
                @if(isset($field_type))
                    type="{{ $field_type != 'date' ? $field_type : 'text' }}"
                @endif
                @if(isset($min_num))
                    min="{{ $min_num }}"
                @endif
                @if(isset($max_num))
                    max="{{ $max_num }}"
                @endif
                   autocomplete="off"
            >
            @if(isset($product_profit))
                <span style="
                    position: absolute;
                    right: 15px;
                    bottom: 2px;
                    transform: translateY(-50%);
                    background-color: rgba(82, 193, 89, 0.15);
                    color: #52C15A;
                    padding: 5px 13px;
                    border-radius: 9999px;
                    white-space: nowrap;
                    font-size:8.8px;
                    font-weight:500;
                    display:flex;
                    justify-content:center;
                    align-items:center;
                ">
                    Profit: ${{ $product_profit }}
                </span>
            @endif

            @if(isset($field_right_mark))
                <span class="right_mark">{!! $field_right_mark !!}</span>
            @endif
            @if(isset($field_left_mark))
                <span class="left_mark">{!! $field_left_mark !!}</span>
            @endif
{{--                <span class="invalid-feedback">*{{ $errors->first($field_name) }}</span>--}}

            <span class="invalid-feedback">*{{ $errors->first(str_replace(['[', ']'], ['.', ''], $field_name)) }}</span>
            @if(isset($field_type) AND $field_type == 'password')
            <div class="password-eye">
                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                    <path id="Path_5662" data-name="Path 5662" d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z" transform="translate(-1 -4.5)" fill="#DFDFDF"/>
                </svg>
            </div>
            @endif
        </div>
    </div>
@endif

<script type="module">
    $('.decimal-field').on('input', function() {
        let value = $(this).val();
        let valid = /^\d*(\.\d{0,2})?$/.test(value);

        if (!valid) {
            $(this).val(value.slice(0, -1));
        }
    });

    $('.integer-field').on('input', function() {
        var value = $(this).val();
        var valid = /^\d*$/.test(value);
        if (!valid) {
            $(this).val(value.slice(0, -1));
        }
    });

    $('.date-field').on('input', function() {
        let value = $(this).val();
        let valid = /^\d{4}-\d{2}-\d{2}$|^\d{2}-\d{2}-\d{4}$/.test(value);
        if (!valid) {
            $(this).val(value.slice(0, -1));
        }
    });
</script>
