<?php

namespace App\Services\Admin\Machine;

use App\Models\Customer;
use App\Models\Machine;
use App\Models\User;
use Illuminate\Database\Query\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;

class MachineService implements IMachineService
{
    public function store(array $data)
    {
        Machine::create([
            'name' => $data['name'],
            'description' => $data['description'],
            'price' => $data['price'],
            'parent_machine_id' => $data['parent_machine_id'],
            'parent_show' => $data['parent_show'],
            'monthly_installment' => $data['monthly_installment'],
            'lease_deposit' => $data['lease_deposit'],
            'lease_duration' => $data['lease_duration'],
            'purchase_deposit' => $data['purchase_deposit'],
        ]);
    }

    public function update(array $data, Machine $machine)
    {
        $machine->update($data);
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator
    {
        $query = Machine::query();

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where('name', 'LIKE', '%' . $searchData . '%');
        });

        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }
}
