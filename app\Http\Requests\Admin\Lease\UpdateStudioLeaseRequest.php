<?php

namespace App\Http\Requests\Admin\Lease;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Validation\Rule;

class UpdateStudioLeaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'studio.email'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.phone'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()) {
            toastr()->addError('Please check all the fields');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
