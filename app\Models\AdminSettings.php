<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdminSettings extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'admin_id',
        'timezone',
        'date_format',
        'time_format',
        'week_start',
        'currency',
        'seo_title',
        'seo_description',
        'smtp_from_name',
        'smtp_from_email',
    ];

    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class);
    }
}
