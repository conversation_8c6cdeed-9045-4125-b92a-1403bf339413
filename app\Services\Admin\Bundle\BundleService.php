<?php

namespace App\Services\Admin\Bundle;

use App\Models\Bundle;
use App\Models\BundleItem;
use Illuminate\Pagination\LengthAwarePaginator;

class BundleService implements IBundleService
{
    public function store(array $data): void
    {
        // echo '<pre>';
        // print_r($data->toArray());
        // die();        

        $lastBundle = Bundle::withTrashed()->orderBy('id', 'desc')->first();
        $bundle = Bundle::create(
            array_merge($data, ['bundle_id' => $lastBundle ? $lastBundle->id + 1 : 1])
        );
        $bundle->items()->createMany($data['items']);
    }

    public function update(array $data, Bundle $bundle): void
    {
        $bundle->update($data);
        $ids = [];
        foreach ($data['items'] as $item) {
            if (isset($item['id'])) {
                $ids[] = $item['id'];
                unset($item['product_name']);
                $bundle->items()->where('id', $item['id'])->update($item);
            } else {
                unset($item['product_name']);
                $newItem = $bundle->items()->create($item);
                $ids[] = $newItem->id;
            }
        }
        BundleItem::where('bundle_id', $bundle->id)->whereNotIn('id', $ids)->delete();
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator
    {
        $query = Bundle::with('items');

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->where('title', 'LIKE', '%' . $searchData . '%');
            // $query->where('price', 'LIKE', '%' . $searchData . '%');
        });

        // if ($orderParam !== 'products.price') {
        //     $query->orderBy($orderParam, $orderType);
        // } else {
        //     $query->with('products')->orderBy(Products::select('price')->whereColumn('product.id', 'bundles.product_id'), $orderType);
        // }

        return $query->paginate($perPage);
    }

    public function delete(Bundle $bundle): void
    {
        $bundle->delete();
    }

    public function deleteMultiple(array $ids): void
    {
        foreach ($ids as $id) {
            $bundle = Bundle::find($id);
            if ($bundle) {
                $this->delete($bundle);
            }
        }
    }

}
