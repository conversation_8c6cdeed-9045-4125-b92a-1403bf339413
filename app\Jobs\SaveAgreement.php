<?php

namespace App\Jobs;

use App\Models\Agreement;
use App\Services\Admin\AdobeSign\AdobeSignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SaveAgreement implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Agreement $agreement;
    private string $date;

    /**
     * Create a new job instance.
     */
    public function __construct(Agreement $agreement, string $date)
    {
        $this->agreement = $agreement;
        $this->date = $date;
    }

    /**
     * Execute the job.
     * @throws \Exception
     */
    public function handle(): void
    {
        $baseUri = AdobeSignService::getBaseUri();
        AdobeSignService::getDocumentAndSaveToStorage($this->agreement, $baseUri);
    }
}
