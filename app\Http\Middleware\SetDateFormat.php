<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AdminSettings;

class SetDateFormat
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // if (Auth::check() && Auth::user()->hasRole('super-admin')) {
        if (Auth::check() && Auth::user()->hasAnyRole(explode('|', \App\Helpers\Constants::ROLES))) {
            // $settings = \auth()->user()->adminSettings;
            // $date_format = $settings->date_format ?? 'd/m/Y';
            //  Asking - Customer own date_format settings.
            $date_format = AdminSettings::first()->date_format ?? 'd/m/Y';
            view()->share('admin_date_format', $date_format);
        }

        return $next($request);
    }
}
