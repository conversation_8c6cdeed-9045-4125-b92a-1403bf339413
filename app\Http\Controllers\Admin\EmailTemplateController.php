<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\EmailTemplate\UpdateEmailTemplateRequest;
use App\Models\EmailTemplate;
use App\Services\Admin\EmailTemplates\IEmailTemplateService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class EmailTemplateController extends Controller
{
    private IEmailTemplateService $emailTemplateService;

    public function __construct(
        IEmailTemplateService $emailTemplateService,
    )
    {
        $this->emailTemplateService = $emailTemplateService;
    }

    public function index(): View
    {
        return view('admin.email-templates.index');
    }

    public function search(Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page');
        $type = $request->get('type') ?? '';
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';

        $emailTemplates = $this->emailTemplateService->search(
            $searchData,
            $orderParam,
            $orderType,
            $type,
            $perPage
        );

        $viewContent = view('partials.forms.email-templates.email-templates-search', compact('emailTemplates'))->render();

        return response()->json($viewContent);
    }

    public function show(EmailTemplate $template): View
    {
        return view('admin.email-templates.show', compact('template'));
    }

    public function update(UpdateEmailTemplateRequest $request, EmailTemplate $template)
    {
        try {
            $template->update([
                'name' => $request->get('name'),
                'body' => $request->get('body'),
            ]);

            toastr()->addSuccess('', 'Email template updated successfully!');
            return redirect()->back();
        } catch (\Exception $e) {
            toastr()->addError('', 'Something went wrong.');
            return redirect()->back();
        }
    }
}
