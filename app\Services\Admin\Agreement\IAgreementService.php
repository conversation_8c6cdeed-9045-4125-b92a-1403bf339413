<?php

namespace App\Services\Admin\Agreement;

use App\Models\Agreement;
use App\Models\Customer;
use App\Models\Invoice;
use Barryvdh\DomPDF\PDF;
use Illuminate\Pagination\LengthAwarePaginator;

interface IAgreementService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer): LengthAwarePaginator;
    public function searchAll(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator;
    public function delete(Agreement $agreement);
    public function deleteMultiple(array $ids);
}
