<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Products\StoreProductsRequest;
use App\Http\Requests\Admin\Products\UpdateProductsRequest;
use App\Models\Lease;
use App\Models\Products;
use App\Models\Supplier;
use App\Services\Admin\Products\IProductsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;


class ProductsController extends Controller
{
    private IProductsService $productsService;

    public function __construct(
        IProductsService $ProductsService
    )
    {
        $this->ProductsService = $ProductsService;
    }

    public function index(): View
    {
        return view('admin.products.index');
    }

    public function search(Request $request): JsonResponse
    {
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'name';
        $orderType = $request->get('order_type') ?? 'asc';

        $products = $this->ProductsService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage
        );

        $viewContent = view('partials.forms.products-search', compact('products', 'orderParam', 'orderType'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $suppliers = Supplier::orderBy('name')->get()->sortBy('name', SORT_NATURAL|SORT_FLAG_CASE);
        // $suppliers = [{}];
        // $categories = [];
        return view('admin.products.create', compact('suppliers'));
    }

    public function store(StoreProductsRequest $request)
    {
        try {

            $this->ProductsService->store($request->validated());
            toastr()->addSuccess('', 'Products created successfully.');
            return redirect()->route('admin.products.index');
        } catch (\Exception $e) {
            toastr()->addError('Products creation failed');
            return redirect()->back();
        }
    }

    public function edit(Products $product): View
    {
        // dd($product);
        return view('admin.products.edit', compact('product'));
    }

    public function update(Products $product, UpdateProductsRequest $request)
    {
        // dd($product);
        try {
            $this->ProductsService->update($request->validated(), $product);
            toastr()->addSuccess('', 'Products updated successfully.');
            return redirect()->route('admin.products.edit', $product);
        } catch (\Exception $e) {
            toastr()->addError('Products update failed');
            return redirect()->back();
        }
    }

    public function destroy(Products $product)
    {
        try {
            // if (Lease::where('products_id', $product->id)->exists()) {
            //     toastr()->addError('Products cannot be deleted because it is used.');
            //     return redirect(route('admin.products.edit', $product));
            // }
            $product->delete();

            toastr()->addSuccess('', 'Products deleted successfully.');
            return redirect(route('admin.products.index'));
        } catch (\Exception $e) {
            // dd($e);
            toastr()->addError('Products delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $productsIds = $request->input('selectedItems');
            // foreach ($productsIds as $productsId) {
            //     if (Lease::where('products_id', $productsId)->exists()) {
            //         toastr()->addError('Products ' . Products::find($productsId)->name . ' cannot be deleted because it is used.');
            //         return redirect(route('admin.products.index'));
            //     }
            // }
            Products::whereIn('id', $productsIds)->delete();
            toastr()->addSuccess('', 'Selected  Products deleted successfully.');

            return redirect(route('admin.products.index'));
        } catch (\Exception $e) {
            toastr()->addError('Products delete failed');
            return redirect()->back();
        }
    }

}
