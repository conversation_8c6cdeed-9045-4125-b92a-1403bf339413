<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('license_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('license_id');
            $table->foreign('license_id')->references('id')->on('licenses');
            $table->text('body');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('license_notes', function (Blueprint $table) {
            Schema::dropIfExists('license_notes');
        });
    }
};
