<?php

namespace App\Console\Commands;

use App\Models\Webhook;
use App\Services\Admin\AdobeSign\AdobeSignService;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class CreateAdobeSignWebhook extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-adobe-sign-webhook';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates global adobe sing webhook';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if (!Webhook::where('name', 'Adobe Sign Global Webhook')->exists()) {
            $client = new Client();
            $baseUrl = AdobeSignService::getBaseUri();
            $url = $baseUrl . config('adobe-sign.webhook_url');
            $appWebhookUrl = 'https://obviously-devoted-gator.ngrok-free.app/api/adobe-sign-webhook';

            $response = $client->post($url, [
                'headers' => [
                    'Authorization' => 'Bearer ' . config('adobe-sign.access_token'),
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'name' => 'Agreement Webhook',
                    'scope' => 'ACCOUNT',
                    'state' => 'ACTIVE',
                    'webhookSubscriptionEvents' => ['AGREEMENT_ACTION_COMPLETED', 'AGREEMENT_WORKFLOW_COMPLETED'],
                    'webhookUrlInfo' => ['url' => $appWebhookUrl],
                ],
            ]);

            if ($response->getStatusCode() == 201) {
                Webhook::create([
                    'name' => 'Adobe Sign Global Webhook',
                    'url' => $appWebhookUrl,
                    'status' => 'ACTIVE',
                    'type' => Webhook::ADOBE_SIGN,
                ]);
                $this->info('Webhook successfully created!');
            } else {
                $this->info('Failed to create webhook.');
            }
        } else {
            $this->info('Adobe Sign webhook already exists!');
        }
    }
}
