<?php

namespace App\Http\Services\Adobe;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class AdobeAuthorisation
{
    /**
     * @param $code
     * @return mixed
     * @throws GuzzleException
     */
    public function getBearerToken($code): mixed
    {
        //TODO: extract base_url to configurable location
        //TODO: extract endpoint to configurable location
        $url = 'https://api.na3.adobesign.com/oauth/v2/token';
        $client = new Client(
            [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]
        );

        $response = $client->post($url, [
            'form_params' => [
                'code' => $code,
                'client_id' => env("ADOBE_SIGN_CLIENT_ID"),
                'client_secret' => env("ADOBE_SIGN_CLIENT_SECRET"),
                'grant_type' => 'authorization_code',
                'redirect_uri' => 'https://www.lagreefitness.com/trainer/echo_response', //TODO: extract to .env
            ]
        ]);

        //TODO: test if response->status == 200
        $responseData = json_decode($response->getBody(), true);

        return $responseData['access_token'];
    }

    /**
     * @return mixed
     * @throws GuzzleException
     */
    public function accessToken(): mixed
    {
        //TODO: extract base_url to configurable location
        //TODO: extract endpoint to configurable location
        $refreshTokenEndpoint = 'https://secure.na3.echosign.com/oauth/v2/refresh';
        $client = new Client(
            [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
            ]
        );
        $data = [
            'form_params' => [
                'client_id' => env("ADOBE_SIGN_CLIENT_ID"),
                'client_secret' => env("ADOBE_SIGN_CLIENT_SECRET"),
                'refresh_token' => env("ADOBE_SIGN_REFRESH_TOKEN"),
                'grant_type' => 'refresh_token'
            ]
        ];
        $response = $client->post($refreshTokenEndpoint, $data);
        //TODO: test if response->status == 200
        $responseData = json_decode($response->getBody(), true);

        return $responseData['access_token'];
    }
}
