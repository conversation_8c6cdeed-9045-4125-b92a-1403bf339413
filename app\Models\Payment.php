<?php

namespace App\Models;

use App\Models\Scopes\OrderByScope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Payment extends Model
{
    use HasFactory, SoftDeletes;

    protected static function booted()
    {
        static::addGlobalScope(new OrderByScope);
    }

    protected $fillable = [
        'license_id',
        'lease_id',
        'purchase_id',
        'customer_id',
        'machine_id',
        'payment_number',
        'description',
        'payment_amount',
        'payment_date',
        'status',
        'payment_reminder',
    ];

    const PRIMARY_FOREIGN_KEY_PAIR = [
        'invoice'   => ['invoices.payment_id', 'id'],
        'machine'   => ['id', 'payments.machine_id'],
        'customer'   => ['id', 'payments.customer_id'],
    ];

    public function getPrimaryAndForeignKeys(string $relation): array
    {
        return self::PRIMARY_FOREIGN_KEY_PAIR[$relation];
    }

    public function sortableFields(): array
    {
        return [
            'id',
            'payment_amount',
            'payment_date',
            'status',
            'invoice.number',
            'machine.name',
            'customer.name'
        ];
    }

    public function sortField(): string
    {
        return (request()->input('order_param')) ? request()->input('order_param') : 'created_at';
    }

    public function sortOrder(): string
    {
        return (request()->input('order_type')) ? request()->input('order_type') : 'desc';
    }

    public function license(): BelongsTo
    {
        return $this->belongsTo(License::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function lease(): BelongsTo
    {
        return $this->belongsTo(Lease::class);
    }

    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class);
    }

    public function purchase(): HasOne
    {
        return $this->hasOne(Purchase::class);
    }

    public function machine(): BelongsTo
    {
        return $this->belongsTo(Machine::class);
    }
}
