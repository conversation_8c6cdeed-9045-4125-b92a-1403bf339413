<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Supplier\StoreSupplierRequest;
use App\Http\Requests\Admin\Supplier\UpdateSupplierRequest;
use App\Mail\ContactCustomer;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\State;
use App\Models\Countries;
use App\Services\Admin\Supplier\ISupplierService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

class SupplierController extends Controller
{
    private ISupplierService $supplierService;

    public function __construct(
        ISupplierService $supplierService,
    )
    {
        $this->supplierService = $supplierService;
    }

    public function index(): View
    {
        return view('admin.suppliers.index');
    }

    public function show(Request $request, Supplier $supplier): View
    {
        $tab = $request->query('tab', 'profile');

        return view('admin.suppliers.show', compact('supplier', 'tab'));
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $suppliers = $this->supplierService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($suppliers as $supplier) {
            $supplier['sequential_id'] = $sequential_id++;
            $supplier['orderParam'] = $orderParam;
            $supplier['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.suppliers.suppliers-search', compact('suppliers'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        return view('admin.suppliers.create', compact('states_countries'));
    }

    public function store(StoreSupplierRequest $request)
    {
        try {
            $this->supplierService->store($request->validated());
            toastr()->addSuccess('', 'Supplier created successfully.');
            return redirect()->route('admin.suppliers.index');
        } catch (\Exception $e) {
            toastr()->addError('Supplier creation failed');
            return redirect()->back()->withInput();
        }
    }

    public function edit(Supplier $supplier): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        $contact = $supplier->contact;
        return view('admin.suppliers.edit', compact('supplier', 'states_countries', 'contact'));
    }

    public function update(Supplier $supplier, UpdateSupplierRequest $request)
    {
        try {
            $this->supplierService->update($request->validated(), $supplier);
            toastr()->addSuccess('', 'Supplier updated successfully.');
            return redirect()->route('admin.suppliers.edit', $supplier);
        } catch (\Exception $e) {
            toastr()->addError('Supplier update failed');
            return redirect()->back();
        }
    }

    public function destroy(Supplier $supplier)
    {
        try {
            $this->supplierService->delete($supplier);
            toastr()->addSuccess('', 'Supplier deleted successfully.');
            return redirect(route('admin.suppliers.index'));
        } catch (\Exception $e) {
            toastr()->addError('Supplier delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $supplierIds = $request->input('selectedItems');
            $this->supplierService->deleteMultiple($supplierIds);

            toastr()->addSuccess('', 'Selected suppliers deleted successfully.');

            return redirect(route('admin.suppliers.index'));
        } catch (\Exception $e) {
            toastr()->addError('Supplier delete failed');
            return redirect()->back();
        }
    }

    public function contactSupplier(Supplier $supplier, Request $request)
    {
        try {
            $subject = $request->get('subject');
            $message = $request->get('message');
            Mail::to($supplier->email)->send(new ContactCustomer($subject, $message));
            toastr()->addSuccess('', 'Email successfully sent.');
            return redirect()->back();

        } catch (\Exception $e) {
            toastr()->addError('Email send failed');
            return redirect()->back();
        }
    }

    public function loadTabContent(Supplier $supplier, Request $request)
    {
        $tab = $request->query('tab');

        return match ($tab) {
            'orders' => view('admin.suppliers.orders-index', compact('supplier'))->render(),
            default => response()->json(['error' => 'Invalid tab'], 400),
        };
    }
}
