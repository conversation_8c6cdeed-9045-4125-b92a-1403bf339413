/* Bootstrap 5 overrides */

// Colors
$primary: #000;
$secondary: #969696;
$success: #52C15A;
$info: #969696;
$warning: #E8AF44;
$danger: #DB1818;
$light: #F0F0F0;
$lighter: #F8F8F8;
$dark: #00020a;
$border: #dddddd;

$theme-colors: (
    "primary":   $primary,
    "secondary": $secondary,
    "success":   $success,
    "info":      $info,
    "warning":   $warning,
    "danger":    $danger,
    "light":     $light,
    "lighter":   $lighter,
    "dark":      $dark
);

:root, [data-bs-theme=light] {
    --bs-border-color: #{$light};
}

// Typography
$font-family-sans-serif: 'Graphik', sans-serif;
$font-size-base: 0.75rem;

$h1-font-size: $font-size-base * 2.5;
$h2-font-size: $font-size-base * 2;
$h3-font-size: $font-size-base * 1.6667;
$h4-font-size: $font-size-base * 1.5;
$h5-font-size: $font-size-base * 1.25;
$h6-font-size: $font-size-base;

$headings-font-weight: 400;

// Buttons
$input-btn-padding-y: .6663rem;
$input-btn-padding-x: 1.563rem;
$input-btn-padding-y-sm: .6663rem;
$input-btn-padding-x-sm: 1.563rem;
$input-btn-padding-y-lg: .6663rem;
$input-btn-padding-x-lg: 1.563rem;

$btn-font-weight: 600;

$btn-close-padding-x: 0;
$btn-close-padding-y: 0;
$btn-close-opacity: .1;
$btn-close-width: 17px;
$btn-close-height: 17px;
$btn-close-bg: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 18' fill='#{$primary}'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>");

// Box Shadows
$box-shadow:                  0 0 1rem rgba(#333333, .1) !default;
$box-shadow-sm:               0 0 .25rem rgba(#333333, .075) !default;
$box-shadow-lg:               0 0 3.125rem rgba(#333333, .1) !default;
$box-shadow-inset:            inset 0 0 .25rem rgba(#333333, .1) !default;

// Spacing utility overrides
$spacer: 0.938rem !default; // 15px
$spacers: (
    0: 0,
    1: $spacer * 0.25,
    2: $spacer * 0.6663, // 10px
    3: $spacer, // 15px
    4: $spacer * 1.3326, // 20px
    5: $spacer * 1.9989, // 30px
    6: $spacer * 2.6652, // 40px
    7: $spacer * 3.3315, // 50px
    8: $spacer * 3.9978, // 60px
    9: $spacer * 4.6641, // 70px
    10: $spacer * 5.3304, // 80px
    11: $spacer * 5.9968, // 90px
) !default;

// Inputs
$input-padding-y: 0.94rem;
$input-padding-x: 1.2rem;
$input-placeholder-color: $info;

$form-label-margin-bottom: .7rem;
$input-font-size: 14px;
$input-color: $primary;
$input-placeholder-color: $info;

// Regular checkbox
$form-check-input-width: 1.667em;
$form-check-input-height: 1.667em;
$form-check-margin-bottom: 0.94rem;
$form-check-input-checked-bg-color: #ffffff;
$form-check-input-checked-border-color: $light;
$form-check-input-focus-border: $light;
$form-check-input-focus-box-shadow: none;
$form-check-input-checked-bg-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Crect x='5' y='5' width='10' height='10' fill='fill:%23000' /%3E%3C/svg%3E");

// Regular radio button
$form-check-radio-checked-bg-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3E%3Ccircle cx='10' cy='10' r='5' fill='fill:%23000' /%3E%3C/svg%3E");

// Switch checkbox
$form-switch-width: 5em;
$form-switch-bg-image: url("data:image/svg+xml,%3csvg xmlns= 'http://www.w3.org/2000/svg' viewBox= '-4 -4 8 8' %3e%3ccircle r= '3' fill= '%23969696' /%3e%3c/svg%3e");
$form-switch-focus-bg-image: url("data:image/svg+xml,%3csvg xmlns= 'http://www.w3.org/2000/svg' viewBox= '-4 -4 8 8' %3e%3ccircle r= '3' fill= '%23969696' /%3e%3c/svg%3e");
$form-switch-checked-bg-image: url("data:image/svg+xml,%3csvg xmlns= 'http://www.w3.org/2000/svg' viewBox= '-4 -4 8 8' %3e%3ccircle r= '3' fill= '%2352C15A' /%3e%3c/svg%3e");

// List group
$list-group-border-color: $light;

// Modal
$modal-md: 600px;
$modal-content-border-width: 0;

// Dropdown
$dropdown-box-shadow: $box-shadow-lg;
$dropdown-border-color: $light;
$dropdown-item-padding-y: 7px;
$dropdown-item-padding-x: 25px;
$dropdown-min-width: 210px;
$dropdown-spacer: 2rem;

// Pagination
$pagination-hover-bg: #fff;
$pagination-active-bg: #fff;
$pagination-focus-bg: #fff;
$pagination-disabled-bg: #fff;
$pagination-active-color: #00020a;

//
$accordion-icon-width: 0.75rem;
$accordion-button-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='7.41' viewBox='0 0 12 7.41'><path id='Path_5671' data-name='Path 5671' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)' fill='#000'></path></svg>");
$accordion-button-active-icon: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='12' height='7.41' viewBox='0 0 12 7.41'><path id='Path_5671' data-name='Path 5671' d='M445.59,1414.295l-4.59,4.58-4.59-4.58-1.41,1.41,6,6,6-6Z' transform='translate(-435 -1414.295)' fill='#000'></path></svg>");

/* END Bootstrap 5 overrides */

/* Custom variables */
$colors: (
    primary: #000,
    secondary: #969696,
    success: #52C15A,
    info: #969696,
    warning: #E8AF44,
    danger: #DB1818,
    light: #F0F0F0,
    lighter: #F8F8F8,
    dark: #00020a
);

@each $name, $color in $colors {
    .custom-#{$name} {
        color: $color;
    }
    .custom-bg-#{$name} {
        background-color: $color;
    }
}
