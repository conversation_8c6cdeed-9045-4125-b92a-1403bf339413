<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class ConversionRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'rateable_type',
        'rateable_id',
        'currency_id',
        'rate',
    ];

    public function rateable(): MorphTo
    {
        return $this->morphTo();
    }
}
