// Override Bootstrap's Sass default variables
//
// Nearly all variables in Bootstrap are written with the `!default` flag.
// This allows you to override the default values of those variables before
// you import Bootstrap's source Sass files.
//
// Overriding the default variable values is the best way to customize your
// CSS without writing _new_ styles. For example, you can either change
// `$body-color` or write more CSS that override's Bootstrap's CSS like so:
// `body { color: red; }`.


//
// Bring in Bootstrap
//

// Option 1
//
// Import all of Bootstrap's CSS

// @import "bootstrap/scss/bootstrap";

// Option 2
//
// Place variable overrides first, then import just the styles you need. Note that some stylesheets are required no matter what.

// Toggle global options
$enable-gradients: false;
$enable-shadows: false;
$enable-rounded: false;
$enable-negative-margins: true;
$offcanvas-box-shadow: 0 1rem 3rem rgba(0, 0, 0, .175);

// Required
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/variables-dark";
@import "bootstrap/scss/maps";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/utilities";
@import "bootstrap/scss/root";
@import "bootstrap/scss/reboot";

@import "bootstrap/scss/type";
// @import "bootstrap/scss/images";
@import "bootstrap/scss/containers";
@import "bootstrap/scss/grid";
//@import "bootstrap/scss/tables";
@import "bootstrap/scss/forms";
@import "bootstrap/scss/buttons";
@import "bootstrap/scss/transitions";
@import "bootstrap/scss/dropdown";
//@import "bootstrap/scss/button-group";
@import "bootstrap/scss/nav";
@import "bootstrap/scss/navbar"; // Requires nav
//@import "bootstrap/scss/card";
// @import "bootstrap/scss/breadcrumb";
@import "bootstrap/scss/accordion";
@import "bootstrap/scss/pagination";
@import "bootstrap/scss/badge";
 @import "bootstrap/scss/alert";
@import "bootstrap/scss/progress";
@import "bootstrap/scss/list-group";
@import "bootstrap/scss/close";
@import "bootstrap/scss/toasts";
@import "bootstrap/scss/modal"; // Requires transitions
// @import "bootstrap/scss/tooltip";
@import "bootstrap/scss/popover";
// @import "bootstrap/scss/carousel";
// @import "bootstrap/scss/spinners";
@import "bootstrap/scss/offcanvas"; // Requires transitions
// @import "bootstrap/scss/placeholders";

// Helpers
// @import "bootstrap/scss/helpers";

// Utilities
@import "bootstrap/scss/utilities/api";

// CUSTOM

// Body overflow fix
body {
    overflow: hidden;
}

// Style overrides
.btn {
    text-transform: uppercase;
    letter-spacing: .6px;
}

.btn-primary {
    letter-spacing: 1.2px;
}

.btn-white {
    font-weight: 400;
    color: rgb(var(--#{$prefix}secondary-rgb));
    text-transform: capitalize;
}

.btn.btn-link {
    text-transform: none;
    padding: 0;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open>.dropdown-toggle.btn-primary {
    background-color: #2D2D2D;
}

@import "icon-list";

//modals
.modal-header, .modal-footer{
    border:none;
}
// progress bar
.progress
    {
        height: 10px;
        width: 100px;
    }
// nav-tabs
.nav-tabs{
    border: none;
    .nav-item{
        .nav-link{
            border: none;
            color:$info;
            font-weight: 600;
            font-size: 16px;
            &.active{
                color: $primary;
            }
        }
    }
}

.flat-info-table {
    font-size: 14px;

    tr:first-of-type {
        th, td {
            border-top: 1px solid $light
        }
    }
    th, td {
        @extend .py-3;
        border-bottom: 1px solid $light
    }
    th {
        @extend .text-secondary;
        font-weight: 400;
    }
    td {
        @extend .ps-4;
    }
}

// Dropdown overrides
.dropdown-menu {
    box-shadow: var(--bs-dropdown-box-shadow);
    top: 10px !important;
}

// Form field messages
.line-style {
    &.was-validated :invalid ~ .invalid-feedback,
    &.was-validated :invalid ~ .invalid-tooltip,
    &.is-invalid ~ .invalid-feedback,
    &.is-invalid ~ .invalid-tooltip {
        margin-top: -.75rem;
    }
}

// Input overrides
.form-control {
    &:focus,
    &:active {
        box-shadow: 0px 0px 20px #0000000D;
        border-color: var(--#{$prefix}border-color)
    }
}

@import "bootstrap-datepicker";

// Pagination overrides
.pagination {
    margin-bottom: 0;
  }

  .page-link {
    color: rgb(var(--#{$prefix}secondary-rgb));
    border: none;

    &:focus {
      box-shadow: none;
    }

    &.disabled,
    .disabled > & {
      display: none;
    }
}

// Font size custom classes
$fs-values: 8px, 10px, 11px, 12px, 14px, 16px, 18px, 20px, 24px;
@each $fs-value in $fs-values {
    .fs-#{$fs-value} {
        font-size: $fs-value !important;
    }
}

.fs-14px {
    @include df-breakpoint(md){
        font-size: 12px !important;
    }
}

.fs-16px {
    @include df-breakpoint(md){
        font-size: 14px !important;
    }
}

.fs-20px {
    @include df-breakpoint(md){
        font-size: 16px !important;
    }
}

.fs-24px {
    @include df-breakpoint(md){
        font-size: 18px !important;
    }
}

// Letter spacing custom classes
$ls-values: 0.5px, 0.6px, 0.7px, 0.8px, 0.9px, 1px, 1.2px, 1.8px, 2.4px;
@each $ls-value in $ls-values {
    .ls-#{str-replace(#{$ls-value}, '.', '\\.')} {
        letter-spacing: $ls-value !important;
    }
}

// Line height custom classes
$lh-values: 25px;
@each $lh-value in $lh-values {
    .lh-#{$lh-value} {
        line-height: $lh-value !important;
    }
}

// Textarea full width
.textarea-w-full {
    width: 100% !important;
    max-width: 100% !important;
}

// Bootstrap border responsive
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

        .border#{$infix}-top {      border-top: $border-width solid #F0F0F0 !important; }
        .border#{$infix}-right {    border-right: $border-width solid #F0F0F0 !important; }
        .border#{$infix}-bottom {   border-bottom: $border-width solid #F0F0F0 !important; }
        .border#{$infix}-left {     border-left: $border-width solid #F0F0F0 !important; }

        .border#{$infix}-top-0 {    border-top: 0 !important; }
        .border#{$infix}-right-0 {  border-right: 0 !important; }
        .border#{$infix}-bottom-0 { border-bottom: 0 !important; }
        .border#{$infix}-left-0 {   border-left: 0 !important; }

        .border#{$infix}-x {
            border-left: $border-width solid #F0F0F0 !important;
            border-right: $border-width solid #F0F0F0 !important;
        }

        .border#{$infix}-y {
            border-top: $border-width solid #F0F0F0 !important;
            border-bottom: $border-width solid #F0F0F0 !important;
        }

        .border#{$infix}-none { border: none !important; }
    }

    @include media-breakpoint-down($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

        .border-df#{$infix}-none { border: none !important; }
    }
}

$utilities: map-merge(
$utilities, ( "width": map-merge(
map-get( $utilities, "width"), ( responsive: true ),),)
);

