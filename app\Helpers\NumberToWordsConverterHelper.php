<?php

namespace App\Helpers;

class NumberToWordsConverterHelper
{
    public static function convert($number): string
    {
        if (is_string($number)) {
            $number = str_replace('.00', '', str_replace([',', ' '], '', $number));
        }
        $f = new \NumberFormatter("en", \NumberFormatter::SPELLOUT);

        $parts = explode('.', $number);

        $integerPart = $parts[0];
        $decimalPart = $parts[1] ?? null;

        $numberInWords = ucwords($f->format($integerPart));

        if ($decimalPart) {
            $decimalInWords = ucwords($f->format($decimalPart));
            return $numberInWords . ' Point ' . $decimalInWords;
        }

        return $numberInWords;
    }

    public static function formatDecimal($number): string
    {
        $parts = explode('.', $number);

        $integerPart = $parts[0];
        $decimalPart = $parts[1] ?? null;

        if ($decimalPart && $decimalPart !== '00') {
            return number_format($number, 2);
        }

        return $integerPart;
    }
}
