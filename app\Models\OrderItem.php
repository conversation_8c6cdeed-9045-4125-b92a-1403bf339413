<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Products;
use App\Models\Suppliers;

class OrderItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'order_id',
        'product_id',
        'name',
        'quantity',
        'price',
    ];

    protected $appends = [
        'product_name',
        'supplier_name',
    ];

    public function getProductNameAttribute(): string
    {
        if($this->product_id == NULL OR $this->product_id == 0 OR $this->product_id == '') {
            return '';
        }
        $product = Products::find($this->product_id);

        return $product->name;
    }

    public function getSupplierNameAttribute(): string
    {
        if($this->product_id == NULL OR $this->product_id == 0 OR $this->product_id == '') {
            return '';
        }
        $product = Products::find($this->product_id);
        if($product->supplier_id == null OR $product->supplier_id == 0 OR $product->supplier_id == '') {
            return '';
        }
        $suppliers = Suppliers::find($product->supplier_id);

        return $suppliers?->name;
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Products::class);
    }

}
