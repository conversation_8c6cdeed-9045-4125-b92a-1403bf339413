<?php

namespace App\Services\Admin\Customer;

use App\Models\Currency;
use App\Models\Customer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface ICustomerService
{
    public function store(array $data);
    public function update(array $data, Customer $customer);
    public function update_setting(array $data, Customer $customer);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status, string $type): LengthAwarePaginator;
    public function delete(Customer $customer);
    public function licensesRemainingSum(Collection $collection, Currency $currency): float;
    public function leasesRemainingSum(Collection $collection, Currency $currency, $leasesById): float;
}
