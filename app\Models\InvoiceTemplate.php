<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class InvoiceTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        // 'status',
        'number',
        'title',
        // 'company_id',
        // 'customer_id',
        // 'send_date',
        // 'sent_date',
        // 'shipping_fee',
        // 'handling_fee',
        // 'show_tax',
        // 'paid',
        // 'reminder_date',
        'note',
    ];

    protected $appends = [
        'formatted_number'
    ];

    public function getFormattedNumberAttribute(): string
    {
        return str_pad($this->number, 4, '0', STR_PAD_LEFT);
    }

    public function items(): HasMany
    {
        return $this->hasMany(InvoiceTemplateProduct::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
