<?php

namespace Database\Seeders;

use App\Helpers\Constants;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = User::factory()->create([
            'first_name' => 'Super Admin',
            'last_name' => '',
            'phone' => '12345689',
            'email' => '<EMAIL>',
            'password' => '@123qweR',
        ]);

        $user->assignRole(Constants::SUPER_ADMIN);
    }
}
