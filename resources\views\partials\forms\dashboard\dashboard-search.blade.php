@if(count($payments) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="customer.name" class="sortable-list-header" data-sort="customer.name">{{ __('Customer') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="payment_amount" class="sortable-list-header" data-sort="payment_amount">{{ __('Amount') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Type') }}</div>
        <div id="payment_date" class="sortable-list-header" data-sort="payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            @foreach($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">

        @foreach($payments as $payment)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <p class="my-0 hide-transp">{{$payment->sequential_id}}</p>
                <p class="my-0"><a href="{{ route('admin.customers.dashboard', ['customer' => ($payment->customer ?? 0)]) }}" class="custom-secondary" style="text-decoration: none;">{{ $payment->customer->name ?? '-'}}</a></p>
                <p class="my-0 text-success">+ {{ $admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                @if($payment->type === \App\Helpers\Constants::LICENSE_TYPES[0])
                    <p class="my-0">Studio License: {{$payment->name}}</p>

                @elseif($payment->type === \App\Helpers\Constants::LICENSE_TYPES[1])
                    <p class="my-0">Studio Exclusivity: {{$payment->name}}</p>

                @else
                    <p class="my-0">Machine Lease: {{$payment->name}}</p>

                @endif
                <p class="my-0">{{Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y')}}</p>
            </div>
        @endforeach
        <div id="paginate paginate-all-payments">
            @if($payments)
                <div class="">
                    {!! $payments->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">

    const paymentsCount = @json($payments).total;
    const descriptiveLabel = paymentsCount === 1 ? ' Item' : ' Items';
    $('#paymentsCount').text(paymentsCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>

