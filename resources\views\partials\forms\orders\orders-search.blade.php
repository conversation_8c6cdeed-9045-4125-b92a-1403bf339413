@if(count($orders) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id">
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="name" class="sortable-list-header {{ $orders[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('Order') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="supplier.name" class="sortable-list-header {{ $orders[0]->orderParam == 'supplier.name' ? 'active' : '' }}" data-sort="supplier.name">{{ __('Supplier') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="created_at" class="sortable-list-header {{ $orders[0]->orderParam == 'created_at' ? 'active' : '' }}" data-sort="created_at">{{ __('Created') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="updated_at" class="sortable-list-header {{ $orders[0]->orderParam == 'updated_at' ? 'active' : '' }}" data-sort="updated_at">{{ __('Modified') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="sent_at" class="sortable-list-header {{ $orders[0]->orderParam == 'sent_at' ? 'active' : '' }}" data-sort="sent_at">{{ __('Sent') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header {{ $orders[0]->orderParam == 'status' ? 'active' : '' }}" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

{{--    <form id="searchForm" method="post">--}}
{{--        @csrf--}}
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button" onclick="document.querySelectorAll('.open-multi-actions:checked').length > 1 ? document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete these orders?') }}' : document.querySelector('#multi-delete-title').innerHTML = '{{ __('Are you sure you want to delete this order?') }}'">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1" id="multi-delete-title">{{ __('Are you sure you want to delete these orders?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.orders.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
{{--    </form>--}}

    @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">
            @foreach($orders as $order)
                <div class="list-group-item list-group-item-action d-grid align-items-center">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                               value="{{ $order->id }}"
                               id="flexCheckDefault_{{$order->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$order->id}}"></label>
                    </div>
                    <p class="my-0 hide-transp">{{$order->sequential_id}}</p>
                    <p class="table-name-col"><a href="{{ route('admin.orders.edit', $order) }}" style="text-decoration: none;">Order #{{$order->formatted_order_number}}</a></p>
                    <p class="my-0" style="color: #969696"><a href="{{ route('admin.suppliers.show', ['supplier' => $order->supplier]) }}" class="custom-secondary" style="text-decoration: none;">{{$order->supplier->name}}</a></p>
                    <p class="my-0">{{Carbon\Carbon::parse($order->created_at)->format('m/d/Y')}}</p>
                    <p class="my-0">{{Carbon\Carbon::parse($order->updated_at)->format('m/d/Y')}}</p>
                    <p class="my-0">{{($order->sent_at) ? Carbon\Carbon::parse($order->sent_at)->format('m/d/Y') : "-"}}</p>
                    <div class="status-div">
                        @if(\App\Enum\Order\Status::SENT  ===  $order->status)
                            <div class="d-inline-block rounded-pill py-2 px-4 lh-1 fw-medium bg-success text-success">
                                {{ __('Sent') }}
                            </div>
                        @else
                            <div class="d-inline-block rounded-pill lh-1 fw-medium text-secondary draft-pill">
                                {{ __('Draft') }}
                            </div>
                        @endif
                    </div>
                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="orders-dropdown-content">
                            <a href=""  class="send-to-supplier" data-bs-toggle="modal" data-bs-target="#sendToSupplier{{$order->id}}">{{ __('Send to supplier') }}</a>
                            <a href="{{ route('admin.orders.edit', $order) }}">{{ __('Edit') }}</a>
                            <a href="{{ route('admin.orders.download', $order) }}">{{ __('Download') }}</a>
                            <a href="" class="delete text-danger" data-bs-toggle="modal"
                               data-bs-target="#deleteModal{{'Order'}}{{$order->id}}">{{ __('Delete') }}</a>
                        </div>
                    </div>
                </div>

                @include('partials.modals.send-order-to-supplier', [
                  'type' => 'Order',
                  'id' => $order->id,
                  'route' => route('admin.orders.send-to-supplier', ['order' => $order]),
                  'title' => $order->formatted_order_number,
                ])

                @include('partials.modals.delete', [
                  'type' => 'Order',
                  'id' => $order->id,
                  'route' => route('admin.orders.destroy', ['order' => $order]),
                  'title' => 'Order #' . $order->formatted_order_number,
                ])


            @endforeach
            <div id="paginate paginate-orders">
                @if($orders)
                    <div class="">
                        {!! $orders->links() !!}
                    </div>
                @endif
            </div>
        </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const ordersCount = @json($orders).total;
    const descriptiveLabel = ordersCount === 1 ? ' Order' : ' Orders';
    $('#ordersCount').text(ordersCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.orders.delete-multiple') }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.send-to-supplier').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    $('.delete').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

</script>
