<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->unsignedBigInteger('shipping_state_id')->nullable();
            $table->foreign('shipping_state_id')->references('id')->on('states');
            $table->unsignedBigInteger('shipping_country_id')->nullable();
            $table->foreign('shipping_country_id')->references('id')->on('countries');
            $table->string('shipping_address')->nullable();
            $table->string('shipping_city')->nullable();
            $table->string('shipping_zip')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropForeign(['shipping_country_id']);
            $table->dropColumn(['shipping_state_id', 'shipping_country_id', 'shipping_address', 'shipping_city', 'shipping_zip']);
            
        });
    }
};
