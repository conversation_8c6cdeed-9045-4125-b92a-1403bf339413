<div class="form-group {{ $parent_class ?? '' }} @if($errors->has($field_name)) has-danger @endif">

    <label class="form-label {{ $field_label_class ?? '' }}" for="{{ $field_name }}">{{ $field_label }}</label>

    <textarea id="{{$field_name}}" name="{{$field_name}}"
              class="form-control p-5 fs-14px lh-25px {{ $field_class ?? '' }}"
              placeholder="{{ $placeholder ?? $field_label }}"
              rows="{{$size ?? 7}}"
              @if($render_max_char ?? true)
                  maxlength="{{ $max_chars ?? 500 }}"
              @endif
        @readonly(isset($readonly) && $readonly == 'readonly')
        @disabled(isset($disabled) && $disabled == 'disabled')
        @required(isset($required) && $required == 'required')
    >{{old($field_name) ?? $field_value ?? ''}}</textarea>

    @if( $render_max_char ?? true )
        <div id="charCount" class="mt-3 text-secondary"></div>
    @endif

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</div>

@if($render_max_char ?? true)
    @push('scripts')
        <script type="module">
            $(document).ready(function () {
                var maxLength = {{ $max_chars ?? 500 }};
                setCounter(maxLength);
                $('#' + '{{$field_name}}').on('input', function () {
                    setCounter(maxLength);
                });
            });

            function setCounter(maxLength) {
                var currentLength = $('#' + '{{$field_name}}').val().length;
                var remaining = maxLength - currentLength;
                $('#charCount').text('Characters left: ' + remaining);
            }
        </script>
    @endpush
@endif
