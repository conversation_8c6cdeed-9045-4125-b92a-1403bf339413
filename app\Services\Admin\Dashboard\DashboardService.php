<?php

namespace App\Services\Admin\Dashboard;

use App\Enum\Order\Status;
use App\Helpers\Constants;
use App\Helpers\CurrencyConversionHelper;
use App\Helpers\OrderHelper;
use App\Mail\SendInvoiceReminder;
use App\Mail\SendOrderToSupplier;
use App\Models\AdminSettings;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Lease;
use App\Models\License;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Purchase;
use App\Services\Admin\Order\IOrderService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Mail;

class DashboardService implements IDashboardService
{
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $status): LengthAwarePaginator
    {
        $query = Payment::where('status', Constants::PAYMENT_STATUS['paid'])->with('lease.machine', 'license.studio', 'customer');

        $perPage = ($perPage != 0) ? $perPage : $query->count();

        if ($searchData !== '') {
            $query->where(function ($q) use ($searchData) {
                $q->whereHas('customer', function ($q) use ($searchData) {
                    $q->where('name', 'LIKE', '%' . $searchData . '%');
                });
            });
        }

//        $query->orderBy($orderParam, $orderType);

        return $query->paginate($perPage);
    }

    public function getLastTenPayments(Currency $currentCurrency): Collection
    {
        $payments = Payment::where('status', Constants::PAYMENT_STATUS['paid'])->orderBy('id', 'desc')->take(10)->get();
        $sequential_id = 1;
        foreach ($payments as $payment) {
            $paymentType = self::getPaymentType($payment);
            $payment['type'] = $paymentType['type'];
            $payment['name'] = $paymentType['name'];
            $payment['sequential_id'] = $sequential_id++;
            if($payment->license_id){
                $conversionRate = $payment->license->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;                
            }else if($payment->lease_id){
                $conversionRate = $payment->lease->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;                
            }else if($payment->purchase_id){
                $purchase = Purchase::findOrFail($payment->purchase_id);
                $conversionRate = $purchase->conversionRate->where('currency_id', $currentCurrency->id)->first()->rate;
            }
    
            $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);
        }
        return $payments;
    }

    public function getPaymentType(Payment $payment): array
    {
        $data = [];
        if ($payment->license_id) {
            $payment = Payment::with('license.studio')->findOrFail($payment->id);
            if ($payment->license->type === Constants::LICENSE_TYPES[0]) {
                $data['type'] = Constants::LICENSE_TYPES[0];
            } else {
                $data['type'] = Constants::LICENSE_TYPES[1];
            }
            $data['name'] = $payment->license->studio->name;
        } else if ($payment->lease_id) {
            $payment = Payment::with('lease.machine')->findOrFail($payment->id);
            $data['type'] = Constants::LEASE;
            $data['name'] = $payment->lease->machine->name;            
        } else {
            $data['type'] = 'purchase';
            $data['name'] = $payment->machine->name;
        }
        return $data;
    }

    public function countCustomer(): int
    {
        return Customer::count();
    }

    public function countActiveLicenses(): array
    {
        $activeLicense = License::where('is_active', true)->get();
        return [
            'licenses_count' => $activeLicense->where('type', Constants::LICENSE_TYPES[0])->count(),
            'exclusivities_count' => $activeLicense->where('type', Constants::LICENSE_TYPES[1])->count(),
        ];
    }

    public function countActiveLeases(): int
    {
        return Lease::where('is_active', true)->count();
    }

    public function sumPayments($period, Currency $currentCurrency): array
    {
        switch ($period) {
            case 'month':
                $carbonPrevious30DaysPayments = Carbon::now()->subDays(60);
                $carbonLast30DaysPayments = Carbon::now()->subDays(30);

                $previous30DaysPayments = Payment::whereBetween('payment_date', [$carbonPrevious30DaysPayments, $carbonLast30DaysPayments])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $previous30DaysPaymentsSum = CurrencyConversionHelper::calculatePaymentsSum($previous30DaysPayments, $currentCurrency);

                $last30DaysPayments = Payment::whereBetween('payment_date', [$carbonLast30DaysPayments, Carbon::now()])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $last30DaysPaymentsSum = CurrencyConversionHelper::calculatePaymentsSum($last30DaysPayments, $currentCurrency);

                return [
                    'sum' => $last30DaysPaymentsSum,
                    'percentage' => self::calculatePercentageDifference($previous30DaysPaymentsSum, $last30DaysPaymentsSum),
                    'selected_period' => $period,
                ];

            case 'year':
                $carbonLastYear = Carbon::now()->subYear()->startOfYear();
                $carbonStartOfTheYear = Carbon::now()->startOfYear();

                $lastYear = Payment::whereBetween('payment_date', [$carbonLastYear, $carbonStartOfTheYear])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $lastYearSum = CurrencyConversionHelper::calculatePaymentsSum($lastYear, $currentCurrency);

                $currentYear = Payment::whereBetween('payment_date', [$carbonStartOfTheYear, Carbon::now()])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $currentYearSum = CurrencyConversionHelper::calculatePaymentsSum($currentYear, $currentCurrency);

                return [
                    'sum' => $currentYearSum,
                    'percentage' => self::calculatePercentageDifference($lastYearSum, $currentYearSum),
                    'selected_period' => $period,
                ];

            default:
                $carbonLast7Days = Carbon::now()->subDays(7);
                $carbonLast14Days = Carbon::now()->subDays(14);

                $previous7Days = Payment::whereBetween('payment_date', [$carbonLast14Days, $carbonLast7Days])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $previous7DaysSum = CurrencyConversionHelper::calculatePaymentsSum($previous7Days, $currentCurrency);

                $last7days = Payment::whereBetween('payment_date', [$carbonLast7Days, Carbon::now()])
                    ->where('status', Constants::PAYMENT_STATUS['paid'])
                    ->get();

                $last7daysSum = CurrencyConversionHelper::calculatePaymentsSum($last7days, $currentCurrency);

                return [
                    'sum' => $last7daysSum,
                    'percentage' => self::calculatePercentageDifference($previous7DaysSum, $last7daysSum),
                    'selected_period' => $period,
                ];
        }
    }

    public function calculatePercentageDifference($oldValue, $newValue): int
    {
        return $oldValue == 0 ? ($newValue == 0 ? 0 : 100) : (($newValue - $oldValue) / abs($oldValue)) * 100;
    }
}
