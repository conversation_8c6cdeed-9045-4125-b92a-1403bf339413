<?php

namespace App\Services\Admin\Products;

use App\Models\Customer;
use App\Models\Products;
use Illuminate\Pagination\LengthAwarePaginator;

interface IProductsService
{
    public function store(array $data);
    public function update(array $data, Products $products);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, string $category=''): LengthAwarePaginator;
}
