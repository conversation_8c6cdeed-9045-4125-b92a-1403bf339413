@mixin sc-transition($transition: all .3s ease) {
	-webkit-transition: $transition;
	-moz-transition: $transition;
	-ms-transition: $transition;
	-o-transition: $transition;
	transition: $transition;
}

@mixin sc-multi-transition($transition...) {
	-moz-transition:    $transition;
	-o-transition:      $transition;
	-webkit-transition: $transition;
	transition:         $transition;
}

@mixin sc-box-shadow($shadow) {
	-webkit-box-shadow: $shadow;
	-moz-box-shadow: $shadow;
	box-shadow: $shadow;
}

@mixin sc-filter($filter-type, $filter-amount) {
	-webkit-filter: $filter-type+unquote('(#{$filter-amount})');
	-moz-filter: $filter-type+unquote('(#{$filter-amount})');
	-ms-filter: $filter-type+unquote('(#{$filter-amount})');
	-o-filter: $filter-type+unquote('(#{$filter-amount})');
	filter: $filter-type+unquote('(#{$filter-amount})');
}

@mixin sc-break-inside($value) {
	-webkit-column-break-inside: $value;
	page-break-inside: $value;
	break-inside: $value;
}

@mixin sc-placeholder {
	&::-webkit-input-placeholder {@content}
	&:-moz-placeholder {@content}
	&::-moz-placeholder {@content}
	&:-ms-input-placeholder {@content}
}

@mixin sc-appearance($value) {
	-webkit-appearance: $value;
	-moz-appearance: $value;
	appearance: $value;
}

@mixin sc-gradient($property, $direction, $list) {
	#{$property}: -webkit-linear-gradient(#{$direction}, #{$list});
	#{$property}: -moz-linear-gradient(#{$direction}, #{$list});
	#{$property}: -o-linear-gradient(#{$direction}, #{$list});
	#{$property}: linear-gradient(#{$direction}, #{$list});
}

@mixin sc-column-count($count) {
	-webkit-column-count: $count;
	-moz-column-count: $count;
	column-count: $count;
}

@mixin sc-column-gap($gap) {
	-webkit-column-gap: $gap;
	-moz-column-gap: $gap;
	column-gap: $gap;
}

@mixin sc-background-clip($clip) {
	background-clip: $clip;
	-moz-background-clip: $clip;
	-webkit-background-clip: $clip;
}

@mixin sc-backface-visibility($value){
	-webkit-backface-visibility: $value;
}

// Media breakpoints
@mixin df-breakpoint($point) {
	@media (max-width: map-get($grid-breakpoints, $point)) { @content; }
}

@mixin breakpoint($point) {
    @media (min-width: map-get($grid-breakpoints, $point)) { @content; }
}
