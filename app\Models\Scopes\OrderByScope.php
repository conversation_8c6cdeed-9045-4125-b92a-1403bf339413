<?php

namespace App\Models\Scopes;

use App\Helpers\ModelNamespaceHelper;
use Dflydev\DotAccessData\Data;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Log;

class OrderByScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     */
    public function apply(Builder $builder, Model $model): void
    {
        $sortField = $model->sortField();
        $sortOrder = $model->sortOrder();
        if (in_array($sortField, $model->sortableFields())) {
            if (strpos($sortField, '.')) {
                [$relation, $field] = explode('.', $sortField);

                $relatedModel = app()->make(ModelNamespaceHelper::getModelNamespaces($relation));

                $keys = $model->getPrimaryAndForeignKeys($relation);

                ($sortOrder === 'asc')
                    ? $builder->orderBy(
                    $relatedModel::select($field)
                        ->whereColumn($keys[0], $keys[1])
                        ->orderBy($field)
                        ->limit(1)
                )
                    : $builder->orderByDesc(
                    $relatedModel::select($field)
                        ->whereColumn($keys[0], $keys[1])
                        ->orderByDesc($field)
                        ->limit(1)
                );
            } else {
                $builder->orderBy($sortField, $sortOrder);
            }
        }

    }
}
