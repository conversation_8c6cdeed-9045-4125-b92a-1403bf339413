<div class="form-group @if($errors->has($field_name)) has-danger @endif">

    <label for="{{ $field_name }}">{{ $field_label }}</label>
    {{$sub_label ?? ''}}

    <input id="{{$field_name}}" name="{{$field_name}}" placeholder="{{$field_label}}"
           value="{{$field_value ?? ''}}"
           type="radio"
        @readonly(isset($readonly) && $readonly == 'readonly')
        @disabled(isset($disabled) && $disabled == 'disabled')
        @checked(old($field_name) == $field_value || (isset($checked) && $checked == 'checked'))
        @required(isset($required) && $required == 'required')
    >

    @if($errors->has($field_name))
        <span class="text-danger input-error">*{{ $errors->first($field_name) }}</span>
    @endif
</div>
