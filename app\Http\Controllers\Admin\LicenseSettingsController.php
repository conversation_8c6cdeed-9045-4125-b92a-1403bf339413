<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\LicenseSettings\StoreLicenseSettingsRequest;
use App\Http\Requests\Admin\LicenseSettings\UpdateLicenseSettingsRequest;
use App\Mail\ContactCustomer;
use App\Models\Customer;
use App\Models\LicenseSettings;
use App\Models\State;
use App\Models\Countries;
use App\Services\Admin\LicenseSettings\ILicenseSettingsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

class LicenseSettingsController extends Controller
{
    private ILicenseSettingsService $licenseSettingsService;

    public function __construct(
        ILicenseSettingsService $licenseSettingsService,
    )
    {
        $this->licenseSettingsService = $licenseSettingsService;
    }

    public function index(): View
    {
        return view('admin.licenseSettings.index');
    }

    public function show(Request $request, LicenseSettings $licenseSettings): View
    {
        $tab = $request->query('tab', 'profile');

        return view('admin.licenseSettings.show', compact('licenseSettings', 'tab'));
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'asc';

        $licenseSettingss = $this->licenseSettingsService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($licenseSettingss as $licenseSettings) {
            $licenseSettings['sequential_id'] = $sequential_id++;
            $licenseSettings['orderParam'] = $orderParam;
            $licenseSettings['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.licenseSettings.licenseSettingss-search', compact('licenseSettingss'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        return view('admin.licenseSettings.create', compact('states_countries'));
    }

    public function store(StoreLicenseSettingsRequest $request)
    {
        try {
            $this->licenseSettingsService->store($request->validated());
            toastr()->addSuccess('', 'LicenseSettings created successfully.');
            return redirect()->route('admin.licenseSettings.index');
        } catch (\Exception $e) {
            toastr()->addError('LicenseSettings creation failed');
            return redirect()->back()->withInput();
        }
    }

    public function edit(LicenseSettings $licenseSettings): View
    {
        $states_countries['states'] = State::all();
        $states_countries['countries'] = Countries::all();

        $contact = $licenseSettings->contact;
        return view('admin.licenseSettings.edit', compact('licenseSettings', 'states_countries', 'contact'));
    }

    public function update(LicenseSettings $licenseSettings, UpdateLicenseSettingsRequest $request)
    {
        try {
            $this->licenseSettingsService->update($request->validated(), $licenseSettings);
            toastr()->addSuccess('', 'LicenseSettings updated successfully.');
            return redirect()->route('admin.licenseSettings.edit', $licenseSettings);
        } catch (\Exception $e) {
            toastr()->addError('LicenseSettings update failed');
            return redirect()->back();
        }
    }

    public function destroy(LicenseSettings $licenseSettings)
    {
        try {
            $this->licenseSettingsService->delete($licenseSettings);
            toastr()->addSuccess('', 'LicenseSettings deleted successfully.');
            return redirect(route('admin.licenseSettings.index'));
        } catch (\Exception $e) {
            toastr()->addError('LicenseSettings delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $licenseSettingsIds = $request->input('selectedItems');
            $this->licenseSettingsService->deleteMultiple($licenseSettingsIds);

            toastr()->addSuccess('', 'Selected licenseSettingss deleted successfully.');

            return redirect(route('admin.licenseSettings.index'));
        } catch (\Exception $e) {
            toastr()->addError('LicenseSettings delete failed');
            return redirect()->back();
        }
    }

}
