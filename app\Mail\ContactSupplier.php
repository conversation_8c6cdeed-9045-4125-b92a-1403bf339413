<?php

namespace App\Mail;

use App\Models\AdminSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactSupplier extends Mailable
{
    use Queueable, SerializesModels;

    private $supplierSubject;
    private $supplierMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(string $supplierSubject, string $supplierMessage)
    {
        $this->supplierSubject = $supplierSubject;
        $this->supplierMessage = $supplierMessage;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $settings = AdminSettings::first();
        return new Envelope(
            from: new Address($settings->smtp_from_email, $settings->smtp_from_name),
            subject: 'Contact Supplier',
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.contact-supplier',
            with: ['supplierSubject' => $this->supplierSubject, 'supplierMessage' => $this->supplierMessage],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
