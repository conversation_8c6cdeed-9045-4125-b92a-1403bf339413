@extends('layouts.login')

@section('content')
    @if ($errors->has('tokenMessage'))
        <div class="alert alert-danger">
            {{ $errors->first('tokenMessage') }}
        </div>
    @endif
    <div class="auth-card login-wrap">
        <div class="login-above">
            <div class="login-title">
                <svg xmlns="http://www.w3.org/2000/svg" width="117" height="13" viewBox="0 0 117 13">
                    <path id="Path_7384" data-name="Path 7384"
                        d="M1,4.666a.343.343,0,0,0,.344.343H7.963a.343.343,0,0,0,.344-.343V3.186a.343.343,0,0,0-.344-.343H3.352V-7.286a.355.355,0,0,0-.344-.343H1.345A.343.343,0,0,0,1-7.286Zm10.181.343h1.573a.545.545,0,0,0,.524-.361l.886-1.95h5.407l.886,1.95a.52.52,0,0,0,.524.361h1.573a.319.319,0,0,0,.307-.469L17.294-7.611a.307.307,0,0,0-.307-.2h-.181a.325.325,0,0,0-.307.2L10.875,4.54A.319.319,0,0,0,11.182,5.009ZM15.052.694l1.772-3.972h.054L18.687.694ZM25.323-1.292a6.443,6.443,0,0,0,6.492,6.464A9.147,9.147,0,0,0,36.3,4a.347.347,0,0,0,.145-.289V-.913a.34.34,0,0,0-.326-.343H32.647a.332.332,0,0,0-.344.343V.514a.328.328,0,0,0,.344.325h1.447V2.373a5.664,5.664,0,0,1-2.152.451A4.11,4.11,0,0,1,27.873-1.31,4.143,4.143,0,0,1,31.924-5.5a4.1,4.1,0,0,1,2.731,1.047.309.309,0,0,0,.47,0l1.121-1.174a.349.349,0,0,0-.018-.506A6.842,6.842,0,0,0,31.815-7.81,6.489,6.489,0,0,0,25.323-1.292ZM40.911,4.666a.343.343,0,0,0,.344.343h1.664a.355.355,0,0,0,.344-.343V.062h2.007l2.405,4.785a.31.31,0,0,0,.289.162h1.9a.342.342,0,0,0,.307-.524L47.692-.1a3.985,3.985,0,0,0,2.676-3.647,3.914,3.914,0,0,0-3.942-3.882H41.255a.343.343,0,0,0-.344.343ZM43.28-1.87V-5.463h2.948a1.8,1.8,0,0,1,1.79,1.751,1.84,1.84,0,0,1-1.79,1.842ZM54.763,4.666a.343.343,0,0,0,.344.343h7.36a.343.343,0,0,0,.344-.343V3.186a.343.343,0,0,0-.344-.343H57.114V-.317h4.467a.343.343,0,0,0,.344-.343v-1.5a.355.355,0,0,0-.344-.343H57.114V-5.463h5.353a.343.343,0,0,0,.344-.343V-7.286a.343.343,0,0,0-.344-.343h-7.36a.343.343,0,0,0-.344.343Zm12.441,0a.343.343,0,0,0,.344.343h7.36a.343.343,0,0,0,.344-.343V3.186a.343.343,0,0,0-.344-.343H69.555V-.317h4.467a.343.343,0,0,0,.344-.343v-1.5a.355.355,0,0,0-.344-.343H69.555V-5.463h5.353a.343.343,0,0,0,.344-.343V-7.286a.343.343,0,0,0-.344-.343h-7.36a.343.343,0,0,0-.344.343Zm18.246,0a.355.355,0,0,0,.344.343h1.682a.355.355,0,0,0,.344-.343V-7.286a.355.355,0,0,0-.344-.343H85.794a.355.355,0,0,0-.344.343Zm6.637-.072a.331.331,0,0,0,.344.415h1.628a.35.35,0,0,0,.326-.271L95.433-2.1h.054l3.2,7.1a.325.325,0,0,0,.307.2h.326a.307.307,0,0,0,.307-.2l3.165-7.1h.054l1.067,6.843a.392.392,0,0,0,.344.271H105.9a.314.314,0,0,0,.326-.415L104.095-7.539a.3.3,0,0,0-.326-.271h-.289a.312.312,0,0,0-.307.181L99.194.893H99.14L95.162-7.629a.33.33,0,0,0-.307-.181h-.289a.3.3,0,0,0-.326.271Zm17.776-.813a5.822,5.822,0,0,0,3.978,1.408c2.55,0,4.159-1.715,4.159-3.575,0-2.329-2.025-3.376-3.779-4.081-1.465-.6-2.134-1.174-2.134-2.022a1.439,1.439,0,0,1,1.591-1.264,5.954,5.954,0,0,1,2.514.993.476.476,0,0,0,.633-.217L117.513-6a.447.447,0,0,0-.109-.6,6.373,6.373,0,0,0-3.617-1.21c-2.875,0-4.069,1.86-4.069,3.467,0,2.131,1.7,3.214,3.4,3.9,1.519.614,2.3,1.246,2.3,2.167a1.509,1.509,0,0,1-1.646,1.39,5.993,5.993,0,0,1-2.731-1.119.419.419,0,0,0-.615.126l-.651,1.119C109.61,3.529,109.7,3.619,109.863,3.782Z"
                        transform="translate(-1.001 7.81)" />
                </svg>
            </div>
            <form method="POST" action="{{ route('login') }}" class="my-auto">
                @csrf
                <div class="input-placeholder">
                    <input id="email" type="email" class="line-style  @error('email') is-invalid @enderror"
                        name="email" value="{{ old('email') }}" required autocomplete="email" autofocus
                        placeholder="Email">
                    @error('email')
                        <span class="invalid-feedback" role="alert">{{ $message }}</span>
                    @enderror
                </div>

                <div class="position-relative">
                    <div class="input-placeholder">
                        <input id="password" type="password" class="line-style @error('password') is-invalid @enderror"
                            name="password" required autocomplete="current-password" placeholder="Password">
                        @error('password')
                            <span class="invalid-feedback" role="alert">{{ $message }}</span>
                        @enderror
                        <div class="password-eye">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="10.227" viewBox="0 0 15 10.227">
                                <path id="Path_5662" data-name="Path 5662"
                                    d="M8.5,4.5A8.064,8.064,0,0,0,1,9.614a8.057,8.057,0,0,0,15,0A8.064,8.064,0,0,0,8.5,4.5Zm0,8.523a3.409,3.409,0,1,1,3.409-3.409A3.41,3.41,0,0,1,8.5,13.023Zm0-5.455a2.045,2.045,0,1,0,2.045,2.045A2.043,2.043,0,0,0,8.5,7.568Z"
                                    transform="translate(-1 -4.5)" fill="#DFDFDF" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div
                    class="login-submit d-flex flex-column flex-md-row justify-content-between align-items-stretch align-items-md-center gap-4">
                    <button type="submit" class="btn btn-primary ms-0 ls-1.2px fw-normal">{{ __('Log in') }}</button>
                    @if (Route::has('password.request'))
                        <a href="{{ route('password.request') }}"
                            class="text-secondary f-12 text-decoration-none text-center">{{ __('Forgot Password?') }}</a>
                    @endif
                </div>
        </div>
        <div class="login-bottom">
            <a href="/new-account">Create an account as Studio, B2C, or Trainer &rightarrow;</a>
        </div>
        </form>
    </div>
@endsection
