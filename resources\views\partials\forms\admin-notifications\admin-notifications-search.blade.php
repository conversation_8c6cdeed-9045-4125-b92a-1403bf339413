@if(count($notifications) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div></div>
        <div id="title" class="sortable-list-header" data-sort="title">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these notifications?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.notifications.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">
            @foreach($notifications as $notification)
                <a href="{{ route('admin.notifications.edit', $notification) }}"
                   class="list-group-item list-group-item-action d-grid align-items-center py-4 px-4">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                               value="{{ $notification->id }}"
                               id="flexCheckDefault_{{$notification->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$notification->id}}"></label>
                    </div>

                    <p class="my-0">{{ $notification->title }}
                        <span class="d-block mt-1 text-secondary text-capitalize">Date: {{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $notification->created_at)->format('m/d/Y') }}</span>
                    </p>
                </a>
            @endforeach
            <div id="paginate paginate-admin-notifications">
                @if($notifications)
                    <div class="">
                        {!! $notifications->links() !!}
                    </div>
                @endif
            </div>
        </div>
    </form>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const notificationsCount = @json($notifications).total;
    const descriptiveLabel = notificationsCount === 1 ? ' Item' : ' Items';
    $('#notificationsCount').text(notificationsCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        // Control all on click
        selectAllElement.change(function() {
            if (this.checked) {
                tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', true);
            } else {
                tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        tableWrapper.find('.list-group input[type="checkbox"]').change(function(){
            let allChecked = true;
            let allNonChecked = false;
            tableWrapper.find('.list-group input[type="checkbox"]').each(function () {
                allChecked &= $(this).prop('checked');
                allNonChecked ||= $(this).prop('checked');
            })
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (allNonChecked === false) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });
</script>
