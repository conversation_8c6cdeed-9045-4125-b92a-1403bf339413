@extends('layouts.app')

@section('content')
    <div class="page-title ttl-dropdown no-border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ $customer->name }}</h3>
            </div>
        </div>
    </div>
    <div class="border-bottom border-light pb-4">
        <div class="row">
            <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($remainingSubscriptionsSum ?? 0, 2) }}
                        </h3>
                        <p class="customer-dashboard-card-text text-secondary"> Upcoming lease payments</p>
                        <p class="customer-dashboard-card-text text-warning">({{ date('M') }})</p>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">
                            {{ $activeLicenses + $activeExclusivities }}
                        </h3>
                        <p class="customer-dashboard-card-text text-secondary">Active licenses</p>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">{{ $activeLeases }}</h3>
                        <p class="customer-dashboard-card-text text-secondary">Active leases</p>
                        <p class="customer-dashboard-card-text text-secondary">({{ $machines }} machines)</p>
                    </div>
                </div>
            </div>

            <div class="col-12 col-md-3 mb-4 d-flex justify-content-center">
                <div class="customer-dashboard-card position-relative text-md-start">
                    <div class="customer-dashboard-card-body ps-5">
                        <h3 class="customer-dashboard-card-title">{{ $purchasedMachines }}</h3>
                        <p class="customer-dashboard-card-text text-secondary">Purchased machines</p>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="main-subtitle no-border-btm">
        <h5>last 10 payments</h5>
    </div>

    <div id="studio-last-10-payments-table" class="entity-table mb-9">
        @if (count($payments) !== 0)
            <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
                {{-- <div>{{ __('Id') }}</div> --}}
                <div>{{ __('Name') }}</div>
                <div>{{ __('Type') }}</div>
                <div>{{ __('Payment') }}</div>
                <div>{{ __('Amount') }}</div>
                <div>{{ __('Invoice #') }}</div>
                <div>{{ __('Date') }}</div>
                <div>{{ __('Status') }}</div>
                <div></div>
            </div>

            @if ($errors->any())
                <div class="alert alert-danger">
                    @foreach ($errors->all() as $error)
                        <p>{{ $error }}</p>
                    @endforeach
                </div>
            @endif
            <div class="list-group pb-2" id="results">
                @foreach ($payments as $payment)
                    <div class="list-group-item list-group-item-action d-grid align-items-center">
                        <p class="my-0">
                            {{ $payment->license_id ? $payment->license->studio->name : $payment->lease->machine->name }}
                        </p>
                        <p class="my-0 text-secondary">
                            {{ $payment->license_id ? ucfirst($payment->license->type) : 'Lease' }}</p>
                        <p class="my-0">{{ $payment->payment_number }} of
                            {{ $payment->license_id !== null ? $payment->license->duration : $payment->lease->duration }}
                        </p>
                        <p class="my-0 text-success">+
                            {{ $currentCurrency->symbol ?? "$" }}{{ number_format($payment->payment_amount, 2) }}</p>
                        <p class="my-0 text-secondary">Invoice #{{ $payment->invoice?->formatted_number }}</p>
                        <p class="my-0">{{ \Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y') }}</p>
                        <div>
                            @include('partials.b2c-badge', [
                                'status' => 'active',
                                'label' => 'Paid',
                            ])
                        </div>
                        <div class="round-button-dropdown">
                            <button class="dropbtn">
                                <i class="fa fa-ellipsis-h"></i>
                            </button>
                            <div class="dropdown-content" id="last-10-payments-dropdown-content">
                                {{-- <a
                                    href="{{ route('admin.download-invoice', ['invoice' => $payment->invoice, 'customer' => $customer]) }}">Download
                                </a> --}}
                                <a
                                    href="{{ route('studio.download-invoice', ['customer' => $customer, 'invoice' => $payment->invoice->id ?? 0]) }}">Download
                                    Invoice</a>
                                <a href="" class="email-invoice" data-bs-toggle="modal"
                                    data-bs-target="#emailInvoice{{ $payment->invoice->id ?? 0 }}">{{ __('Email Invoice') }}</a>
                            </div>
                        </div>
                    </div>
                    @include('partials.modals.email-invoice', [
                        'id' => $payment->invoice->id ?? 0,
                        'route' => route('admin.email-invoice', [
                            'customer' => $customer,
                            'invoice' => $payment->invoice->id ?? 0,
                        ]),
                        'invoice' => $payment->invoice,
                    ])
                @endforeach
            </div>
        @else
            <p class="no-results-txt">There are no results.</p>
        @endif
    </div>
@endsection

<script type="module">
    document.addEventListener('DOMContentLoaded', function() {
        $('.dropbtn').on('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            var dropdownContent = $(this).siblings('.dropdown-content');
            $('.dropdown-content').removeClass('show');
            dropdownContent.addClass('show');
        });

        $(document).on('click', function() {
            $('.dropdown-content').removeClass('show');
        });

        $('.email-invoice').on('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
        })
    })
</script>
