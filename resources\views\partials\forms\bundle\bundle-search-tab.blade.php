<style>
#invoices-table .list-group-item, #invoices-table .list-header {
	grid-template-columns: 0px 0px 130px 130px 115px auto 40px 60px;
}
</style>
@if(count($invoices) !== 0)
    @php
        // echo '<pre>';
        // print_r($invoices->toArray());
        // die();
        
    @endphp
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header {{ $invoices[0]->orderParam == 'id' ? 'active' : '' }}" data-sort="id">{{ __('') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="number" class="sortable-list-header {{ $invoices[0]->orderParam == 'number' ? 'active' : '' }}" data-sort="amount">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="customer.name" class="sortable-list-header {{ $invoices[0]->orderParam == 'amount' ? 'active' : '' }}" data-sort="amount">{{ __('Amount') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="created_at" class="sortable-list-header {{ $invoices[0]->orderParam == 'created_at' ? 'active' : '' }}" data-sort="created_at">{{ __('Created') }}
            <div class="sort-icon asc"></div>
        </div>
        {{-- <div id="updated_at" class="sortable-list-header {{ $invoices[0]->orderParam == 'updated_at' ? 'active' : '' }}" data-sort="updated_at">{{ __('Modified') }}
            <div class="sort-icon asc"></div>
        </div> --}}
        <div id="sent_date" class="sortable-list-header {{ $invoices[0]->orderParam == 'sent_date' ? 'active' : '' }}" data-sort="sent_date">{{ __('Sent') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header {{ $invoices[0]->orderParam == 'status' ? 'active' : '' }}" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these invoices?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.invoice-products.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

{{--    <form id="searchForm" method="post">--}}
{{--        @csrf--}}

        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">

            @foreach($invoices as $invoice)

                <div class="list-group-item list-group-item-action d-grid align-items-center">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                               value="{{ $invoice->id }}"
                               id="flexCheckDefault_{{$invoice->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$invoice->id}}"></label>
                    </div>
                    <p class="my-0 hide-transp">{{$invoice->sequential_id}}</p>
                    <p class="table-name-col"><a href="{{ route('admin.invoice-products.edit', $invoice->id) }}" style="text-decoration: none;">Invoice #{{$invoice->formatted_number}}</a></p>
                    <p class="my-0 custom-secondary">{{ $invoice->amount ? $admin_currency_symbol . number_format($invoice->amount, 2) : '-' }}</p>
                    <p class="my-0">{{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $invoice->created_at)->format('m/d/Y') }}</p>
                    {{-- <p class="my-0">{{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $invoice->updated_at)->format('m/d/Y') }}</p> --}}
                    <p class="my-0">{{ $invoice->sent_date ? \Carbon\Carbon::createFromFormat('Y-m-d', $invoice->sent_date)->format('m/d/Y') : '-' }}</p>
                    <div class="row-paid-status">
                        @if(\App\Enum\InvoiceProduct\PaidStatus::PAID  ===  $invoice->paid)
                            <div class="d-inline-block rounded-pill lh-1 fw-medium bg-success text-success" data-item="paid_status_{{ $invoice->id }}">
                                {{ __('Paid') }}
                            </div>
                        @else
                            <div class="d-inline-block rounded-pill lh-1 fw-medium text-secondary bg-danger" data-item="paid_status_{{ $invoice->id }}">
                                {{ __('Unpaid') }}
                            </div>
                        @endif
                    </div>

                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="invoices-dropdown-content">
                            <a href="javascript:;" class="mark_as_paid text-danger" data-id="{{ $invoice->id }}">{{ $invoice->paid === \App\Enum\InvoiceProduct\PaidStatus::PAID ? __('Mark as unpaid') : __('Mark as paid') }}</a>
                            {{-- @if ($invoice->reminder_date == NULL) --}}
                            <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#emailInvoiceReminder{{$invoice->id}}">{{ __('Send a reminder') }}</a>                                
                            {{-- @endif --}}
                            <a href="{{route('admin.invoice-products.download', ['invoiceProduct' => $invoice->id])}}">{{ __('Download Invoice') }}</a>
                            <a href="javascript:;" class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$invoice->id}}">{{ __('Email Invoice') }}</a>
                        </div>
                    </div>
                </div>
                @include('partials.modals.email-invoice', [
                    'id' => $invoice->id,
                    'route' => route('admin.invoice-products.email', ['invoiceProduct' => $invoice])
                ])

            @endforeach
            <div id="paginate paginate-invoices">
                @if($invoices)
                    <div class="">
                        {!! $invoices->links() !!}
                    </div>
                @endif
            </div>
        </div>
{{--    </form>--}}
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    $('.mark_as_paid').on('click', function(){
        var id = $(this).data('id');
        var t = $(this);
        var url = "/admin/invoice-products/" + id + "/mark_as_paid";
        
        console.log('mark_as_paid', url);
        
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': "{{ csrf_token() }}"
            }
        });
        $.ajax({
            url: url,
            type: 'POST',
            data: {
                "id": id
            },
            success: function (data) {
                console.log(data);
                console.log('Success');
                if(data.success == 1){
                    $('[data-item="paid_status_' + id + '"]').removeClass('text-secondary bg-danger').addClass('bg-success text-success').text('Paid');
                    t.text('Mark as unpaid');
                }else{
                    $('[data-item="paid_status_' + id + '"]').removeClass('bg-success text-success').addClass('text-secondary bg-danger').text('Unpaid');
                    t.text('Mark as paid');
                }
            },
            error: function (request, status, error) {
                console.log('PHP Error');
            }
        });
    });
    const invoiceProductsCount = @json($invoices).total;
    const invoicesCount = @json($invoices).total;
    const descriptiveLabel = invoiceProductsCount === 1 ? ' Item' : ' Items';
    $('#invoiceProductsCount').text(invoiceProductsCount + descriptiveLabel);
    $('#invoicesCount').text(invoicesCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.email-invoice').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        // Uncheck all items
        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        // Uncheck 'select all' for the same target
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
        checkboxes.change(function(){
            let allChecked = true;
            let anyChecked = false;
            checkboxes.each(function () {
                if (!$(this).prop('checked')) {
                    allChecked = false;
                } else {
                    anyChecked = true;
                }
            });
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (!anyChecked) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    // Handle Delete Selected Companies
    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.invoice-products.delete-multiple') }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });
</script>
