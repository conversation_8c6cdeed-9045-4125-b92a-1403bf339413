@if(count($leases) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="studio.name" class="sortable-list-header" data-sort="studio.name">{{ __('Studio') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="machine.name" class="sortable-list-header" data-sort="machine.name">{{ __('Machine') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="machine_quantity" class="sortable-list-header" data-sort="machine_quantity">{{ __('Qty') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Zip') }}</div>
        <div id="machine_price" class="sortable-list-header" data-sort="machine_price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Remaining') }}</div>
        <div>{{ __('Expires On') }}</div>
        <div id="is_active" class="sortable-list-header" data-sort="is_active">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">

            @foreach($leases as $lease)
                <div class="list-group-item list-group-item-action d-grid align-items-center">
                    <p class="my-0 hide-transp">{{$lease->sequential_id}}</p>
                    <a href="{{ route('studio.lease.show', ['customer' => $customer, 'lease' => $lease]) }}" style="text-decoration: none;" class="table-name-col">{{$lease->studio->name}} ({{ $lease->note_count }})</a>
                    <p class="my-0 text-secondary">{{ucfirst($lease->machine->name)}}</p>
                    <p class="my-0">{{$lease->machine_quantity}}</p>
                    <p class="my-0">{{$lease->studio->zip}}</p>
                    <div class="table-div">
                        <p class="my-0 text-success">{{ $currentCurrency->symbol ?? "$" }}{{number_format($lease->total, 2)}}</p>
                        <p class="my-0">{{ $currentCurrency->symbol ?? "$" }}{{number_format($lease->monthly, 2)}}/month</p>
                    </div>
                    <div class="table-div">
                        <p class="my-0 text-danger">{{ $currentCurrency->symbol ?? "$" }}{{number_format($lease->remaining['paymentSum'], 2)}}</p>
                        <p class="my-0">{{$lease->remaining['count']}}/{{$lease->duration}} months</p>
                    </div>
                    <p class="my-0">{{\Carbon\Carbon::parse($lease->expires)->format('m/d/Y')}}</p>
                    <div class="status-div">
                        @include('partials.status-badge', [
                            'status' => $lease->is_active ? 'active' : 'inactive',
                        ])
                    </div>
                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="leases-dropdown-content">
                            <a href="{{ route('studio.lease.edit', ['customer' => $customer, 'lease' => $lease]) }}">Edit</a>
                            <a href=""  class="cancel-lease text-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{'Lease'}}{{$lease->id}}">{{ __('Cancel') }}</a>
                        </div>
                    </div>
                </div>
                @include('partials.modals.cancel', [
                    'type' => 'Lease',
                    'id' => $lease->id,
                    'route' => route('admin.leases.change-status', ['customer' => $customer, 'lease' => $lease, 'is_active' => '0']),
                    'title' => 'Lease',
                ])


            @endforeach
            <div id="paginate paginate-leases">
                @if($leases)
                    <div class="">
                        {!! $leases->links() !!}
                    </div>
                @endif
            </div>
        </div>
@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    function clearLicenseActiveTab() {
        localStorage.removeItem('leaseActiveTab');
    }
    const leasesCount = @json($leases).total;
    const descriptiveLabel = leasesCount === 1 ? ' Item' : ' Items';
    $('#leasesCount').text(leasesCount + descriptiveLabel);


    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.form-check-input').on('click', function(event) {
        event.stopPropagation();
    })

    $('.cancel-lease').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })
</script>
