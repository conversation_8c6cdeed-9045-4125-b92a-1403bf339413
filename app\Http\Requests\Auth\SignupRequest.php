<?php

namespace App\Http\Requests\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SignupRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $accountType = $this->input('account-type');
    
        return [
            'account-type'     => ['required', Rule::in(['studio', 'customer', 'trainer', 'master-trainer'])],
            'studio-name'      => [Rule::requiredIf($accountType === 'studio'), 'nullable', 'string', 'max:255'],
            'user-email'       => ['required', 'email', 'max:255', 'unique:users,email'],
            'first-name'       => ['required', 'string', 'max:255'],
            'last-name'        => ['required', 'string', 'max:255'],
            'user-password'    => ['required', 'string', 'min:8', 'confirmed'],
        ];
    }
    public function attributes(): array
    {
        return [
            'account-type' => 'account type',
            'studio-name' => 'studio name',
            'user-email' => 'email address',
            'first-name' => 'first name',
            'last-name' => 'last name',
            'user-password' => 'password',
        ];
    }
}
