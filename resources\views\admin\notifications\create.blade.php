@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('NEW notification') }}</h3>
            <a href="{{ route('admin.notifications.index') }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.notifications.store') }}" id="studio-location-form">
            @csrf

            <div class="">
                <h5 class="form-section-title first-title">{{ __('notification text') }}</h5>
                @include('partials.forms.input', [
                    'field_name' => 'title',
                    'field_label' => 'TITLE *',
                    'field_type' => 'text',
                ])

                <label class="form-label mb-3 text-uppercase">{{ __('description *') }}</label>

                @include('partials.forms.textarea', [
                    'field_name' => 'description',
                    'field_label' => 'Enter',
                    'field_type' => 'text',
                    'field_label_class' => 'd-none',
                    'render_max_char' => true,
                    'max_chars' => 200,
                ])
            </div>

            <h5 class="form-section-title">{{ __('notification details') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'link',
                'field_label' => 'NOTIFICATION LINK',
                'field_type' => 'text',
            ])

            @include('partials.forms.input',  [
                'field_name' => 'published_at',
                'field_label' => 'DATE PUBLISHED',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
            ])

{{--            @include('partials.forms.select', [--}}
{{--                'field_name' => 'time',--}}
{{--                'field_label' => 'Time',--}}
{{--                'field' => 'time',--}}
{{--                'values' => $times,--}}
{{--                'field_value' => date('h:i A'),--}}
{{--                'option_key' => 'id',--}}
{{--                'option_label' => 'name',--}}
{{--            ])--}}

            <div class="form-section-title">
                <h5 class="text-uppercase custom-fw-500">{{ __('customer') }}</h5>
                <h6 class="text-secondary">(If notification is addressed to a customer)</h6>
            </div>


            @include('partials.forms.select', [
               'field_name' => 'customer_id',
               'field_label' => 'CUSTOMER',
               'values' => $customers,
               'field' => 'customer',
               'option_key' => 'id',
               'option_label' => 'name',
               'field_value' => old('customer_id'),
               'include_empty' => true
           ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.notifications.create')}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>

@endsection
