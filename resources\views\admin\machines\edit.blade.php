@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('edit machine') }}</h3>
            <a href="{{ route('admin.machines.index') }}" class="back-link">← Back</a>
        </div>
        <!--<a href="{{ route('admin.machines.index') }}">
            <button type="button"
                    class="btn btn-ghost-light-bigger back-btn">{{ __('Back') }}</button>
        </a>-->
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.machines.update', $machine) }}" id="studio-location-form">
            @csrf
            @method('PUT')

            <h5 class="form-section-title first-title">{{ __('machine info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'MACHINE NAME *',
                'field_type' => 'text',
                'field_value' => old('name', $machine['name'] ?? ''),
                ])

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'UNIT PRICE *',
               'field_type' => 'text',
               'currency' => true,
               'field_value' => old('price', $machine['price'] ?? ''),
               'input_field_class' => 'decimal-field',
            ])

            <div class="d-flex flex-column align-items-end pt-4">
                <div class="form-check d-flex align-items-center w-100 ps-0 mb-0">
                    <input id="parent_show" class="form-check-input ms-0 me-3" value="1" type="checkbox" onclick="$(this).is(':checked') ? $('.parent_machines_list').show() : $('.parent_machines_list').hide();$(this).is(':checked') ? $('#parent_show-hidden').val(1) : $('#parent_show-hidden').val(0)" @checked($machine['parent_show'])>
                    <label for="parent_show" class="form-check-label ms-0 fs-14px ls-0.7px">Is this machine a configuration?</label>
                    <input type="hidden" name="parent_show" value="{{ old('parent_show', $machine['parent_show']) }}" id="parent_show-hidden">
                </div>
            </div>

            <div class="parent_machines_list pt-4" @if((old('parent_show') AND old('parent_show') == 0) OR ($machine['parent_show'] == 0 OR $machine['parent_show'] === NULL)) style="display: none;" @endif>
                @include('partials.forms.select', [
                    'field_name' => 'parent_machine_id',
                    'field_label' => 'MACHINE *',
                    'values' => $machines,
                    'field' => 'machine',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('parent_machine_id', $machine['parent_machine_id'] ?? ''),
                    'include_empty' => true,
                ])

                @include('partials.forms.textarea', [
                    'field_name' => 'description',
                    'field_label' => 'DESCRIPTION',
                    'field_type' => 'text',
                    'placeholder' => 'Enter (optional)',
                    'field_value' => old('description', $machine['description'] ?? ''),
                ])
            </div>

            <h5 class="form-section-title">{{ __('lease info (agreement)') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'monthly_installment',
                'field_label' => 'MONTHLY INSTALLMENT (LEASE) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('monthly_installment', $machine['monthly_installment'] ?? ''),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'lease_deposit',
                'field_label' => 'DEPOSIT (PER UNIT) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('lease_deposit', $machine['lease_deposit'] ?? ''),
                'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'lease_duration',
                'field_label' => 'DEFAULT LEASE DURATION *',
                'field_type' => 'text',
                'placeholder' => '0',
                'field_right_mark' => 'months',
                'field_value' => old('lease_duration', $machine['lease_duration'] ?? ''),
                'input_field_class' => 'decimal-field',
            ])

            <h5 class="form-section-title">{{ __('purchase info (agreement)') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'purchase_deposit',
                'field_label' => 'DEPOSIT (PER UNIT) *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('purchase_deposit', $machine['purchase_deposit'] ?? ''),
                'input_field_class' => 'decimal-field',
            ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{route('admin.machines.edit', $machine)}}"
                   class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'Machine'}}{{$machine->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>

@endsection
@include('partials.modals.delete', [
        'type' => 'Machine',
        'id' => $machine->id,
        'route' => route('admin.machines.destroy', ['machine' => $machine]),
        'title' => $machine->name,
    ])
