<?php

namespace App\Http\Requests\Admin\Machine;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\ValidationException;

class StoreMachineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $this->prepareForValidation();
        return [
            'name'                  => 'required|string',
            'description'           => 'nullable|string',
            'price'                 => 'required|numeric|min:0',
            'parent_machine_id'     => 'nullable|integer|exists:machines,id',
            'parent_show'           => 'nullable|numeric|min:0',
            'monthly_installment'   => 'required|numeric|min:0',
            'lease_deposit'         => 'required|numeric|min:0',
            'lease_duration'        => 'required|numeric|min:0',
            'purchase_deposit'      => 'required|numeric|min:0',
        ];
    }
    protected function prepareForValidation()
    {
        if ($this->has('parent_show') AND $this->input('parent_show') == 0) {
            $this->merge([
                'parent_machine_id' => NULL
            ]);
        }
        if ($this->has('price')) {
            $this->merge([
                'price' => $this->cleanCurrency($this->input('price'))
            ]);
        }
        if ($this->has('monthly_installment')) {
            $this->merge([
                'monthly_installment' => $this->cleanCurrency($this->input('monthly_installment'))
            ]);
        }
        if ($this->has('lease_deposit')) {
            $this->merge([
                'lease_deposit' => $this->cleanCurrency($this->input('lease_deposit'))
            ]);
        }
        if ($this->has('purchase_deposit')) {
            $this->merge([
                'purchase_deposit' => $this->cleanCurrency($this->input('purchase_deposit'))
            ]);
        }
    }
    private function cleanCurrency($value)
    {
        if ($value === '') {
            return null;
        }
        // Remove everything except numbers, dots, and negative sign
        return preg_replace('/[^0-9.-]+/', '', $value);
    }

    protected function failedValidation(Validator $validator)
    {
        // dd($this);
        if ($validator->errors()) {
            toastr()->addError('Some fields are required.');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
