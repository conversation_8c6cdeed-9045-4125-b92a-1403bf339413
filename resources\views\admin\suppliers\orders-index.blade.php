
    <div class="main-subtitle">
            <h5>{{ __('orders') }}</h5>
{{--            <a href="{{ route('admin.orders.create') }}" class="d-none d-sm-inline-block btn btn-primary float-right">{{ __('Add New') }}</a>--}}
    </div>

    <div class="nr-items">
        <h5 id="ordersCount"></h5>

        <div class="sortbysearch">
            <div class="zIndex-10000 filter-dropdown">
            <select class="form-select table-filter-select" aria-label="Default select example" id="statusSelect">
                <option selected value="all">Sort by: {{ __('All') }}</option>
                <option value="{{\App\Enum\Order\Status::DRAFT}}">Sort by: {{ \App\Enum\Order\Status::STATUSES[0] }}</option>
                <option value="{{\App\Enum\Order\Status::SENT}}">Sort by: {{ \App\Enum\Order\Status::STATUSES[1] }}</option>
            </select>
            </div>
            <div class="ms-auto lg-search h-40px">
                <input class="typeahead form-control" id="search" type="text" name="lagree-search" placeholder="Search"
                       autocomplete="off">
                <span class="lg-search-ico"><img src="/search.svg" alt="search" class=""></span>
            </div>
        </div>
    </div>

    <div id="orders-table" class="entity-table bg--loading-black"></div>
<script type="module">
    // document.addEventListener('DOMContentLoaded', function () {
        const searchInput = $('#search');
        const searchIcon = $('.lg-search-ico');
        let searchData = '';
        let orderParam = 'id';
        let orderType = 'desc';
        let supplierId = {{$supplier->id}};
        const sortIcon = $('.sort-icon');
        const ordersTable = $('#orders-table');
        const html = $('html, body');

        @php
            $route = route('admin.orders.search');
        @endphp

        function fetchData(url) {
            let perPage = $('.pagination-select').val() ?? 10;
            let selectedValue = $('#statusSelect').val();
            $.ajax({
                url: url,
                data: {
                    status: selectedValue,
                    search_data: searchData,
                    order_param: orderParam,
                    order_type: orderType,
                    supplier_id: supplierId,
                    per_page: perPage,
                },
                success: function (data) {
                    setTimeout(function () {
                        ordersTable.removeClass('bg--loading-black').html(data);
                        // ordersTable.trigger('resultLoaded');
                        setSortIcon();
                        $('.pagination-select').val(perPage);
                    }, 300);
                },
                error: function () {
                    flasherJS.error('', 'Error occurred while loading data.');
                }
            });
        }

        $('body').on('change', '#statusSelect', function (e) {
            fetchData("{{ $route }}");
        });

        $('body').on('change', '.pagination-select', function (e) {
            fetchData("{{ $route }}");
        });

        $('body').on('click', '.pagination a', function (e) {
            e.preventDefault();
            const url = $(this).attr('href');
            fetchData(url);
        });

        searchInput.on('input', debounce(function () {
            searchData = $(this).val();
            fetchData("{{ $route }}");
        }, 500));

        searchInput.on('click focus', function(e){
            e.stopPropagation();
        });

        $('body').on('click', '.sortable-list-header', function () {
            const newOrderParam = $(this).data('sort');
            if (orderParam === newOrderParam) {
                // Toggle sorting direction if the same column is clicked
                orderType = orderType === 'asc' ? 'desc' : 'asc';
            } else {
                // Set new sort column and default direction to ascending
                orderParam = newOrderParam;
                orderType = 'asc';
            }
            fetchData("{{ $route }}");
        });

        function debounce(func, wait, immediate) {
            let timeout;
            return function () {
                let context = this, args = arguments;
                let later = function () {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                let callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        function setSortIcon() {
            sortIcon.removeClass('asc desc'); // Remove existing sort classes
            $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(orderType); // Add active sort class
        }

        searchIcon.click(function (e) {
            e.stopPropagation();
            searchInput.get(0).classList.toggle("lg-search-expanded");

            if (searchData !== '' && !searchInput.get(0).classList.contains("lg-search-expanded")) {
                searchInput.val('');
                searchData = '';
                fetchData("{{ $route }}");
            }else{
                searchInput.focus();
            }
        });

        html.click(function (e) {
            e.stopPropagation();
            searchInput.get(0).classList.remove("lg-search-expanded");
            searchInput.val('');
            searchData = '';
        });

        // Initial load
        fetchData("{{ $route }}");
    // });
</script>