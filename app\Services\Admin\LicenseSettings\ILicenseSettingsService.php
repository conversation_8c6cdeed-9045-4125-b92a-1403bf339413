<?php

namespace App\Services\Admin\LicenseSettings;

use App\Models\LicenseSettings;
use Illuminate\Pagination\LengthAwarePaginator;

interface ILicenseSettingsService
{
    public function store(array $data);
    public function update(array $data, LicenseSettings $licenseSettings);
    public function search(string $searchData, string $orderParam, string $orderType, int $perPage): LengthAwarePaginator;
    public function delete(LicenseSettings $licenseSettings);
    public function deleteMultiple(array $ids);
}
