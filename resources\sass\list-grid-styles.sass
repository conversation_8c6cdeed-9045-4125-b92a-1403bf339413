// Multi-select actions
.multi-select-actions
    // Hide container by default
    display: none

.entity-table
    overflow-x: auto
    +breakpoint(lg)
        overflow-x: unset

    .list-group-item, .list-header
        width: 900px
        +breakpoint(lg)
            width: 100%

    .list-header
        letter-spacing: .6px
        line-height: 1
        padding: 20px 10px
        column-gap: 20px
        +breakpoint(lg)
            padding: 20px 20px

    .list-group-item
        padding: 25px 10px
        column-gap: 20px
        +breakpoint(lg)
            padding: 25px 20px

    .sortable-list-header
        cursor: pointer
        display: flex
        align-items: center

    .asc, .desc
        width: 0
        height: 0
        border-style: solid

    .asc
        border-width: 0 4px 6px 4px
        border-color: transparent transparent rgba(var(--bs-info-rgb), var(--bs-text-opacity)) transparent

    .desc
        border-width: 6px 4px 0 4px
        border-color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) transparent transparent transparent

    .sort-icon
        margin-left: 10px

    .sort-icon.asc.active, .sort-icon.desc.active
        color: #000


// Table grid layouts
#customers-table
    .list-group-item, .list-header
        grid-template-columns: 20px 28px 12% 10% 10% 10% 32% 6% 3%

#b2c-customers-table
    .list-group-item, .list-header
        grid-template-columns: 0% 0% 12% 67% 6% 3%

#machines-table
    .list-group-item, .list-header
        grid-template-columns: 2% 3% 12% 10% 10% 10% 30% 10% 5%

#licenseSettings-table
    .list-group-item, .list-header
        grid-template-columns: 20px 20% 15% 15% 1fr

#companies-table
    .list-header
        grid-template-columns: 20px 30px 20% 60% 7%
    .list-group-item
        grid-template-columns: 2% 1.5% 5% 14% 59.5% 2% 6%

#licences-table
    .list-group-item, .list-header
        grid-template-columns: 1% 16% 8% 8% 8% 30% 19% 5% 5%

#studio-location-table
    .list-group-item, .list-header
        grid-template-columns: 1% 10% 8% 8% 8% 36% 5% 5%
#ticket-table
    .list-group-item, .list-header
        grid-template-columns: 1% 10% 8% 8% 8% 36% 5% 5%

#upcoming-payments-table
    .list-group-item, .list-header
        grid-template-columns: 3% 10% 10% 10% 46% 10% 5%

#payment-history-table
    .list-group-item, .list-header
        grid-template-columns: 3% 10% 10% 10% 10% 10% 18% 10% 3%

#leases-table
    .list-group-item, .list-header
        grid-template-columns: 3% 13% 9% 7% 7% 13% 11% 12% 8% 4%

#invoices-table
    .list-group-item, .list-header
        grid-template-columns: 2.5% 10% 68% 9% 4%

#agreements-table
    .list-group-item, .list-header
        grid-template-columns: 3% 12% 12% 51% 12% 3%

#purchases-table
    .list-group-item, .list-header
        grid-template-columns: 0% 13% 5% 10% 10.6% 1fr 140px 3%

#payments-history-table
    .list-group-item, .list-header
        grid-template-columns: 3% 14% 9% 9% 11% 13% 19% 7% 3%

#last-10-payments-table
    .list-group-item, .list-header
        grid-template-columns: 0% 15% 100px 85px 110px 120px 1fr 4% 30px

#studio-last-10-payments-table
    .list-group-item, .list-header
        grid-template-columns: 15% 100px 85px 110px 120px 1fr 4% 30px

#suppliers-table
    .list-group-item, .list-header
        grid-template-columns: 2% 3% 15% 62% 8% 4%

.items-header
    grid-template-columns: 3% 78% 10% 8%

.items-group
    grid-template-columns: 3% 78% 10% 8%

#orders-table
    .list-group-item, .list-header
        grid-template-columns: 2% 3% 11% 20% 12% 12% 12% 10% 5%

#invoice-products-table
    .list-group-item, .list-header
        grid-template-columns: 20px 30px 12% 13% 11% 12% 27% 3% 5% 7%

#dashboard-table
    .list-group-item, .list-header
        // grid-template-columns: 0% 15% 100px 30% 1fr 50px
        grid-template-columns: 0% 15% 100px 85px 110px 120px 1fr 4% 30px

#all-payments-table
    .list-group-item, .list-header
        grid-template-columns: 3% 17% 17% 48% 10%

#admin-notifications-table
    .list-group-item, .list-header
        grid-template-columns: 3% 85%

.invoice-items
    .list-header
        grid-template-columns: 3% 56.5% 12% 12% 12% 13%

.invoice-items
    .list-group-item
        grid-template-columns: 3% 33% 23% 9% 3% 9% 3.5% 9% 3.5% 7% 5%
