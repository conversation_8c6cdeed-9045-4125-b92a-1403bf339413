<?php

namespace App\Services\Admin\EmailTemplates;

use App\Models\Customer;
use App\Models\Invoice;
use Barryvdh\DomPDF\PDF;
use Illuminate\Pagination\LengthAwarePaginator;

interface IEmailTemplateService
{
    public function renderTemplateKeywords(string $templateBody, array $keywords): string;
    public function search(string $searchData, string $orderParam, string $orderType, string $type, int $perPage): LengthAwarePaginator;
}
