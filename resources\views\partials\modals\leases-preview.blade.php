<div class="modal fade" id="leasesPreviewModal{{$id}}" tabindex="-1" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content studiospopup">
            <div class="modal-header pt-5 pb-5 border-bottom">
                <h1 class="modal-title custom-fs-14px text-center fw-normal" id="">{{$customer->name}} - leases</h1>
                <button type="button" class="btn-close ms-0 position-absolute " style="right: 20px;"
                        data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-0 py-0 customer-modal-body">
                @if(count($customer['active_leases']) > 0 || count($customer['inactive_leases']) > 0)
                    <div class="container-fluid">
                        @if(count($customer['active_leases']) > 0)
                            <div class="active-leases-header row border-bottom border-light py-3 px-3">
                                <span class="text-secondary">{{count($customer['active_leases'])}} active {{count($customer['active_leases']) > 1 ? 'leases' : 'lease'}}</span>
                            </div>
                            <div class="active-leases row d-grid">
                                @foreach($customer['active_leases'] as $activeLease)
                                    <div class="active-leases-data studios-popup-data">
                                        <div class="lease-left d-flex flex-column">
                                            <span>{{$activeLease->studio->name}}</span>
                                            <span class="text-secondary">{{$activeLease->studio->zip}}, {{ucfirst($activeLease->machine->name)}} ({{$activeLease->machine_quantity}})</span>
                                        </div>

                                        <!-- Right side content -->
                                        <div class="lease-right d-flex flex-column text-end">
                                            @php
                                                $conversionRate = $activeLease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
                                                $leasePaymentsSum = \App\Helpers\CurrencyConversionHelper::calculateLeasePaymentsSum($activeLease, $conversionRate);
                                            @endphp
                                            <span class="text-success">{{$admin_currency_symbol}}{{number_format($leasePaymentsSum['paymentSum'], 2)}} left</span>
                                            <span class="text-secondary">{{$leasePaymentsSum['count']}}/{{$activeLease->duration}} months left</span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif

                        @if(count($customer['inactive_leases']) > 0)
                            <div class="active-leases-header row border-bottom border-light py-3 px-3">
                                <span class="text-secondary">{{count($customer['inactive_leases'])}} inactive {{count($customer['inactive_leases']) > 1 ? 'leases' : 'lease'}}</span>
                            </div>
                                @foreach($customer['inactive_leases'] as $inactiveLease)
                                    <div class="active-leases-data d-flex justify-content-between align-items-center border-bottom border-light py-5">
                                        <div class="lease-left d-grid px-3">
                                            <span>{{$inactiveLease->studio->name}}</span>
                                            <span class="text-secondary">{{$inactiveLease->studio->zip}}, {{ucfirst($inactiveLease->machine->name)}} ({{$inactiveLease->machine_quantity}})</span>
                                        </div>

                                        <div class="lease-right d-grid px-3 text-end">
                                            <span class="text-danger input-error">Lease expired</span>
                                            <span class="text-secondary">Date: </span>
                                        </div>
                                    </div>
                                @endforeach
                        @endif
                    </div>
                @else
                    <div class="empty-lease-text p-4 text-center">
                        Customer doesn't have any leases.
                    </div>
                @endif

            </div>
        </div>
    </div>
</div>
