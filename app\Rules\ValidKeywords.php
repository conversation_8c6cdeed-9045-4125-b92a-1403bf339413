<?php

namespace App\Rules;

use App\Models\EmailTemplate;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidKeywords implements ValidationRule
{
    private EmailTemplate $emailTemplate;

    public function __construct(EmailTemplate $emailTemplate)
    {
        $this->emailTemplate = $emailTemplate;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $availableKeywords = $this->emailTemplate->keywords;

        preg_match_all("/{{(.*?)}}/", $value, $matches);

        $requestKeywords = $matches[1];

        foreach ($requestKeywords as $requestKeyword) {
            if (!in_array($requestKeyword, $availableKeywords)) {
                $fail('Invalid keyword: ' . $requestKeyword);
            }
        }
    }
}
