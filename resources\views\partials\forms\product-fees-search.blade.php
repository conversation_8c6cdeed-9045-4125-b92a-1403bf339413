@if(count($fees) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id">
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="name" class="sortable-list-header {{ $orderParam == 'name' ? 'active' : '' }}" data-sort="name">{{ __('Name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="price" class="sortable-list-header {{ $orderParam == 'price' ? 'active' : '' }}" data-sort="price">{{ __('Price') }}
            <div class="sort-icon asc"></div>
        </div>
        <div class="justify-content-end"></div>
    </div>

    <form id="searchForm" method="post">
        @csrf
        <div class="multi-select-actions">
            <div class="py-5 ps-4 d-flex gap-5 border-bottom border-light text-info">
                <div class="form-check my-0">
                    <input class="form-check-input form-select-all" type="checkbox" value=""
                           id="flexCheckDefault_all">
                    <label class="form-check-label ps-2" for="flexCheckDefault_all">{{ __('Select All') }}</label>
                </div>
                <span class="form-select-none text-decoration-none">{{ __('Deselect') }}</span>

                {{--Delete selected with comfirmation modal--}}
                <span class="text-danger text-decoration-none" data-bs-toggle="modal"
                      href="#deleteSelectedModalToggle" role="button">{{ __('Delete') }}</span>
            </div>
        </div>

        {{--Selected items actions--}}
        <div class="modal fade delete-selected-modal" id="deleteSelectedModalToggle" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content delete-selected-modal-content">
                    <div class="modal-body p-7 text-center">
                        <h5 class="mb-1">{{ __('Are you sure you want to delete these notifications?') }}</h5>
                        <span class="text-info">{{ __('Note: Operation cannot be undone.') }}</span>
                        <div class="d-flex justify-content-center gap-3 mt-5">
                            <button type="submit"
                                    formaction="{{ route('admin.products.delete-multiple') }}"
                                    class="btn btn-primary">{{ __('Delete') }}</button>
                            <div class="btn btn-outline-light text-info"
                                 data-bs-dismiss="modal">{{ __('Cancel') }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">
            @foreach($fees as $fee)
                <div class="list-group-item list-group-item-action d-grid align-items-center py-4 px-4">
                    <div class="form-check mb-0">
                        <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]" value="{{ $fee->id }}" id="flexCheckDefault_{{$fee->id}}">
                        <label class="form-check-label d-none" for="flexCheckDefault_{{$fee->id}}"></label>
                    </div>
                    <a href="{{ route('admin.productfees.edit', $fee) }}" class="medium" style="text-decoration: none;">
                        <div class="my-0">{{$fee->name}}</div>
                    </a>
                    <p class="my-0">{{ $admin_currency_symbol }}{{ number_format($fee->price, 2) }}</p>
                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="licences-dropdown-content">
                            <a href="{{ route('admin.productfees.edit', $fee) }}">{{ __('Edit') }}</a>
                            <a href="" class="delete-product text-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{'fees'}}{{$fee->id}}">{{ __('Delete') }}</a>
                         </div>
                    </div>
                </div>
            @endforeach
            <div id="paginate paginate-products">
                @if($fees)
                    <div class="bottom">
                        {!! $fees->links() !!}
                    </div>
                @endif
            </div>
        </div>
    </form>
    @foreach($fees as $fee)
        @include('partials.modals.delete', [
            'type' => 'fees',
            'id' => $fee->id,
            'route' => route('admin.productfees.destroy', ['fee' => $fee]),
            'title' => $fee->name,
        ])
    @endforeach

@else
<p class="no-results-txt">There are no results.</p>
@endif

<script type="module">
    const productsCount = @json($fees).total;
    const descriptiveLabel = productsCount === 1 ? ' Item' : ' Items';
    $('#productsCount').text(productsCount + descriptiveLabel);

    // 'Select all' action
    $('.form-select-all').each(function(){
        let tableWrapper = $(this).closest('.entity-table');
        let selectAllElement = $(this);

        selectAllElement.change(function() {
            let checkboxes = tableWrapper.find('.list-group input[type="checkbox"]');
            if (this.checked) {
                checkboxes.prop('checked', true);
                tableWrapper.find('.multi-select-actions').slideDown();
            } else {
                checkboxes.prop('checked', false);
                $('#select_all_checkboxes').prop('checked', false);
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#select_all_checkboxes').on('click', function(){
        $('.form-select-all').trigger('click');
    });

    // 'Select none' action
    $('.form-select-none').click(function(){
        let tableWrapper = $(this).closest('.entity-table');

        tableWrapper.find('.list-group input[type="checkbox"]').prop('checked', false);
        tableWrapper.find('.form-select-all').prop('checked', false);

        tableWrapper.find('.multi-select-actions').slideUp();
    });

    // Expand multi-select section on checkbox click
    $('.entity-table .list-group .list-group-item input[type="checkbox"]').click(function(){
        let tableWrapper = $(this).closest('.entity-table');
        tableWrapper.find('.multi-select-actions').slideDown(300);

        tableWrapper.find('.list-group input[type="checkbox"]').change(function(){
            let allChecked = true;
            let allNonChecked = false;
            tableWrapper.find('.list-group input[type="checkbox"]').each(function () {
                allChecked &= $(this).prop('checked');
                allNonChecked ||= $(this).prop('checked');
            })
            tableWrapper.find('.form-select-all').prop('checked', allChecked);
            if (allNonChecked === false) {
                tableWrapper.find('.multi-select-actions').slideUp();
            }
        });
    });

    $('#deleteSelectedModalToggle').on('click', 'button[type="submit"]', function(event) {
        event.preventDefault();

        let selectedIds = [];
        $('input[name="selectedItems[]"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length > 0) {
            let form = $('<form>', {
                'method': 'POST',
                'action': '{{ route('admin.orders.delete-multiple') }}'
            });

            form.append('@csrf');

            selectedIds.forEach(function(id) {
                form.append($('<input>', {
                    'type': 'hidden',
                    'name': 'selectedItems[]',
                    'value': id
                }));
            });

            $('body').append(form);
            form.submit();
        }
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.dropbtn').on('click', function(event){
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    })

    $('.delete-product').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

</script>
