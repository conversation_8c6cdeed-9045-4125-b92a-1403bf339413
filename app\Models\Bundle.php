<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class Bundle extends Model
{
    use HasFactory, SoftDeletes;
    public $table = 'bundle';
    protected $fillable = [
        'title',
    ];

    protected $appends = [
        'price'
    ];

    public function getPriceAttribute(): string
    {
        $bundle_items = $this->items;
        $price = 0;
        foreach ($bundle_items as $item) {
            $price += $item->price * $item->quantity;
        }

        return number_format($price, 2, '.', '');
    }

    public function items(): HasMany
    {
        return $this->hasMany(BundleItem::class);
    }
}
