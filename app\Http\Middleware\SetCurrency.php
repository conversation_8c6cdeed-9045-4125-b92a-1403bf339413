<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AdminSettings;

class SetCurrency
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // if (Auth::check() && Auth::user()->hasRole('super-admin')) {
        if (Auth::check() && Auth::user()->hasAnyRole(explode('|', \App\Helpers\Constants::ROLES))) {
            // $currentCurrency = AdminSettings::first()->currency;
            // $settings = \auth()->user()->adminSettings;
            //  Asking - Customer own currency settings.
            $currency = AdminSettings::first()->currency;
            view()->share([
                'admin_currency_code' => $currency->code,
                'admin_currency_symbol' => $currency->symbol,
            ]);
        }

        return $next($request);
    }
}
