<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Session\TokenMismatchException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * The list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }
    // public function logout(){
    //     if ($exception instanceof \Illuminate\Session\TokenMismatchException) {
    //         return redirect()->route('login');
    //     }
    // }
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof TokenMismatchException) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['tokenMessage' => 'Your session has expired. Please try again.']);
        }

        return parent::render($request, $exception);
    }
}
