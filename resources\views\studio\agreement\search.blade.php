<style>
    #agreements-table .list-group-item,
    #agreements-table .list-header {
        grid-template-columns: 0px 0px 130px 130px 115px auto 135px 60px;
    }
</style>
@if (count($agreements) !== 0)
@php
// echo '<pre>';
// print_r($agreements->toArray());
// die();

@endphp

    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div>
            <div class="form-check mb-0">
                <input class="form-check-input" type="checkbox" id="select_all_checkboxes">
                <label class="form-check-label d-none" for="select_all_checkboxes"></label>
            </div>
        </div>
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="file_name" class="sortable-list-header" data-sort="file_name">{{ __('name') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="send_date" class="sortable-list-header" data-sort="location">{{ __('Location') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="singing_date" class="sortable-list-header" data-sort="type">{{ __('Type') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="completed_date" class="sortable-list-header" data-sort="created_at">{{ __('created') }}
            <div class="sort-icon asc"></div>
        </div>
        <div id="status" class="sortable-list-header" style="justify-content: flex-end;" data-sort="status">{{ __('Status') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            @foreach ($errors->all() as $error)
                <p>{{ $error }}</p>
            @endforeach
        </div>
    @endif
    <div class="list-group pb-2" id="results">
        @foreach ($agreements as $agreement)
            <div class="list-group-item list-group-item-action d-grid align-items-center">
                <div class="form-check mb-0">
                    <input class="form-check-input open-multi-actions" type="checkbox" name="selectedItems[]"
                        value="{{ $agreement->id }}" id="flexCheckDefault_{{ $agreement->id }}">
                    <label class="form-check-label d-none" for="flexCheckDefault_{{ $agreement->id }}"></label>
                </div>
                <p class="my-0 hide-transp">{{ $agreement->id }}</p>
                <div class="my-0 medium">{{ $agreement->name }}</div>
                <p class="my-0">{{ $agreement->studio_name }}</p>
                <p class="my-0" style="text-transform: capitalize">{{ $agreement->type }}</p>
                <p class="my-0">{{ \Carbon\Carbon::parse($agreement->created_at)->format('m/d/Y') }}</p>
                <div class="status-div" style="text-align: right;">
                    @include('partials.customer-agreement-status-badge', [
                        'status' => $agreement->status,
                        'style' => 'max-width: 135px',
                    ])
                </div>
                <div class="round-button-dropdown">
                    <button class="dropbtn">
                        <i class="fa fa-ellipsis-h"></i>
                    </button>
                    <div class="dropdown-content" id="agreements-dropdown-content">
                        {{-- <a href="javascript:void(0)">{{ __('Mark as Active') }} </a> --}}
                        @if ($agreement->status != '0' AND $agreement->status != '1' AND $agreement->status != '4')
                        <a href="javascript:void(0);" class="toggle-status" data-id="{{ $agreement->id }}"
                            data-status="{{ $agreement->status }}">
                            {{ $agreement->status == '3' ? 'Mark as Inactive' : 'Mark as Active' }}
                        </a>                        
                        @endif
                        {{-- @if ($agreement->status == '3' OR $agreement->status == '4') --}}
                        <a href="javascript:void(0);" class="download-agreement" data-id="{{ $agreement->id }}">
                            {{ __('Download') }}
                        </a>
                        {{-- @endif --}}
                        {{-- <a href="javascript:void(0)">{{ __('Download') }} </a> --}}
                        {{-- @if ($agreement->completed_date)
                            <a href="{{ route('admin.agreements.download', $agreement) }}">{{ __('Download') }} </a>
                        @else
                            <a href="javascript:void(0)" class="download-link disabled text-secondary"
                                style="pointer-events: none">{{ __('Download') }}</a>
                        @endif --}}
                        {{-- <a href="javascript:void(0)">{{ __('Send to customer') }} </a> --}}
                        <a href="javascript:void(0)" class="delete text-danger delete-agreement"
                            data-id="{{ $agreement->id }}">{{ __('Delete') }} </a>
                    </div>
                </div>
            </div>
        @endforeach
        <div id="paginate paginate-agreements">
            @if ($agreements)
                <div class="">
                    {!! $agreements->links() !!}
                </div>
            @endif
        </div>
    </div>
@else
    <p class="no-results-txt">There are no results.</p>    
@endif

<script type="module">
    const agreementsCount = @json($agreements).total;
    const descriptiveLabel = agreementsCount === 1 ? ' Item' : ' Items';

    $('#agreementsCount').text(agreementsCount + descriptiveLabel);

    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });

    $('.download-link').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
    })

    $('.toggle-status').click(function() {
        let button = $(this);
        let agreementId = button.data('id');
        let currentStatus = button.data('status');
        let newStatus = currentStatus == '3' ? 'Inactive' : 'Active';

        $.ajax({
            url: "{{ route('admin.customers.agreements.toggle') }}",
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                agreement_id: agreementId
            },
            success: function(response) {
                if (response.success) {
                    window.location.reload();
                }
            },
        });
    });
    $('.download-agreement').click(function(e) {
        e.preventDefault();
        let agreementId = $(this).data('id');

        $.ajax({
            url: "{{ route('admin.customer.agreements.download') }}",
            type: "POST",
            data: {
                _token: "{{ csrf_token() }}",
                agreement_id: agreementId
            },
            success: function(response) {
                if (response.success) {
                    let link = document.createElement('a');
                    link.href = response.file_url;
                    link.download = response.file_name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                } else {
                    alert(response.message);
                }
            },
            error: function(xhr) {
                alert('Error downloading file.');
            }
        });
    });
    $('.delete-agreement').click(function(e) {
        e.preventDefault();
        let agreementId = $(this).data('id');
        $.ajax({
            url: "{{ route('admin.customer.agreements.delete') }}",
            type: "DELETE",
            data: {
                _token: "{{ csrf_token() }}",
                agreement_id: agreementId
            },
            success: function(response) {
                window.location.reload();
            }
        });
    });
</script>
