<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\CurrencyConversionHelper;
use App\Http\Controllers\Controller;
use App\Models\AdminSettings;
use App\Models\Customer;
use App\Services\Admin\Dashboard\IDashboardService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use App\Helpers\Constants;
use App\Models\Payment;
use App\Models\Lease;
use App\Models\License;

class DashboardController extends Controller
{
    private IDashboardService $dashboardService;

    public function __construct(
        IDashboardService $dashboardService,
    ) {
        $this->dashboardService = $dashboardService;
    }

    public function index(Request $request)
    {
        $user = $request->user();
        switch ($this->getRoles($user->id)) {
            case 'super-admin':
                $currentCurrency = AdminSettings::first()->currency;
                $paymentsSum = $this->dashboardService->sumPayments($request->get('period', 'week'), $currentCurrency);
                $payments = $this->dashboardService->getLastTenPayments($currentCurrency);
                $customerCount = $this->dashboardService->countCustomer();
                $activeLicensesCount = $this->dashboardService->countActiveLicenses();
                $activeLeases = $this->dashboardService->countActiveLeases();
                return view('admin.dashboard.index', compact('payments', 'customerCount', 'activeLicensesCount', 'activeLeases', 'paymentsSum'));
            case 'b2c':
                echo 'This is a b2c role';
                break;
            case 'studio':
                return redirect()->route('studio.dashboard');
            case 'trainer':
                echo 'This is a trainer role';
                break;
            case 'master-trainer':
                echo 'This is a master-trainer role';
                break;
        }
    }

    public function getAllPayments(Request $request)
    {
        return view('admin.dashboard.index-all');
    }
    // public function newpass(Request $request)
    // {
    //     $newpass = Hash::make('Kat2025!!!');

    //     echo '<pre>';
    //     print_r($newpass);
    //     die();
    // }

    public function searchPayments(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'id';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $payments = $this->dashboardService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status
        );

        $currentCurrency = AdminSettings::first()->currency;

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage) + 1;
        }

        foreach ($payments as $payment) {
            $paymentType = $this->dashboardService->getPaymentType($payment);
            $payment['type'] = $paymentType['type'];
            $payment['name'] = $paymentType['name'];
            $payment['sequential_id'] = $sequential_id++;
            $conversionRate = ($payment->license_id)
                ? $payment->license->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate
                : $payment->lease->conversionRate()->where('currency_id', $currentCurrency->id)->first()->rate;
            $payment['payment_amount'] = CurrencyConversionHelper::calculatePaymentAmount($payment, $conversionRate);
        }

        $viewContent = view('partials.forms.dashboard.dashboard-search', compact('payments'))->render();

        return response()->json($viewContent);
    }

    public function getRoles($userId)
    {
        $roles = DB::table('model_has_roles')
            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
            ->where('model_has_roles.model_id', $userId)
            ->pluck('roles.name');
        return $roles[0];
    }
}
