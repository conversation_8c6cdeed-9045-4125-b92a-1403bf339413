<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = User::whereHas('roles', function ($query) {
            $query->where('name', 'super-admin');
        })->first();

        $admin->adminSettings()->create([
            'timezone' => 'America/Los_Angeles',
            'date_format' => 'F j, Y',
            'time_format' => 'g:i A',
            'week_start' => 'sunday',
            'currency_id' => 1,
            'smtp_from_name' => config('mail.from.name'),
            'smtp_from_email' => config('mail.from.address'),
        ]);
    }
}
