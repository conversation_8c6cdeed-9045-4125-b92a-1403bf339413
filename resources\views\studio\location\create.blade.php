@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('New Location') }}</h3>
            <a href="{{ route('studio.location', $customer) }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('studio.location.store', $customer) }}" id="studio-location-form">
            @csrf
            <h5 class="form-section-title first-title">{{ __('Location company') }}</h5>

            @include('partials.forms.select', [
                'field_name' => 'company_id',
                'field_label' => null,
                'values' => $companies,
                'field' => 'company',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('company_id', null),
                'include_empty' => true,
            ])

            <h5 class="form-section-title">{{ __('lincese type') }}</h5>

            @include('partials.forms.radio-group', [
                'field_name' => 'type',
                'field_class' => 'mb-7',
                'field_label' => 'Radio group location',
                'values' => [
                    'license' => ['text' => 'License', 'value' => 'license'],
                    'exclusivity' => ['text' => 'Exclusive <img src="/exclusivity-icon.svg" alt="Exclusive Badge" style="height:1em;vertical-align:middle;margin-left:4px;">', 'value' => 'exclusivity'],
                ],
                'field' => 'status',
                'field_value' => old('type', null),
                'required' => 'required',
                'checked' => 'license',
            ])
            <div class="studio_info_wrap">
                <h5 class="form-section-title">{{ __('studio info') }}</h5>
                @include('partials.forms.input', [
                    'field_name' => 'studio[name]',
                    'field_label' => 'LOCATION NAME *',
                    'field_type' => 'text',
                    'field_value' => old('studio[name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_first_name]',
                    'field_label' => 'FIRST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_first_name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[owner_last_name]',
                    'field_label' => 'LAST NAME (LICENSEE) *',
                    'field_type' => 'text',
                    'field_value' => old('studio[owner_last_name]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[phone]',
                    'field_label' => 'PHONE # *',
                    'field_type' => 'text',
                    'field_value' => old('studio[phone]', null),
                    'field_class' => 'studio-input',
                ])
                @include('partials.forms.input', [
                    'field_name' => 'studio[email]',
                    'field_label' => 'EMAIL ADDRESS *',
                    'field_type' => 'email',
                    'field_value' => old('studio[email]', null),
                    'field_class' => 'studio-input',
                ])

                <h5 class="form-section-title">{{ __('address info') }}</h5>

                @include('partials.forms.radio-group', [
                    'field_name' => 'location',
                    'field_class' => 'mb-7',
                    'field_label' => 'Radio group location',
                    'values' => [
                        'location_usa' => ['text' => 'USA', 'value' => 'USA'],
                        'location_international' => ['text' => 'International', 'value' => 'International'],
                    ],
                    'field' => 'status',
                    'field_value' => old('location', null),
                    'required' => 'required',
                    'checked' => 'USA',
                ])

                @include('partials.forms.input', [
                    'field_name' => 'studio[address]',
                    'field_label' => 'ADDRESS',
                    'field_type' => 'text',
                    'field_value' => old('studio[address]', null),
                    'field_class' => 'studio-input',
                ])

                @include('partials.forms.input', [
                    'field_name' => 'studio[city]',
                    'field_label' => 'CITY',
                    'field_type' => 'text',
                    'field_value' => old('studio[city]', null),
                    'field_class' => 'studio-input',
                ])

                @include('partials.forms.input', [
                    'field_name' => 'studio[zip]',
                    'field_label' => 'ZIP CODE',
                    'field_type' => 'text',
                    'field_value' => old('studio[zip]', null),
                    'field_class' => 'studio-input',
                ])

                @include('partials.forms.select', [
                    'field_name' => 'studio[state_id]',
                    'field_label' => 'STATE',
                    'values' => $states_countries['states'],
                    'field' => 'state',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[state_id]', null),
                    'include_empty' => true,
                ])

                @include('partials.forms.select', [
                    'field_name' => 'studio[country_id]',
                    'field_label' => 'COUNTRY',
                    'values' => $states_countries['countries'],
                    'field' => 'countries',
                    'option_key' => 'id',
                    'option_label' => 'name',
                    'field_value' => old('studio[country_id]', null),
                    'include_empty' => true,
                ])
            </div>

            <h5 class="fs-14px mt-50 mb-45 pt-45 form-section-title custom-fw-500 text-uppercase border-top">
                {{ __('license Info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'starting_date',
                'field_label' => 'AGREEMENT DATE *',
                'placeholder' => 'Enter',
                'field_type' => 'date',
                'field_plugin' => 'date-picker',
                'field_value' => old('starting_date', null),
                'field_right_mark' => '<img src="/calendar.svg" class="calendar" />',
                'input_field_class' => 'date-field',
            ])

            @include('partials.forms.select', [
                'field_name' => 'package',
                'field_label' => 'PACKAGE TYPE *',
                'placeholder' => 'Select',
                'values' => \App\Helpers\Constants::LICENSE_PACKAGES,
                'field' => 'package',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('package', null),
                'include_empty' => true,
            ])

            @include('partials.forms.input', [
                'field_name' => 'price',
                'field_type' => 'hidden',
                'field_value' => 0,
            ])

            @include('partials.forms.input', [
                'field_name' => 'duration',
                'field_type' => 'hidden',
                'field_value' => 1,
            ])

            {{-- @include('partials.forms.input', [
                'field_name' => 'type',
                'field_type' => 'hidden',
                'field_value' => 'license',
            ]) --}}

            @include('partials.forms.input', [
                'field_name' => 'deposit_date',
                'field_type' => 'hidden',
                'field_value' => date('Y-m-d'),
            ])

            <div class="deposit-wrap">
                @include('partials.forms.input', [
                    'field_name' => 'deposit_amount',
                    'field_label' => 'DEPOSIT AMOUNT *',
                    'field_type' => 'text',
                    'currency' => true,
                    'currentCurrency' => $currentCurrency->symbol ?? "$",
                    'field_value' => old('deposit_amount', 0),
                    'input_field_class' => 'decimal-field',
                ])
                <p class="f-10" style="display: none; color: red; position: relative; top: -20px">Deposit is not available
                    for international locations</p>
            </div>

            @include('partials.forms.textarea', [
                'field_name' => 'description',
                'field_label' => 'INTERNAL NOTE',
                'field_type' => 'text',
                'placeholder' => 'Enter (optional)',
                'field_value' => old('description', null),
            ])

            <div
                class="d-flex justify-content-start align-items-stretch align-items-sm-center border-top pt-7 mt-50 flex-column flex-md-row">
                <button type="submit" class="btn btn-primary">{{ __('PUBLISH') }}</button>
                <a type="button" href="{{ route('admin.licenses.create', $customer) }}"
                    class="btn cancel-btn">{{ __('CANCEL') }}</a>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script type="module">
        const package_prices = @json(\App\Helpers\Constants::LICENSE_PACKAGES_PRICES);

        document.addEventListener('DOMContentLoaded', function() {
            function toggleLocationInputs() {
                const isUSA = document.getElementById('location_usa').checked;
                const stateSelect = document.querySelector("[name='studio[state_id]']");
                const countrySelect = document.querySelector("[name='studio[country_id]']");

                if (isUSA) {
                    stateSelect.closest('.form-group').style.display = 'block';
                    countrySelect.closest('.form-group').style.display = 'none';
                    $('.deposit-wrap').removeClass('disabled');
                    if ($('#package-package').val() != 0) {
                        $('#deposit_amount').val(490);
                    } else {
                        $('#deposit_amount').val(0);
                    }
                } else {
                    stateSelect.closest('.form-group').style.display = 'none';
                    countrySelect.closest('.form-group').style.display = 'block';
                    $('.deposit-wrap').addClass('disabled');
                    $('#deposit_amount').val(0);
                }
            }

            document.getElementById('location_usa').addEventListener('change', toggleLocationInputs);
            document.getElementById('location_international').addEventListener('change', toggleLocationInputs);

            let inputField = document.querySelector('.is-invalid');
            if (inputField) {
                inputField.focus();
            }

            $('form').on('submit', function() {
                $('input[name="location"]').prop('disabled', false);
            });

            $('#package-package').change(function() {
                var val = $(this).val();
                console.log('val: ', val);

                $('#price').val(package_prices[val] || 0);
                toggleLocationInputs();
            });

            toggleLocationInputs();
        });
    </script>
@endpush