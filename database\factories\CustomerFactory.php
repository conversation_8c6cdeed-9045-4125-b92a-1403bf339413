<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Customer;
use App\Models\Lease;
use App\Models\License;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $user = User::factory()->create();
        return [
            'name' => fake()->name,
            'location' => array_rand(['USA', 'International']),
            'owner_id' => $user->id,
        ];
    }

    public function configure()
    {
        return $this->afterCreating(function ($customer) {
            License::factory()->count(100)->create(['customer_id' => $customer->id]);
            Lease::factory()->count(100)->create(['customer_id' => $customer->id]);
        });
    }
}
