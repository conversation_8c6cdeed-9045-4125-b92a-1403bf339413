@extends('layouts.app')

@section('content')
    <div class="page-title">
        <div class="title-left">
            <h3>{{ __('edit licenseSettings') }}</h3>
            <a href="{{ route('admin.licenseSettings.index') }}" class="back-link">← Back</a>
        </div>
    </div>

    <div class="mb-6">
        <form method="POST" action="{{ route('admin.licenseSettings.update', $licenseSettings) }}" id="studio-location-form" enctype="multipart/form-data">
            @csrf

            <h5 class="form-section-title">{{ __('License Info') }}</h5>

            @include('partials.forms.input', [
                'field_name' => 'name',
                'field_label' => 'NAME *',
                'field_type' => 'text',
                'field_value' => old('name', NULL),
            ])

            @include('partials.forms.select', [
                'field_name' => 'package_id',
                'field_label' => 'MACHINE *',
                'values' => \App\Helpers\Constants::LICENSE_PACKAGES_KEYS,
                'field' => 'package_id',
                'option_key' => 'id',
                'option_label' => 'name',
                'field_value' => old('package_id', NULL),
                'include_empty' => true,
            ])      

            @include('partials.forms.input', [
               'field_name' => 'price',
               'field_label' => 'PRICE *',
               'field_type' => 'text',
               'currency' => true,
               'field_value' => old('price', NULL),
               'input_field_class' => 'decimal-field',
            ])

            @include('partials.forms.input', [
                'field_name' => 'deposit',
                'field_label' => 'DEPOSIT *',
                'field_type' => 'text',
                'currency' => true,
                'field_value' => old('deposit', NULL),
                'input_field_class' => 'decimal-field',
            ])

            <div class="buttons-wrapper">
                <button type="submit" class="btn btn-primary">{{ __('UPDATE') }}</button>
                <a type="button" href="{{route('admin.licenseSettings.edit', $licenseSettings)}}" class="btn cancel-btn">{{ __('CANCEL') }}</a>
                <button type="button" class="btn delete-btn" id="delete-button"
                        data-bs-toggle="modal" data-bs-target="#deleteModal{{'LicenseSettings'}}{{$licenseSettings->id}}"
                        data-bs-whatever="">{{ __('Delete') }}
                </button>
            </div>
        </form>
    </div>

@endsection
@include('partials.modals.delete', [
        'type' => 'LicenseSettings',
        'id' => $licenseSettings->id,
        'route' => route('admin.licenseSettings.destroy', ['licenseSettings' => $licenseSettings]),
        'title' => $licenseSettings->name,
    ])

<script>
    document.addEventListener('DOMContentLoaded', function () {
        let inputField = document.querySelector('.is-invalid');
        if (inputField) {
            inputField.focus();
        }
        document.getElementById('location_international').addEventListener('click', function () {
            console.log('change');
            document.getElementById('location_usa_container').style.display = 'none';
            document.getElementById('location_international_container').style.display = 'block';
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        });        
        document.getElementById('location_usa').addEventListener('click', function () {
            document.getElementById('location_usa_container').style.display = 'block';
            document.getElementById('location_international_container').style.display = 'none';
            $('select').select2({
                minimumResultsForSearch: 10,
                placeholder: "Select",
            });
        });
    });
</script>
