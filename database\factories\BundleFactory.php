<?php

namespace Database\Factories;

use App\Models\Address;
use App\Models\Bundle;
use App\Models\BundleItem;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Company>
 */
class BundleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->name,
        ];
    }
}
