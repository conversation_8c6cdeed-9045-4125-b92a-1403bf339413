<?php

namespace App\Http\Controllers\Admin;

use App\Enum\InvoiceProduct\Status;
use App\Helpers\InvoiceHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\InvoiceProduct\StoreInvoiceProductRequest;
use App\Http\Requests\Admin\InvoiceProduct\UpdateInvoiceProductRequest;
use App\Mail\SendInvoiceProduct;
use App\Models\Bundle;
use App\Models\Company;
use App\Models\Customer;
use App\Models\InvoiceProduct;
use App\Models\InvoiceTemplate;
use App\Models\Products;
use App\Services\Admin\InvoiceProduct\IInvoiceProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Illuminate\View\View;
use App\Models\User;
use App\Models\AdminNotification as AdminNotificationModel;
use App\Notifications\AdminNotification;

use URL;

class InvoiceProductController extends Controller
{
    private IInvoiceProductService $invoiceProductService;

    public function __construct(IInvoiceProductService $invoiceProductService)
    {
        $this->invoiceProductService = $invoiceProductService;
    }

    public function index(): View
    {
        return view('admin.invoice-products.index');
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';

        $invoices = $this->invoiceProductService->search($searchData, $orderParam, $orderType, $perPage, $status, '');

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = ($page - 1) * $perPage + 1;
        }

        foreach ($invoices as $invoiceProduct) {
            $invoiceProduct['sequential_id'] = $sequential_id++;
            $invoiceProduct['orderParam'] = $orderParam;
            $invoiceProduct['orderType'] = $orderType;
        }
        $viewContent = view('partials.forms.invoice-products.invoice-products-search', compact('invoices'))->render();

        return response()->json($viewContent);
    }

    public function searchCustomerInvoices(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';
        $customer = $request->get('customerId') ?? '';

        // echo '<pre>';
        // print_r($customer->toArray());
        // die();

        $invoices = $this->invoiceProductService->search($searchData, $orderParam, $orderType, $perPage, $status, $customer);

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = ($page - 1) * $perPage + 1;
        }

        foreach ($invoices as $invoiceProduct) {
            $invoiceProduct['sequential_id'] = $sequential_id++;
            $invoiceProduct['orderParam'] = $orderParam;
            $invoiceProduct['orderType'] = $orderType;
            $product_items = $invoiceProduct->load('items.product', 'customer');
            $invoiceProduct['amount'] = 0;
            $subTotal = 0;
            $totalDiscount = 0;

            if (count($product_items->items) > 0) {
                foreach ($product_items->items as $key => $item) {
                    $discount_val = 0;
                    if ($item->discount_type == "$") {
                        $discount_val = $item->discount;
                    } else {
                        $discount_val = $item->price * $item->quantity * ($item->discount / 100);
                    }

                    $subTotal += $item->price * $item->quantity;
                    $totalDiscount += $discount_val;
                }
            }

            $tax = $invoiceProduct->show_tax ? ($subTotal - $totalDiscount) * 0.095 : 0;
            $tax = $tax < 0 ? 0 : $tax; // Ensure tax is not negative

            $invoiceProduct['amount'] = $subTotal - $totalDiscount + $tax + $invoiceProduct->shipping_fee + $invoiceProduct->handling_fee;
        }

        $viewContent = view('partials.forms.invoice-products.invoice-products-search-tab', compact('invoices'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $companies = Company::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $customers = Customer::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $templates = InvoiceTemplate::orderBy('title')
            ->get()
            ->sortBy('title', SORT_NATURAL | SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);
        $bundles = Bundle::all();
        $new_products = [];

        foreach ($products as $product) {
            // if($product->category == 'bundle'){
            //     $new_products['bundle'][] = $product;
            // }else
            if ($product->category == 'fee') {
                $new_products['fee'][] = $product;
            } elseif ($product->category == 'product') {
                $new_products['products'][] = $product;
            }
        }

        foreach ($bundles as $key => $value) {
            $value['name'] = $value['title'];
            $new_products['bundle'][] = $value;
        }
        return view('admin.invoice-products.create', compact('companies', 'customers', 'new_products', 'products', 'templates'));
    }

    public function store(StoreInvoiceProductRequest $request): RedirectResponse
    {
        // dd($request->all()); // Dumps and stops execution
        try {
            $this->invoiceProductService->store($request->validated());
            toastr()->addSuccess('', 'Invoice created successfully.');
            return redirect()->route('admin.invoice-products.index');
        } catch (\Exception $e) {
            toastr()->addError('Invoice creation failed');
            return redirect()->back();
        }
    }

    public function edit(InvoiceProduct $invoiceProduct): View
    {
        $companies = Company::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $customers = Customer::orderBy('name')
            ->get()
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
        $templates = InvoiceTemplate::orderBy('title')
            ->get()
            ->sortBy('title', SORT_NATURAL | SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);
        $bundles = Bundle::all();

        $new_products = [];

        foreach ($products as $product) {
            if ($product->category == 'fee') {
                $new_products['fee'][] = $product;
            } elseif ($product->category == 'product') {
                $new_products['products'][] = $product;
            }
        }

        foreach ($bundles as $key => $value) {
            $value['name'] = $value['title'];
            $new_products['bundle'][] = $value;
        }

        $invoice = $invoiceProduct->load('items.product', 'customer');
        return view('admin.invoice-products.edit', compact('companies', 'customers', 'new_products', 'products', 'invoice', 'templates'));
    }

    public function update(InvoiceProduct $invoiceProduct, UpdateInvoiceProductRequest $request): RedirectResponse
    {
        try {
            $this->invoiceProductService->update($request->validated(), $invoiceProduct);
            toastr()->addSuccess('', 'Invoice updated successfully.');
            return redirect()->route('admin.invoice-products.edit', $invoiceProduct);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Invoice update failed');
            return redirect()->back();
        }
    }

    public function destroy(InvoiceProduct $invoiceProduct): RedirectResponse
    {
        try {
            $this->invoiceProductService->delete($invoiceProduct);
            toastr()->addSuccess('', 'Invoice deleted successfully.');
            return redirect(route('admin.invoice-products.index'));
        } catch (\Exception $e) {
            toastr()->addError('Invoice delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request): RedirectResponse
    {
        try {
            $invoiceProductIds = $request->input('selectedItems');
            foreach ($invoiceProductIds as $invoiceProductId) {
                $invoiceProduct = InvoiceProduct::find($invoiceProductId);
                $this->invoiceProductService->delete($invoiceProduct);
            }
            toastr()->addSuccess('', 'Selected invoices deleted successfully.');
            return redirect(route('admin.invoice-products.index'));
        } catch (\Exception $e) {
            toastr()->addError('Invoice delete failed');
            return redirect()->back();
        }
    }

    public function mark_as_paid(InvoiceProduct $invoiceProduct): JsonResponse
    {
        $new_paid_status = $invoiceProduct->paid == 1 ? 0 : 1;
        $invoiceProduct->update(['paid' => $new_paid_status]);
        // toastr()->addSuccess('', 'Invoice #' . $invoiceProduct->formatted_number . ' marked as ' . ($invoiceProduct->paid == 1 ? 'not paid' : 'paid') . ' successfully.');

        admin_notify([
            'title' => 'Invoice marked as ' . ($new_paid_status == 1 ? 'paid' : 'unpaid'), 
            'description' => 'Invoice #' . $invoiceProduct->formatted_number . ' is marked as ' . ($new_paid_status == 1 ? 'paid' : 'unpaid'),
            'link' => '',
            'type' => 'regular'
        ]);

        toastr()->addSuccess('', 'Invoice marked as ' . ($new_paid_status == 1 ? 'paid' : 'unpaid'));
        return response()->json(['success' => $new_paid_status]);
    }

    public function get_bundle(Request $request): JsonResponse
    {
        $bundle = Bundle::find($request->bundle_id);

        return response()->json(['success' => true, 'bundle' => $bundle]);
    }

    public function downloadInvoice(InvoiceProduct $invoiceProduct): Response
    {
        $pdf = InvoiceHelper::generateInvoiceProductPDF($invoiceProduct);
        return $pdf->download('Invoice-' . $invoiceProduct->formatted_number . '.pdf');
    }

    public function viewInvoice(InvoiceProduct $invoiceProduct): Response
    {
        $ddd = $invoiceProduct;
        echo view('pdfs.invoice-product', compact('invoiceProduct'));
    }

    public function emailInvoice(InvoiceProduct $invoiceProduct)
    {
        try {
            $pdf = InvoiceHelper::generateInvoiceProductPDF($invoiceProduct);
            Mail::to($invoiceProduct->customer->owner->email)->send(new SendInvoiceProduct($invoiceProduct, $pdf));
            if ($invoiceProduct->customer->owner->email2 !== null) {
                Mail::to($invoiceProduct->customer->owner->email2)->send(new SendInvoiceProduct($invoiceProduct, $pdf));
            }
            toastr()->addSuccess('', 'Invoice emailed successfully.');
            $invoiceProduct->update(['sent_date' => now(), 'status' => Status::SENT]);
            return redirect()->back();
        } catch (\Exception $exception) {
            toastr()->addError('Invoice email failed');
            return redirect()->back();
        }
    }

    public function emailInvoiceReminder(InvoiceProduct $invoiceProduct)
    {
        try {
            $pdf = InvoiceHelper::generateInvoiceProductPDF($invoiceProduct);

            $invoiceProduct['reminder_date_tmp'] = 'IMA';

            Mail::to($invoiceProduct->customer->owner->email)->send(new SendInvoiceProduct($invoiceProduct, $pdf));
            if ($invoiceProduct->customer->owner->email2 !== null) {
                Mail::to($invoiceProduct->customer->owner->email2)->send(new SendInvoiceProduct($invoiceProduct, $pdf));
            }
            toastr()->addSuccess('', 'Invoice reminder emailed successfully.');
            unset($invoiceProduct['reminder_date_tmp']);
            $invoiceProduct->update(['reminder_date' => now()]);
            return redirect()->back();
        } catch (\Exception $exception) {
            dd($exception);
            toastr()->addError('Invoice reminder email failed');
            return redirect()->back();
        }
    }
}
