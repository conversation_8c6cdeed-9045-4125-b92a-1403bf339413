<?php

namespace App\Http\Requests\Admin\License;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateStudioLicenseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'studio.email'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            'studio.phone'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
        ];
    }

    public function attributes()
    {
        return [
            'studio.email' => 'email',
            'studio.phone' => 'phone',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()) {
            toastr()->addError('Please check all the fields');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
