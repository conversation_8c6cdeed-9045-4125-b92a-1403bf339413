<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\OrderHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Order\StoreOrderRequest;
use App\Http\Requests\Admin\Order\UpdateOrderRequest;
use App\Mail\ContactCustomer;
use App\Models\Company;
use App\Models\State;
use App\Models\Order;
use App\Models\Supplier;
use App\Models\InvoiceProduct;
use App\Models\Products;
use App\Services\Admin\Order\IOrderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\View\View;

class OrderController extends Controller
{
    private IOrderService $orderService;

    public function __construct(
        IOrderService $orderService,
    )
    {
        $this->orderService = $orderService;
    }

    public function index(): View
    {
        return view('admin.orders.index');
    }

    public function show(Request $request, Order $order): View
    {
        $tab = $request->query('tab', 'profile');

        return view('admin.orders.show', compact('order', 'tab'));
    }

    public function search(Request $request): JsonResponse
    {
        $page = $request->get('page') ?? 1;
        $searchData = $request->get('search_data') ?? '';
        $perPage = $request->get('per_page') ?? 10;
        $orderParam = $request->get('order_param') ?? 'created_at';
        $orderType = $request->get('order_type') ?? 'desc';
        $status = $request->get('status') ?? '';
        $supplierId = $request->get('supplier_id') ?? null;

        $orders = $this->orderService->search(
            $searchData,
            $orderParam,
            $orderType,
            $perPage,
            $status,
            $supplierId
        );

        if (!isset($page) || $page == 1) {
            $sequential_id = 1;
        } else {
            $sequential_id = (($page - 1) * $perPage)+1;
        }

        foreach ($orders as $order) {
            $order['sequential_id'] = $sequential_id++;
            $order['orderParam'] = $orderParam;
            $order['orderType'] = $orderType;
        }

        $viewContent = view('partials.forms.orders.orders-search', compact('orders'))->render();

        return response()->json($viewContent);
    }

    public function create(): View
    {
        $companies = Company::orderBy('name')->get()->sortBy('name', SORT_NATURAL|SORT_FLAG_CASE);        
        $suppliers = Supplier::orderBy('name')->get()->sortBy('name', SORT_NATURAL|SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0, 'category' => 'product'])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);

        return view('admin.orders.create', compact('companies', 'suppliers', 'products'));
    }

    public function store(StoreOrderRequest $request)
    {
        try {
            $this->orderService->store($request->validated());
            // dd($request->validated());
            toastr()->addSuccess('', 'Order created successfully.');
            return redirect()->route('admin.orders.index');
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Order creation failed');
            return redirect()->back();
        }
    }

    public function edit(Order $order): View
    {
        $companies = Company::orderBy('name')->get()->sortBy('name', SORT_NATURAL|SORT_FLAG_CASE);        
        $suppliers = Supplier::orderBy('name')->get()->sortBy('name', SORT_NATURAL|SORT_FLAG_CASE);
        $products = Products::where(['custom_product' => 0, 'category' => 'product'])->orderBy('name')->get()->sortBy("name", SORT_NATURAL|SORT_FLAG_CASE);

        $single_order = $order->load('items.product');

        // echo '<pre>';
        // print_r($order_items->toArray());
        // die();
        
        return view('admin.orders.edit', compact('single_order', 'companies', 'suppliers', 'products'));
    }

    public function update(Order $order, UpdateOrderRequest $request)
    {
        try {
            $this->orderService->update($request->validated(), $order);
            toastr()->addSuccess('', 'Order updated successfully.');
            return redirect()->route('admin.orders.edit', $order);
        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Order update failed');
            return redirect()->back();
        }
    }

    public function destroy(Order $order)
    {
        try {
            $this->orderService->delete($order);
            toastr()->addSuccess('', 'Order deleted successfully.');
            return redirect(route('admin.orders.index'));
        } catch (\Exception $e) {
            toastr()->addError('Order delete failed');
            return redirect()->back();
        }
    }

    public function deleteMultiple(Request $request)
    {
        try {
            $orderIds = $request->input('selectedItems');
            $this->orderService->deleteMultiple($orderIds);

            toastr()->addSuccess('', 'Selected orders deleted successfully.');

            return redirect(route('admin.orders.index'));
        } catch (\Exception $e) {
            toastr()->addError('Order delete failed');
            return redirect()->back();
        }
    }

    public function download(Order $order)
    {
        $pdf = OrderHelper::generatePDF($order);
        return $pdf->download('order-' . $order->formatted_order_number  . '.pdf');
    }

    public function sendToSupplier(Order $order)
    {
        try {
            $this->orderService->sendToSupplier($order);
            toastr()->addSuccess('', 'Order sent successfully.');
            return redirect()->back();

        } catch (\Exception $e) {
            dd($e);
            toastr()->addError('Send order failed');
            return redirect()->back();
        }
    }
}
