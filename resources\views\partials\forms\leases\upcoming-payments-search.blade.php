@if(count($payments) !== 0)
    <div class="list-header d-grid border-bottom border-light text-info text-uppercase fw-normal">
        <div id="id" class="sortable-list-header" data-sort="id">{{ __('Id') }}
            <div class="sort-icon asc"></div>
        </div>
        <div>{{ __('Name') }}</div>
        <div>{{ __('Machine') }}</div>
        <div>{{ __('Payment') }}</div>
        <div>{{ __('Amount') }}</div>
        <div id="payment_date" class="sortable-list-header" data-sort="payment_date">{{ __('Date') }}
            <div class="sort-icon asc"></div>
        </div>
    </div>

    <form id="searchForm" method="post">
        @csrf

        @if($errors->any())
            <div class="alert alert-danger">
                @foreach($errors->all() as $error)
                    <p>{{ $error }}</p>
                @endforeach
            </div>
        @endif
        <div class="list-group pb-2" id="results">

            @foreach($payments as $payment)

                <div class="list-group-item list-group-item-action d-grid align-items-center">
                    <p class="my-0 hide-transp">{{$payment->sequential_id}}</p>
                    <p class="my-0">{{$lease->studio->name}}</p>
                    <p class="my-0 text-secondary">{{ucfirst($lease->machine->name)}}</p>
                    <p class="my-0">{{$payment->payment_number}} of {{$lease->duration}}</p>
                    <p class="my-0 text-success">{{$admin_currency_symbol}}{{number_format($payment->payment_amount, 2)}}</p>
                    <p class="my-0">{{\Carbon\Carbon::parse($payment->payment_date)->format('m/d/Y')}}</p>

                    @if ($payment->invoice)
                    <div class="round-button-dropdown">
                        <button class="dropbtn">
                            <i class="fa fa-ellipsis-h"></i>
                        </button>
                        <div class="dropdown-content" id="upcoming-payments-dropdown-content">
                            <a href="{{route('admin.download-invoice', ['customer' => $customer, 'invoice' => $payment->invoice])}}">{{ __('Download Invoice') }}</a>
                            <a href=""  class="email-invoice" data-bs-toggle="modal" data-bs-target="#emailInvoice{{$payment->invoice->id}}">{{ __('Email Invoice') }}</a>
                            <a href=""  class="select-status" data-bs-toggle="modal" data-bs-target="#selectStatus{{$payment->id}}">{{ __('Change Status') }}</a>
                        </div>
                    </div>
                    @endif
                </div>

                @if ($payment->invoice)
                    @include('partials.modals.select-status', [
                        'id' => $payment->id,
                        'route' => route('admin.payments.change-status', ['payment' => $payment]),
                        'payment' => $payment
                    ])

                    @include('partials.modals.email-invoice', [
                        'id' => $payment->invoice->id,
                        'route' => route('admin.email-invoice', ['customer' => $customer, 'invoice' => $payment->invoice]),
                        'invoice' => $payment->invoice
                    ])
                @endif
            @endforeach
            <div id="paginate paginate-upcoming-payments">
                @if($payments)
                    <div class="">
                        {!! $payments->links() !!}
                    </div>
                @endif
            </div>
        </div>
    </form>
@else
<p class="no-results-txt">There are no results.</p>{{--  --}}
@endif

<script type="module">
    $('.dropbtn').on('click', function(event) {
        event.preventDefault();
        event.stopPropagation();
        var dropdownContent = $(this).siblings('.dropdown-content');
        $('.dropdown-content').removeClass('show');
        dropdownContent.addClass('show');
    });

    $('html, body').on('click', function() {
        $('.dropdown-content').removeClass('show');
    });
</script>
