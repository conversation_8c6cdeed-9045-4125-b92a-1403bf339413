{{-- @if(isset($stickyNotification) AND $stickyNotification->count() AND \auth()->user() != NULL) --}}
<div class="offcanvas offcanvas-end px-3 px-sm-5 py-3 py-sm-6 overflow-auto" tabindex="-1" id="offcanvasExample"
    aria-labelledby="offcanvasExampleLabel">
    <div class="d-flex flex-column flex-sm-row align-items-start align-items-sm-center m-n3 m-sm-0 px-3 px-sm-0 py-5 py-sm-0 border-bottom border-sm-bottom-0 position-relative">
        <div class="d-flex align-items-center">
            <h5 class="mb-0 text-uppercase fw-semibold custom-fs-14px">{{ __('NOTIFICATIONS') }}</h5>
            <div id="stickyNotificationCountContainer" class="p-2 ms-1 badge bg-danger rounded-circle">
                <span class="position-absolute translate-middle fw-semibold">{{ @count($stickyNotification) }}</span>
            </div>
        </div>
        <button id="markAllAsRead" type="button" class="ms-0 ms-sm-auto mt-2 mt-sm-0 px-0 px-sm-1 border-0 text-info bg-transparent ls-0.6px">{{ __('Mark all as read') }}</button>
        <button type="button" class="btn-close d-block d-sm-none position-absolute top-50 mt-n2 end-0 me-4" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="notification-wrap mt-6 d-grid gap-3">
        @forelse($stickyNotification as $notification)
            <div class="notification-system notification-system-read border" id="notification_{{$notification->id}}" style="border-radius: 8px">
                <div class="read-notification" data-bs-toggle="offcanvas" data-bs-target="#offcanvasExample" aria-controls="offcanvasExample"
                    data-href="@if (isset($notification->data['link']) AND $notification->data['link'] != '1' AND $notification->data['link'] != '') {{ $notification->data['link'] }} @else {{ "#" }} @endif" 
                    @if(isset($notification->data['type']) AND $notification->data['type'] == 'external') target="_blank" rel="noreferrer" @endif 
                    @if(isset($notification->data['type']) AND $notification->data['type'] == 'popup') data-popup="iframe-popup" @endif
                    data-notification_id="{{ $notification->id }}">
                    <div class="px-4 py-3 d-flex align-items-center">
                        <div class="me-2 p-3 badge bg-light rounded-circle">
                            <span class="position-absolute translate-middle">
                                <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                    <g id="Group_16391" data-name="Group 16391" transform="translate(-20 -15)">
                                        <g id="Group_12705" data-name="Group 12705" transform="translate(-1676 15)">
                                        <circle id="_1_63d33Lwl2ql4Oh6Dj4NOVQ" data-name="1*63d33Lwl2ql4Oh6Dj4NOVQ" cx="15" cy="15" r="15" transform="translate(1696)"/>
                                        </g>
                                        <path id="notifications_black_24dp" d="M8.718,14A1.183,1.183,0,0,0,9.9,12.821H7.538A1.179,1.179,0,0,0,8.718,14Zm3.538-3.538V7.513A3.593,3.593,0,0,0,9.6,3.786v-.4a.885.885,0,1,0-1.769,0v.4A3.583,3.583,0,0,0,5.179,7.513v2.949L4,11.641v.59h9.436v-.59Z" transform="translate(26.282 21)" fill="#fff"/>
                                    </g>
                                </svg>
                            </span>
                        </div>
                        <div>
                            <p class="mb-0">{{$notification->data['title'] ?? ''}}</p>
                            <p class="mb-0 text-info" style="font-size: 10px">{{\Carbon\Carbon::parse($notification->created_at)->diffForHumans()}}</p>
                            <p class="mb-0 d-none">{{$notification->data['data'] ?? ''}}</p>
                        </div>
                        <div class="ms-auto">
                            <button data-notification_id="{{$notification->id}}" type="button" class="btn-close mark-as-read-btn"></button>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <p class="py-4 text-info">There are no notifications.</p>
        @endforelse
    </div>
</div>
{{-- @endif --}}
<script type="module">
document.addEventListener('DOMContentLoaded', function () {
    let count = 0;
    $(document).ready(function() {

        $(document).on('click', '.close_iframe', function(event) {
            $('#iframe-popup').remove();
        });
        function markAsRead(notification_id){
            var count = parseInt($('#stickyNotificationCountContainer span').text());
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: 'POST',
                url: '{{ route('mark-as-read-notification') }}',
                data: {
                    notification_id
                },
                dataType: 'json',
                success: function(response) {
                    console.log('notification_' + notification_id);
                    
                    $('#notification_' + notification_id).hide();
                    count--;
                    $('#stickyNotificationCountContainer span').text(count);
                    if(count == 0){
                        $('.notification-wrap').html('<p class="py-4 text-info">There are no notifications.</p>');
                        $('.acc-no-active').hide();
                    }
                }
            });
        }
        $('.notification-system .read-notification').on('click', function(event) {
            event.preventDefault();  // Prevent default behavior initially
            if($(this).is('[data-popup]')) {
                event.stopPropagation();
                openIframePopup($(this).data('href'));
            }else if($(this).is('[target="_blank"]')) {
                event.stopPropagation();
                window.open($(this).data('href'), '_blank');
            }else{
                window.location.href = $(this).data('href');
            }
            markAsRead($(this).data('notification_id'));
        });

        function openIframePopup(url) {
            var iframe = document.createElement('iframe');
            iframe.src = url;
            // iframe.id = 'iframe-popup';
            var wrapper = document.createElement('div');
            var close_iframe = document.createElement('span');
            close_iframe.classList.add('close_iframe');
            close_iframe.textContent = '×';
            close_iframe.setAttribute('onclick', "$('#iframe-popup').remove();");
            wrapper.id = 'iframe-popup';
            document.body.appendChild(wrapper);
            wrapper.appendChild(iframe);
            wrapper.append(close_iframe);
            iframe.focus();
            iframe.contentWindow.focus();            
        }
        $('.mark-as-read-btn').on('click', function(btn) {
            event.stopPropagation();  // Prevents the link from being followed
            event.preventDefault();   // Prevents default anchor behavior
            let markAsReadBtn = $(this);
            let notificationId = markAsReadBtn.data('notification_id');
            $('#adminNotifications').empty();

            markAsRead(notificationId);
        });

        $('.mark-as-read-btn-client').on('click', function() {
            let form = $(this).closest('.mark-as-read-form-client');
            let clientId = form.find('input[name="clientId"]').val();
            $('#newClient').empty();

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            $.ajax({
                type: 'POST',
                url: '{{ route('mark-as-read-new-clients-notification') }}',
                data: {
                    clientId: clientId
                },
                success: function(response) {
                    $('#newClient').html(response);

                    let count = $('#newClient').length;
                    $('#stickyNotificationCountContainer span').text(count);

                }
            });
        });

        $('#markAllAsRead').on('click', function () {

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $.ajax({
                url: '{{ route('mark-as-read-all') }}',
                type: 'POST',
                dataType: 'json',
                success: function (response) {
                    location.reload()
                }
            });
        });
    });
}, false);

</script>
