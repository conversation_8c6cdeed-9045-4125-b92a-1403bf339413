<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constants;
use App\Http\Controllers\Controller;
use App\Models\Studio;
use Illuminate\Http\JsonResponse;

class StudioController extends Controller
{
    public function getCompanyInfo(Studio $studio): JsonResponse
    {
        $studio['location'] = ($studio->location === Constants::LOCATION_TYPE[0]) ? 'USA' : 'International';
        return response()->json(['success' => true, 'data' => $studio]);
    }
}
