@extends('layouts.app')
@section('content')
    <div class="page-title ttl-dropdown no-border-btm">
        <div class="title-left">
            <div class="d-flex align-items-center">
                <h3>{{ __('Agreements') }}</h3>
            </div>
        </div>
    </div>
    <div class="nr-items">
        <h5 id="agreementsCount"></h5>

        <div class="sortbysearch">
            <div class="zIndex-10000 filter-dropdown">
                <select class="form-select table-filter-select" aria-label="Default select example" id="statusSelect">
                    <option selected value="all">Sort by: {{ __('All') }}</option>
                    <option value="1">Sort by: {{ __('Active') }}</option>
                    <option value="0">Sort by: {{ __('Inactive') }}</option>
                </select>
            </div>
            <div class="ms-auto lg-search h-40px">
                <input class="typeahead form-control" id="agreements-search" type="text" name="lagree-search"
                    placeholder="Search" autocomplete="off">
                <span class="lg-search-ico" onclick="$(this).prev().toggleClass('lg-search-expanded');"><img
                        src="/search.svg" alt="search" class=""></span>
            </div>
        </div>
    </div>
    <div id="agreements-table" class="entity-table"></div>
@endsection
<script type="module">
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = $('#agreements-search');
        const searchIcon = $('.lg-search-ico');
        const sortIcon = $('.sort-icon');
        let searchData = '';
        let orderParam = '';
        let orderType = 'desc';
        const agreementsTable = $('#agreements-table');
        const html = $('html, body');

        @php
            $route = route('studio.agreements.search', ['customer' => $customer->id]);
        @endphp

        function fetchData(url) {
            let perPage = $('.pagination-select').val() ?? 10;
            let selectedValue = $('#statusSelect').val();
            $.ajax({
                url: url,
                data: {
                    status: selectedValue,
                    search_data: searchData,
                    order_param: orderParam,
                    order_type: orderType,
                    per_page: perPage,
                },
                success: function(data) {
                    setTimeout(function() {
                        agreementsTable.html(data);
                        setSortIcon();
                        $('.pagination-select').val(perPage);
                    }, 300);
                },
                error: function() {
                    flasherJS.error('', 'Error occurred while loading data.');
                }
            });
        }

        $('body').on('change', '#statusSelect', function(e) {
            fetchData("{{ $route }}");
        });

        $('body').on('change', '.pagination-select', function(e) {
            fetchData("{{ $route }}");
        });

        $('body').on('click', '.pagination a', function(e) {
            e.preventDefault();
            const url = $(this).attr('href');
            fetchData(url);
        });

        searchInput.on('input', debounce(function() {
            searchData = $(this).val();
            fetchData("{{ $route }}");
        }, 500));

        searchInput.on('click focus', function(e) {
            e.stopPropagation();
        });

        $('body').on('click', '.sortable-list-header', function() {
            const newOrderParam = $(this).data('sort');
            if (orderParam === newOrderParam) {
                // Toggle sorting direction if the same column is clicked
                orderType = orderType === 'asc' ? 'desc' : 'asc';
            } else {
                // Set new sort column and default direction to ascending
                orderParam = newOrderParam;
                orderType = 'asc';
            }
            fetchData("{{ $route }}");
        });

        function debounce(func, wait, immediate) {
            let timeout;
            return function() {
                let context = this,
                    args = arguments;
                let later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                let callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        }

        searchInput.on('click focus', function(e) {
            e.stopPropagation();
        });

        function setSortIcon() {
            sortIcon.removeClass('asc desc'); // Remove existing sort classes
            $(`.sortable-list-header[data-sort="${orderParam}"] .sort-icon`).addClass(
            orderType); // Add active sort class
        }

        searchIcon.click(function(e) {
            e.stopPropagation();
            if (searchData !== '' && !searchInput.get(0).classList.contains("lg-search-expanded")) {
                searchInput.val('');
                searchData = '';
                fetchData("{{ $route }}");
            } else {
                searchInput.focus();
            }
        });

        html.click(function(e) {
            e.stopPropagation();
            searchInput.get(0).classList.remove("lg-search-expanded");
            searchInput.val('');
            searchData = '';
        });

        // Initial load
        fetchData("{{ $route }}");
    })
</script>
