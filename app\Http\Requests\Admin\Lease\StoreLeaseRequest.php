<?php

namespace App\Http\Requests\Admin\Lease;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\ValidationException;

class StoreLeaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        // dd(request());
        return [
            // 'studio_exists'           => 'required|boolean',
            'studio_id'               => 'required|exists:studios,id',
            'company_id'              => 'required|exists:companies,id',
            'machine_id'              => 'required|exists:machines,id',
            'configuration_id'        => 'nullable|integer',
            'machine_price'           => 'required|numeric',
            'monthly_installment'     => 'required|numeric',
            'machine_quantity'        => 'required|integer',
            'duration'                => 'required|integer',
            'condition'               => 'required|string',
            'status'                  => 'required|integer',
            'starting_date'           => 'required|date',
            'deposit_amount'          => 'required|numeric',
            'buy_out'                 => 'required|numeric',
            'deposit_date'            => 'required|date',
            // 'studio.name'             => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.owner_first_name' => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.owner_last_name'  => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.email'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.phone'            => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.address'          => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.city'             => [Rule::requiredIf(is_null(request()->input('studio_id')))],
            // 'studio.state_id'         => [Rule::requiredIf(is_null(request()->input('studio_id'))), 'exists:states,id'],
            // 'studio.zip'              => [Rule::requiredIf(is_null(request()->input('studio_id')))],
        ];
    }

    public function attributes()
    {
        return [
            'studio.name' => 'name',
            'studio.owner_first_name' => 'owner first name',
            'studio.owner_last_name' => 'owner last name',
            'studio.email' => 'email',
            'studio.phone' => 'phone',
            'studio.address' => 'address',
            'studio.city' => 'city',
            'studio.state_id' => 'state',
            'studio.zip' => 'zip',
        ];
    }
    protected function failedValidation(Validator $validator)
    {
        if ($validator->errors()) {
            // dd($validator->errors());
            toastr()->addError('Please check all the fields');
        }
        throw (new ValidationException($validator))
            ->errorBag($this->errorBag)
            ->redirectTo($this->getRedirectUrl());
    }
}
