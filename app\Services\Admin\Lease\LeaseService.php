<?php

namespace App\Services\Admin\Lease;

use App\Helpers\Constants;
use App\Helpers\CurrencyHelper;
use App\Jobs\SendAgreement;
use App\Models\AdminSettings;
use App\Models\Agreement;
use App\Models\Company;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Lease;
use App\Models\LeaseNotes;
use App\Models\Studio;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class LeaseService implements ILeaseService
{
    public function store(array $data, Customer $customer)
    {
        DB::beginTransaction();

        try {
            // $studio_id = $data['studio_id'] ?? null;
            // if (!$data['studio_exists']) {
            //     $studio = Studio::create($data['studio']);
            //     $studio_id = $studio->id;
            // }

            // $data['studio_id'] = $studio_id;
            $data['customer_id'] = $customer->id;

            $settings = AdminSettings::first();
            if ($settings->currency->code !== Currency::USD) {
                $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);

                $data['machine_price'] = round($currencyToDollarRate * $data['machine_price'] * 100);
                $data['monthly_installment'] = round($currencyToDollarRate * $data['monthly_installment'] * 100);

                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                }
            } else {
                $data['machine_price'] = round($data['machine_price'] * 100);
                $data['monthly_installment'] = round($data['monthly_installment'] * 100);
                if (isset($data['deposit_amount'])) {
                    $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                }
            }
            $data['initial_currency_id'] = $settings->currency_id;
            $lease = Lease::create($data);
            if (isset($data['description']) and $data['description'] != '') {
                LeaseNotes::create([
                    'lease_id' => $lease->id,
                    'body' => $data['description']
                ]);
            }
            foreach (Currency::all() as $currency) {
                $lease->conversionRate()->create([
                    'currency_id' => $currency->id,
                    'rate' => CurrencyHelper::calculateRate($currency)
                ]);
            }
            SendAgreement::dispatch(Agreement::LEASE_TYPE, Agreement::LEASE_TEMPLATE_NAMES, $customer, $lease, Company::find($lease->company_id));
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $data, Lease $lease, Customer $customer)
    {
        DB::beginTransaction();

        try {
            if (!empty($data['studio'])) {
                $studioData = $data['studio'] ?? null;
                if ($studioData) {
                    $lease->studio->update($studioData);
                }
            } else {
                $data['customer_id'] = $customer->id;
                $settings = AdminSettings::first();
                if ($settings->currency->code !== Currency::USD) {
                    $currencyToDollarRate = CurrencyHelper::calculateRate($settings->currency);

                    $data['machine_price'] = round($currencyToDollarRate * $data['machine_price'] * 100);
                    $data['monthly_installment'] = round($currencyToDollarRate * $data['monthly_installment'] * 100);

                    if (isset($data['deposit_amount'])) {
                        $data['deposit_amount'] = round($currencyToDollarRate * $data['deposit_amount'] * 100);
                    }
                } else {
                    $data['machine_price'] = round($data['machine_price'] * 100);
                    $data['monthly_installment'] = round($data['monthly_installment'] * 100);
                    if (isset($data['deposit_amount'])) {
                        $data['deposit_amount'] = round($data['deposit_amount'] * 100);
                    }
                }

                $data['initial_currency_id'] = $settings->currency_id;

                $lease->update($data);

                if (isset($data['description']) and $data['description'] != '') {
                    LeaseNotes::firstOrCreate([
                        'lease_id' => $lease->id,
                        'body' => $data['description']
                    ]);
                }

                $lease->conversionRate()->delete();
                foreach (Currency::all() as $currency) {
                    $lease->conversionRate()->create([
                        'currency_id' => $currency->id,
                        'rate' => CurrencyHelper::calculateRate($currency)
                    ]);
                }
                SendAgreement::dispatch(Agreement::LEASE_TYPE, Agreement::LEASE_TEMPLATE_NAMES, $customer, $lease, Company::find($lease->company_id));
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function search(string $searchData, string $orderParam, string $orderType, int $perPage, Customer $customer, string $status): LengthAwarePaginator
    {
        $query = Lease::with('studio')->where('customer_id', $customer->id)->withCount('leaseNotes');
        $perPage = ($perPage != 0) ? $perPage : $query->count();

        $query->when($status !== 'all', function ($query) use ($status) {
            $query->where('is_active', $status);
        });

        $query->when($searchData !== '', function ($query) use ($searchData) {
            $query->whereHas('studio', function ($query) use ($searchData) {
                $query->where('name', 'LIKE', '%' . $searchData . '%');
                $query->orWhere('zip', 'LIKE', '%' . $searchData . '%');
            });
        });

        $query->orderBy($orderParam, $orderType); // Uncommenting this line to apply ordering

        return $query->paginate($perPage);
    }

    public function delete(Lease $lease)
    {
        $payments = $lease->payments;
        foreach ($payments as $payment) {
            $payment->invoice()->delete();
            $payment->delete();
        }
        $lease->delete();
    }
}
