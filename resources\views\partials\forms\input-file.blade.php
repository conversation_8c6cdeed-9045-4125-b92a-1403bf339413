<div class="form-input">
    
    <label for="{{$field_name}}">
        <img id="{{$field_name}}-preview" class="" alt="Upload {{$field_name}}" src="{{ ($field_value)? URL::asset('/storage/' . $field_value) : URL::asset('/img_placeholder.svg') }}">
    </label>
    <input type="file" id="{{$field_name}}" name="{{$field_name}}" accept="image/*" onchange="showPreview(event);">
    <input type="hidden" id="{{$field_name}}_deleted" name="{{$field_name}}_deleted" value="{{false}}">

    @if($errors->has($field_name))
        <span class="error-msg text-danger">*{{ $errors->first($field_name) }}</span>
    @endif

</div>
<script type="text/javascript">
    function showPreview(event){
        if(event.target.files.length > 0){
          let src = URL.createObjectURL(event.target.files[0]);
          var inputId = event.target.id;
          let preview = document.getElementById(inputId + "-preview");
          preview.src = src;
          preview.style.display = "block";
          $("#{{$field_name . '_deleted'}}").val(false);
          $(".imgRemove").show();
        }
    }
    function myImgRemove(number) {
        event.preventDefault();
        document.getElementById("{{$field_name}}-preview").src = '{{ URL::asset('/img_placeholder.svg') }}';
        $("#{{$field_name}}").val(null);
        $("#{{$field_name . '_deleted'}}").val(true);
    }
    function multipleMyImages(number, type) {
        event.preventDefault();
        document.getElementById(type+"-preview").src = '{{ URL::asset('/img_placeholder.svg') }}';
        $('#' + type).val(null);
        $('#' + type + '_deleted').val(true);
        {{--console.log($('#' + type + '_deleted').val(true));--}}
        {{--console.log(("#{{$field_name . '_deleted'}}").val(true));--}}

    }
</script>
