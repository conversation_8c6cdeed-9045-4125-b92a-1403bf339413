<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket>
 */
class TicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $userIds = User::pluck('id')->toArray();
        $customer = fake()->randomElement($userIds);
        $creator = fake()->randomElement($userIds);
        $assignee = fake()->randomElement($userIds);

        return [
            'customer_id' => $customer,
            'subject' => fake()->sentence,
            'status' => fake()->randomElement(['open', 'closed', 'archived']),
            'created_by' => $creator,
            'assigned_to' => $assignee,
        ];
    }
}
